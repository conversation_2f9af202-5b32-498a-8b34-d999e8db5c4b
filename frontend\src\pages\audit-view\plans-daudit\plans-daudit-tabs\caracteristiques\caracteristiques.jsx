import { useEffect, useState, useRef } from "react";
import { useOutletContext, useParams, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { Tag, Calendar, Save, FileText, Loader2 } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { DateInput } from "@/components/ui/date-input";
import { toast } from "sonner";
import { updateAuditPlanById } from '@/store/slices/auditPlanSlice';

function CaracteristiquesTab() {
  const { auditPlan } = useOutletContext();
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const abortControllerRef = useRef(null);
  
  const { loading, error } = useSelector((state) => ({
    loading: state.auditPlans?.isUpdating,
    error: state.auditPlans?.error
  }));

  const [formData, setFormData] = useState({
    name: "",
    datedebut: "",
    datefin: "",
    description: "",
    nom: '',
    statut: '',
    type: '',
    priorite: '',
    responsable: '',
    entite: '',
    site: '',
    departement: '',
    service: '',
    processus: '',
    activite: '',
    domaine: '',
    sousdomaine: '',
    risque: '',
    impact: '',
    frequence: '',
    niveau: '',
    commentaire: ''
  });

  // Initialize form data when auditPlan is loaded
  useEffect(() => {
    if (auditPlan) {
      setFormData({
        name: auditPlan.name || "",
        datedebut: auditPlan.datedebut || "",
        datefin: auditPlan.datefin || "",
        description: auditPlan.description || "",
        nom: auditPlan.nom || '',
        statut: auditPlan.statut || '',
        type: auditPlan.type || '',
        priorite: auditPlan.priorite || '',
        responsable: auditPlan.responsable || '',
        entite: auditPlan.entite || '',
        site: auditPlan.site || '',
        departement: auditPlan.departement || '',
        service: auditPlan.service || '',
        processus: auditPlan.processus || '',
        activite: auditPlan.activite || '',
        domaine: auditPlan.domaine || '',
        sousdomaine: auditPlan.sousdomaine || '',
        risque: auditPlan.risque || '',
        impact: auditPlan.impact || '',
        frequence: auditPlan.frequence || '',
        niveau: auditPlan.niveau || '',
        commentaire: auditPlan.commentaire || ''
      });
    }
  }, [auditPlan]);

  // Handle error state
  useEffect(() => {
    if (error) {
      toast.error(error);
    }
  }, [error]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSave = async () => {
    if (!formData.name.trim()) {
      toast.error("Le nom du plan d'audit est requis");
      return;
    }

    if (!formData.datedebut) {
      toast.error("La date de début est requise");
      return;
    }

    if (formData.datefin && new Date(formData.datefin) <= new Date(formData.datedebut)) {
      toast.error("La date de fin doit être après la date de début");
      return;
    }

    // Cancel any existing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new AbortController
    abortControllerRef.current = new AbortController();

    try {
      await dispatch(updateAuditPlanById({
        id,
        data: {
          ...formData,
          name: formData.name.trim(),
          description: formData.description.trim()
        },
        signal: abortControllerRef.current.signal
      })).unwrap();
      
      toast.success("Plan d'audit mis à jour avec succès");
    } catch (error) {
      if (error.name !== 'CanceledError') {
        toast.error(error.message || "Erreur lors de la mise à jour du plan d'audit");
      }
    }
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  if (!auditPlan) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#F62D51] mx-auto mb-4"></div>
          <p className="text-gray-500">Chargement des caractéristiques du plan d'audit...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Fixed header */}
      <div className="sticky top-0 z-10 bg-white border-b border-gray-200 shadow-sm">
        <div className="px-4 py-3">
          <h3 className="text-lg font-medium text-gray-800">Caractéristiques du Plan d'Audit</h3>
        </div>
      </div>

      {/* Scrollable content */}
      <div className="flex-1 overflow-y-auto p-6">
        <div className="space-y-6 max-w-4xl mx-auto">
          {/* Name field */}
          <div className="space-y-2">
            <Label htmlFor="name" className="text-sm font-medium text-gray-700 flex items-center gap-2">
              <Tag className="h-4 w-4 text-purple-500" />
              Nom <span className="text-red-500">*</span>
            </Label>
            <Input
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              placeholder="Entrez le nom du plan d'audit"
              className="w-full"
              required
              disabled={loading}
            />
          </div>

          {/* Date fields */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="datedebut" className="text-sm font-medium text-gray-700 flex items-center gap-2">
                <Calendar className="h-4 w-4 text-blue-500" />
                Date début <span className="text-red-500">*</span>
              </Label>
              <DateInput
                id="datedebut"
                name="datedebut"
                value={formData.datedebut}
                onChange={handleInputChange}
                className="w-full"
                required
                disabled={loading}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="datefin" className="text-sm font-medium text-gray-700 flex items-center gap-2">
                <Calendar className="h-4 w-4 text-red-500" />
                Date fin
              </Label>
              <DateInput
                id="datefin"
                name="datefin"
                value={formData.datefin}
                onChange={handleInputChange}
                className="w-full"
                disabled={loading}
              />
            </div>
          </div>

          {/* Description field */}
          <div className="space-y-2">
            <Label htmlFor="description" className="text-sm font-medium text-gray-700 flex items-center gap-2">
              <FileText className="h-4 w-4 text-indigo-500" />
              Description
            </Label>
            <Textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              placeholder="Entrez la description du plan d'audit"
              className="w-full"
              rows={4}
              disabled={loading}
            />
          </div>

          {/* Save button */}
          <div className="flex justify-end pt-4">
            <Button
              className="bg-[#F62D51] hover:bg-[#F62D51]/90 text-white"
              onClick={handleSave}
              disabled={loading}
            >
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Enregistrement...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Enregistrer
                </>
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default CaracteristiquesTab;
