import { useState, useEffect } from "react";
import { useOutletContext } from "react-router-dom";
//import { useDispatch } from "react-redux";
import { Button } from "../../../../components/ui/button";
import { Input } from "../../../../components/ui/input";
import { Textarea } from "../../../../components/ui/textarea";
import { Checkbox } from "../../../../components/ui/checkbox";
import { Loader2, Plus, Link as LinkIcon, Shield, Activity, Check, X } from "lucide-react";
import { toast } from "sonner";
import axios from "axios";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from "../../../../components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../../../components/ui/select";
import { Label } from "../../../../components/ui/label";
import { getApiBaseUrl } from "@/utils/api-config";
import { useTranslation } from 'react-i18next';

function RisksMitigation() {
  const { t } = useTranslation();
  const { risk, refreshRisk, referenceData } = useOutletContext();
  //const dispatch = useDispatch();
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isLinkModalOpen, setIsLinkModalOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [mitigation, setMitigation] = useState({
    appetite: "",
    acceptance: false,
    avoidance: false,
    insurance: false,
    reduction: false,
    controlID: "",
  });
  const [linkedControl, setLinkedControl] = useState(null);
  const [newControl, setNewControl] = useState({
    name: "",
    controlKey: "1",
    organizationalLevel: ""
  });
  const API_BASE_URL = getApiBaseUrl();
  // Get controls from reference data
  const controls = referenceData?.controls || [];

  // Helper function for appetite labels
  const getAppetiteLabel = (value) => {
    if (!value) return { label: '-', color: 'bg-gray-100 text-gray-500' };
    const appetiteLabels = {
      '1': { label: t('admin.risks.management.mitigation.appetite.very_low', 'Very Low'), color: 'bg-blue-100 text-blue-800' },
      '2': { label: t('admin.risks.management.mitigation.appetite.low', 'Low'), color: 'bg-green-100 text-green-800' },
      '3': { label: t('admin.risks.management.mitigation.appetite.medium', 'Medium'), color: 'bg-yellow-100 text-yellow-800' },
      '4': { label: t('admin.risks.management.mitigation.appetite.high', 'High'), color: 'bg-orange-100 text-orange-800' },
      '5': { label: t('admin.risks.management.mitigation.appetite.very_high', 'Very High'), color: 'bg-red-100 text-red-800' }
    };
    return appetiteLabels[value.toString()] || { label: value, color: 'bg-gray-100 text-gray-500' };
  };

  // Helper function for residual risk labels
  const getResidualRiskLabel = (value) => {
    if (!value && value !== 0) return { label: t('admin.risks.management.mitigation.residual_risk.na', 'N/A'), color: 'bg-gray-100 text-gray-500' };

    // Determine risk level based on value
    let label, color;
    if (value >= 20) {
      label = t('admin.risks.management.mitigation.residual_risk.critical', 'Critical');
      color = 'bg-red-100 text-red-800';
    } else if (value >= 15) {
      label = t('admin.risks.management.mitigation.residual_risk.high', 'High');
      color = 'bg-orange-100 text-orange-800';
    } else if (value >= 8) {
      label = t('admin.risks.management.mitigation.residual_risk.medium', 'Medium');
      color = 'bg-yellow-100 text-yellow-800';
    } else if (value >= 4) {
      label = t('admin.risks.management.mitigation.residual_risk.low', 'Low');
      color = 'bg-green-100 text-green-800';
    } else {
      label = t('admin.risks.management.mitigation.residual_risk.very_low', 'Very Low');
      color = 'bg-blue-100 text-blue-800';
    }

    return { label, color, value };
  };

  // Initialize form data when risk is loaded
  useEffect(() => {
    if (risk) {
      setMitigation({
        appetite: risk.appetite ? risk.appetite.toString() : "",
        acceptance: Boolean(risk.acceptance),
        avoidance: Boolean(risk.avoidance),
        insurance: Boolean(risk.insurance),
        reduction: Boolean(risk.reduction),
        controlID: risk.controlID || "",
      });

      // Find linked control if any
      if (risk.controlID) {
        const control = controls.find(c => c.controlID === risk.controlID);
        setLinkedControl(control || null);
      } else {
        setLinkedControl(null);
      }
    }
  }, [risk, controls]);

  // Filter controls based on search query
  const filteredControls = controls?.filter(control =>
    control.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (control.description && control.description.toLowerCase().includes(searchQuery.toLowerCase()))
  ) || [];

  // Handle mitigation form input changes
  const handleMitigationChange = (field, value) => {
    setMitigation(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle create control form input changes
  const handleCreateInputChange = (e) => {
    const { name, value } = e.target;
    setNewControl(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle select input changes for control form
  const handleSelectChange = (name, value) => {
    setNewControl(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Save mitigation strategy changes
  const handleSaveStrategy = async () => {
    setIsSubmitting(true);
    try {
      const response = await axios.put(
        `${API_BASE_URL}/risk/${risk.riskID}`,
        {
          ...risk,
          appetite: mitigation.appetite ? parseInt(mitigation.appetite) : null,
          acceptance: mitigation.acceptance,
          avoidance: mitigation.avoidance,
          insurance: mitigation.insurance,
          reduction: mitigation.reduction,
        },
        {
          withCredentials: true,
          headers: { "Content-Type": "application/json" },
        }
      );

      if (response.data.success) {
        toast.success(t('admin.risks.management.mitigation.strategy_updated', "Mitigation strategy updated successfully"));
        refreshRisk();
      }
    } catch (error) {
      toast.error(error.response?.data?.message || t('admin.risks.management.mitigation.strategy_update_failed', "Failed to update mitigation strategy"));
      console.error("Error updating mitigation strategy:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Create new control
  const handleCreateControl = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Generate a unique ID for the control
      const controlData = {
        ...newControl,
        controlID: `CTL_${Date.now()}`,
        effectiveness: parseInt(newControl.effectiveness),
      };

      // Create the control
      const response = await axios.post(
        `${API_BASE_URL}/controls`,
        controlData,
        {
          withCredentials: true,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.data.success) {
        // Link the control to the risk
        await linkControlToRisk(response.data.data.controlID);

        // Show success message
        toast.success(t('admin.risks.management.mitigation.control_created_linked', "Control created and linked successfully"));

        // Reset form and close modal
        setNewControl({
          name: "",
          controlKey: "1",
          organizationalLevel: "",
          testingFrequency: ""
        });
        setIsCreateModalOpen(false);

        // Refresh data
        refreshRisk();
      }
    } catch (error) {
      console.error('Error creating control:', error);
      toast.error(error?.response?.data?.message || error?.message || t('admin.risks.management.mitigation.control_create_failed', "Failed to create control"));
    } finally {
      setIsSubmitting(false);
    }
  };

  // Link control to risk
  const handleLinkControl = async (controlID) => {
    setIsSubmitting(true);
    try {
      await linkControlToRisk(controlID);
      setIsLinkModalOpen(false);
      toast.success(t('admin.risks.management.mitigation.control_linked', "Control linked successfully"));
    } catch (error) {
      console.error('Error linking control:', error);
      toast.error(error?.response?.data?.message || error?.message || t('admin.risks.management.mitigation.control_link_failed', "Failed to link control"));
    } finally {
      setIsSubmitting(false);
    }
  };

  // Helper function to link control to risk
  const linkControlToRisk = async (controlID) => {
    const response = await axios.put(
      `${API_BASE_URL}/risk/${risk.riskID}`,
      { ...risk, controlID: controlID },
      {
        withCredentials: true,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );

    if (response.data.success) {
      // Refresh risk data
      refreshRisk();
      return true;
    } else {
      throw new Error(response.data.message || "Failed to link control");
    }
  };

  // Unlink control from risk
  const handleUnlinkControl = async () => {
    if (!window.confirm(t('admin.risks.management.mitigation.confirm_unlink', "Are you sure you want to unlink this control?"))) {
      return;
    }

    try {
      const response = await axios.put(
        `${API_BASE_URL}/risk/${risk.riskID}`,
        { ...risk, controlID: null },
        {
          withCredentials: true,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.data.success) {
        toast.success(t('admin.risks.management.mitigation.control_unlinked', "Control unlinked successfully"));
        refreshRisk();
        setLinkedControl(null);
      }
    } catch (error) {
      console.error('Error unlinking control:', error);
      toast.error(error?.response?.data?.message || error?.message || t('admin.risks.management.mitigation.control_unlink_failed', "Failed to unlink control"));
    }
  };

  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold mb-6">{t('admin.risks.management.mitigation.title', "Mitigation")}</h2>

      {/* Strategy Section */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
        <div className="bg-gray-50 px-4 py-3 border-b">
          <h3 className="text-lg font-medium text-gray-800 flex items-center">
            <Activity className="h-5 w-5 mr-2 text-blue-500" />
            {t('admin.risks.management.mitigation.strategy_title', "Strategy")}
          </h3>
        </div>
        <div className="p-6 space-y-6">
          {/* Risk Assessment Row */}
          <div className="grid grid-cols-2 gap-6">
            {/* Residual Risk */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-[#242A33]">{t('admin.risks.management.mitigation.residual_risk_label', "Residual Risk")}</label>
              <div className="flex items-center space-x-2">
                {risk && typeof risk.residualRisk !== 'undefined' ? (
                  <div className={`${getResidualRiskLabel(risk.residualRisk).color} px-3 py-1.5 rounded-md text-sm font-medium`}>
                    {getResidualRiskLabel(risk.residualRisk).label}
                  </div>
                ) : (
                  <div className="bg-gray-100 text-gray-500 px-3 py-1.5 rounded-md text-sm font-medium">
                    {t('admin.risks.management.mitigation.residual_risk.na', 'N/A')}
                  </div>
                )}
              </div>
            </div>

            {/* Appetite */}
            <div className="space-y-4">
              <label className="text-sm font-medium text-[#242A33]">{t('admin.risks.management.mitigation.appetite_label', "Appetite")}</label>
              <Select
                value={mitigation.appetite}
                onValueChange={(value) => handleMitigationChange("appetite", value)}
              >
                <SelectTrigger className="w-full md:w-1/3">
                  <SelectValue placeholder={t('admin.risks.management.mitigation.select_appetite', "Select appetite level")}>
                    {mitigation.appetite ? getAppetiteLabel(mitigation.appetite).label : t('admin.risks.management.mitigation.select_appetite', "Select appetite level")}
                  </SelectValue>
                </SelectTrigger>
                <SelectContent>
                  {[1, 2, 3, 4, 5].map((level) => (
                    <SelectItem key={level} value={level.toString()}>
                      {getAppetiteLabel(level).label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Horizontal separator */}
          <div className="border-t border-gray-200 my-4"></div>

          {/* Treatment Options */}
          <div>
            <label className="text-sm font-medium text-[#242A33] mb-3 block">{t('admin.risks.management.mitigation.treatment_options', "Treatment Options")}</label>
            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  checked={mitigation.acceptance}
                  onCheckedChange={(checked) => handleMitigationChange("acceptance", checked)}
                />
                <label className="text-sm text-[#555F6D]">{t('admin.risks.management.mitigation.treatment.acceptance', "Acceptance")}</label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  checked={mitigation.avoidance}
                  onCheckedChange={(checked) => handleMitigationChange("avoidance", checked)}
                />
                <label className="text-sm text-[#555F6D]">{t('admin.risks.management.mitigation.treatment.avoidance', "Avoidance")}</label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  checked={mitigation.insurance}
                  onCheckedChange={(checked) => handleMitigationChange("insurance", checked)}
                />
                <label className="text-sm text-[#555F6D]">{t('admin.risks.management.mitigation.treatment.insurance', "Insurance")}</label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  checked={mitigation.reduction}
                  onCheckedChange={(checked) => handleMitigationChange("reduction", checked)}
                />
                <label className="text-sm text-[#555F6D]">{t('admin.risks.management.mitigation.treatment.reduction', "Reduction")}</label>
              </div>
            </div>
          </div>

          {/* Save Button */}
          <div className="flex justify-end mt-4">
            <Button
              onClick={handleSaveStrategy}
              className="bg-[#F62D51] hover:bg-red-700"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {t('admin.risks.management.mitigation.saving', "Saving...")}
                </>
              ) : (
                t('admin.risks.management.mitigation.save_strategy', "Save Strategy")
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Controls Section */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
        <div className="bg-gray-50 px-4 py-3 border-b">
          <h3 className="text-lg font-medium text-gray-800 flex items-center">
            <Shield className="h-5 w-5 mr-2 text-teal-500" />
            {t('admin.risks.management.mitigation.controls_title', "Controls")}
          </h3>
        </div>
        <div className="p-6">
          {linkedControl ? (
            <div className="space-y-4">
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="text-lg font-semibold">{linkedControl.name}</h3>
                  <p className="text-sm text-gray-600 mt-1">{linkedControl.description}</p>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleUnlinkControl}
                  className="text-red-500 hover:text-red-700"
                >
                  <X className="h-4 w-4 mr-1" />
                  {t('admin.risks.management.mitigation.unlink', "Unlink")}
                </Button>
              </div>

              {/* Control details */}
              <div className="grid grid-cols-2 gap-4 bg-gray-50 p-4 rounded-lg mt-2">
                <div>
                  <p className="text-sm font-medium text-gray-500">{t('admin.risks.management.mitigation.control_key', "Control Key")}</p>
                  <p className="text-base">{linkedControl.controlKey === "1" ? t('admin.risks.management.mitigation.yes', "Yes") : t('admin.risks.management.mitigation.no', "No")}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">{t('admin.risks.management.mitigation.organizational_level', "Organizational Level")}</p>
                  <p className="text-base">{linkedControl.organizationalLevel || t('admin.risks.management.mitigation.na', "N/A")}</p>
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-8 text-center">
              <div className="flex flex-col items-center">
                <Shield className="h-12 w-12 text-gray-300 mb-2" />
                <p className="text-gray-500">{t('admin.risks.management.mitigation.no_control_linked', "No control linked to this risk.")}</p>
                <p className="text-gray-400 text-sm">{t('admin.risks.management.mitigation.create_or_link', "Create a new control or link an existing one.")}</p>
              </div>
              <div className="flex justify-center gap-4">
                <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
                  <DialogTrigger asChild>
                    <Button className="bg-[#F62D51]/90 hover:bg-[#F62D51] text-white flex items-center px-4">
                      <Plus className="h-4 w-4 mr-2" />
                      {t('admin.risks.management.mitigation.create', "Create")}
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-md">
                    <DialogHeader>
                      <DialogTitle>{t('admin.risks.management.mitigation.create_modal.title', "Create New Control")}</DialogTitle>
                      <DialogDescription>
                        {t('admin.risks.management.mitigation.create_modal.description', "Fill in the details below to create a new control.")}
                      </DialogDescription>
                    </DialogHeader>
                    <form onSubmit={handleCreateControl} className="space-y-4 mt-4">
                      <div className="space-y-2">
                        <Label htmlFor="name">{t('admin.risks.management.mitigation.create_modal.name_label', "Name *")}</Label>
                        <Input
                          id="name"
                          name="name"
                          value={newControl.name}
                          onChange={handleCreateInputChange}
                          placeholder={t('admin.risks.management.mitigation.create_modal.name_placeholder', "Enter control name")}
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="controlKey">{t('admin.risks.management.mitigation.create_modal.control_key_label', "Control Key")}</Label>
                        <Select
                          value={newControl.controlKey}
                          onValueChange={(value) => handleSelectChange("controlKey", value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder={t('admin.risks.management.mitigation.create_modal.select_control_key', "Select control key")} />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="1">{t('admin.risks.management.mitigation.yes', "Yes")}</SelectItem>
                            <SelectItem value="0">{t('admin.risks.management.mitigation.no', "No")}</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <DialogFooter className="mt-6">
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => setIsCreateModalOpen(false)}
                        >
                          {t('admin.risks.management.mitigation.create_modal.cancel', "Cancel")}
                        </Button>
                        <Button
                          type="submit"
                          className="bg-[#F62D51] hover:bg-red-700 ml-2"
                          disabled={isSubmitting}
                        >
                          {isSubmitting ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              {t('admin.risks.management.mitigation.create_modal.creating', "Creating...")}
                            </>
                          ) : (
                            t('admin.risks.management.mitigation.create_modal.create_control', "Create Control")
                          )}
                        </Button>
                      </DialogFooter>
                    </form>
                  </DialogContent>
                </Dialog>

                <Dialog open={isLinkModalOpen} onOpenChange={setIsLinkModalOpen}>
                  <DialogTrigger asChild>
                    <Button variant="outline" className="flex items-center px-4">
                      <LinkIcon className="h-4 w-4 mr-2" />
                      {t('admin.risks.management.mitigation.link', "Link")}
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-3xl max-h-[80vh] overflow-hidden flex flex-col">
                    <DialogHeader>
                      <DialogTitle>{t('admin.risks.management.mitigation.link_modal.title', "Link Existing Control")}</DialogTitle>
                      <DialogDescription>
                        {t('admin.risks.management.mitigation.link_modal.description', "Select a control to link to this risk.")}
                      </DialogDescription>
                    </DialogHeader>
                    <div className="my-4">
                      <Input
                        placeholder={t('admin.risks.management.mitigation.link_modal.search_placeholder', "Search controls...")}
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="mb-4"
                      />

                      <div className="overflow-y-auto max-h-[50vh] border rounded-md">
                        {filteredControls.length > 0 ? (
                          <div className="divide-y">
                            {filteredControls.map((control) => (
                              <div
                                key={control.controlID}
                                className="p-4 hover:bg-gray-50 cursor-pointer flex justify-between items-center"
                                onClick={() => handleLinkControl(control.controlID)}
                              >
                                <div>
                                  <h4 className="font-medium">{control.name}</h4>
                                  <p className="text-sm text-gray-600 mt-1 line-clamp-2">{control.description}</p>
                                  <div className="flex flex-wrap gap-2 mt-1">
                                    <span className="text-xs bg-gray-100 px-2 py-1 rounded">
                                      {t('admin.risks.management.mitigation.control_key', "Control Key")}: {control.controlKey === "1" ? t('admin.risks.management.mitigation.yes', "Yes") : t('admin.risks.management.mitigation.no', "No")}
                                    </span>
                                    <span className="text-xs bg-gray-100 px-2 py-1 rounded">
                                      {t('admin.risks.management.mitigation.org_level_short', "Org Level")}: {control.organizationalLevel || t('admin.risks.management.mitigation.na', "N/A")}
                                    </span>
                                  </div>
                                </div>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  className="text-blue-500 hover:text-blue-700"
                                >
                                  <LinkIcon className="h-4 w-4" />
                                </Button>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <div className="p-4 text-center text-gray-500">
                            {t('admin.risks.management.mitigation.link_modal.no_controls_found', "No controls found. Try a different search or create a new control.")}
                          </div>
                        )}
                      </div>
                    </div>
                    <DialogFooter>
                      <Button
                        variant="outline"
                        onClick={() => setIsLinkModalOpen(false)}
                      >
                        {t('admin.risks.management.mitigation.link_modal.cancel', "Cancel")}
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default RisksMitigation;
