import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate, Outlet, useLocation } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import {
  Loader2,
  <PERSON>lipboard<PERSON><PERSON>ck,
  FileText,
  Puzzle,
  ListChecks,
  Bar<PERSON>hart,
  Activity,
  GitBranch,
  User
} from "lucide-react";
import { getActionPlanById, reset } from "@/store/slices/actionPlanSlice";
import DetailHeader from "@/components/ui/detail-header";
import TabBar from "@/components/ui/tabs/tab-bar";
import TabContent from "@/components/ui/tabs/tab-content";
import { useTranslation } from "react-i18next";

function EditActionPlan() {
  const { id } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const { actionPlan, isLoading, isError, message } = useSelector((state) => state.actionPlan);

  // Get the current tab from the URL
  const getCurrentTab = () => {
    const path = location.pathname;
    if (path.includes("/actions")) return "actions";
    if (path.includes("/features")) return "features";
    if (path.includes("/progress")) return "progress";
    if (path.includes("/activity")) return "activity";
    if (path.includes("/workflow")) return "workflow";
    return "overview";
  };

  const [activeTab, setActiveTab] = useState(getCurrentTab());

  // Update active tab when location changes
  useEffect(() => {
    setActiveTab(getCurrentTab());
  }, [location]);

  // Fetch action plan on component mount and when actionPlan in the store changes
  useEffect(() => {
    if (id) {
      dispatch(getActionPlanById(id));
    }

    // Reset state when component unmounts
    return () => {
      dispatch(reset());
    };
  }, [dispatch, id]);

  // We don't need to re-fetch the action plan after an update
  // because we're already updating the actionPlan in the Redux store

  // Handle go back
  const handleGoBack = () => {
    navigate("/admin/data/action-plans");
  };

  // Navigate to tab
  const navigateToTab = (tab) => {
    switch (tab) {
      case "overview":
        navigate(`/admin/data/action-plans/${id}`);
        break;
      case "features":
        navigate(`/admin/data/action-plans/${id}/features`);
        break;
      case "actions":
        navigate(`/admin/data/action-plans/${id}/actions`);
        break;
      case "progress":
        navigate(`/admin/data/action-plans/${id}/progress`);
        break;
      case "activity":
        navigate(`/admin/data/action-plans/${id}/activity`);
        break;
      case "workflow":
        navigate(`/admin/data/action-plans/${id}/workflow`);
        break;
      default:
        navigate(`/admin/data/action-plans/${id}`);
    }
  };

  // Define tabs
  const tabs = [
    { id: "overview", label: t('admin.action_plans.tabs.overview', 'Overview'), icon: <FileText className="h-4 w-4" /> },
    { id: "features", label: t('admin.action_plans.tabs.features', 'Features'), icon: <Puzzle className="h-4 w-4" /> },
    { id: "actions", label: t('admin.action_plans.tabs.actions', 'Actions'), icon: <ListChecks className="h-4 w-4" /> },
    { id: "progress", label: t('admin.action_plans.tabs.progress', 'Progress Report'), icon: <BarChart className="h-4 w-4" /> },
    { id: "activity", label: t('admin.action_plans.tabs.activity', 'Activity'), icon: <Activity className="h-4 w-4" /> },
    { id: "workflow", label: t('admin.action_plans.tabs.workflow', 'Workflow'), icon: <GitBranch className="h-4 w-4" /> }
  ];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="p-6 max-w-[1200px] mx-auto">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          {t('admin.action_plans.error', 'Error: {{message}}', { message })}
        </div>
      </div>
    );
  }

  if (!actionPlan) {
    return (
      <div className="p-6 max-w-[1200px] mx-auto">
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded">
          {t('admin.action_plans.not_found', 'Action plan not found')}
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-[1200px] mx-auto">
      {/* Header */}
      <DetailHeader
        title={actionPlan.name}
        icon={<ClipboardCheck className="h-6 w-6 text-[#F62D51]" />}
        badges={[
          {
            label: actionPlan.priority || 'No Priority',
            color: `
              ${actionPlan.priority === 'High' ? 'bg-red-100 text-red-800' :
              actionPlan.priority === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
              actionPlan.priority === 'Low' ? 'bg-green-100 text-green-800' :
              'bg-blue-100 text-blue-800'}
            `
          }
        ]}
        metadata={[
          actionPlan.category || 'No Category',
          actionPlan.nature || 'No Nature',
          actionPlan.approver ?
            <span className="flex items-center">
              <User className="h-4 w-4 mr-1" />
              Approver: {actionPlan.approver.username}
            </span> :
            null,
          actionPlan.assignee ?
            <span className="flex items-center">
              <User className="h-4 w-4 mr-1" />
              Assignee: {actionPlan.assignee.username}
            </span> :
            null
        ].filter(Boolean)}
        actions={[]}
        onBack={handleGoBack}
        backLabel={t('admin.action_plans.back_to_list', 'Back to Action Plans')}
      />

      {/* Tabs */}
      <TabBar
        activeTab={activeTab}
        tabs={tabs}
        onTabChange={navigateToTab}
        className="mb-6"
      />

      {/* Tab Content */}
      <TabContent>
        <Outlet context={{ actionPlan }} />
      </TabContent>
    </div>
  );
}

export default EditActionPlan;
