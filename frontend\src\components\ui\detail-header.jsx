import { useState } from "react";
import { <PERSON><PERSON>, DialogContent, <PERSON>alogDescription, <PERSON><PERSON><PERSON>ooter, <PERSON>alog<PERSON>eader, <PERSON>alog<PERSON><PERSON>le, DialogTrigger, DialogClose } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>Left, Trash2 } from "lucide-react";
import { Badge } from "@/components/ui/badge";

/**
 * DetailHeader component for displaying entity details with badges and actions
 *
 * @param {Object} props
 * @param {string} props.title - The title to display
 * @param {React.ReactNode} props.icon - Icon to display next to the title
 * @param {Array} props.badges - Array of badge objects with { label, variant, color }
 * @param {Array} props.metadata - Array of metadata strings to display (separated by |)
 * @param {Array} props.actions - Array of action button objects with { label, icon, onClick, variant }
 * @param {Function} props.onBack - Function to call when back button is clicked
 * @param {string} props.backLabel - Label for the back button
 * @param {string} props.className - Additional classes for the header
 * @param {boolean} props.showDeleteButton - Whether to show the delete button (based on user permissions)
 * @param {Function} props.onDelete - Function to call when delete is confirmed
 * @param {React.ReactNode} props.breadcrumb - Breadcrumb component to display above the title
 */
function DetailHeader({
  title,
  icon,
  badges = [],
  metadata = [],
  actions = [],
  onBack,
  backLabel = "Back",
  className = "",
  showDeleteButton = false,
  onDelete,
  breadcrumb
}) {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const handleDelete = () => {
    if (onDelete) {
      onDelete();
    }
    setIsDeleteDialogOpen(false);
  };

  return (
    <div className={`${className}`}>
      {/* Back button */}
      {onBack && (
        <div className="mb-4">
          <Button
            variant="outline"
            onClick={onBack}
            className="flex items-center text-gray-600 hover:text-gray-900 bg-white"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            {backLabel}
          </Button>
        </div>
      )}

      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
          <div>
            {breadcrumb && (
              <div className="mb-3 pb-2 border-b border-gray-100">
                {breadcrumb}
              </div>
            )}

            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              {icon && <span className="mr-2">{icon}</span>}
              {title}
            </h1>

            {(badges.length > 0 || metadata.length > 0) && (
              <div className="flex flex-wrap items-center gap-2 mt-2">
                {badges.map((badge, index) => (
                  <Badge
                    key={index}
                    variant={badge.variant}
                    className={badge.color}
                  >
                    {badge.label}
                  </Badge>
                ))}

                {badges.length > 0 && metadata.length > 0 && (
                  <span className="text-gray-500">|</span>
                )}

                {metadata.map((item, index) => (
                  <div key={index} className="flex items-center">
                    <span className="text-gray-600">{item}</span>
                    {index < metadata.length - 1 && (
                      <span className="text-gray-500 mx-2">|</span>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>

          {actions.length > 0 && (
            <div className="flex items-center gap-2">
              {actions.map((action, index) => (
                <Button
                  key={index}
                  variant={action.variant || "default"}
                  className={action.className || ""}
                  onClick={action.onClick}
                  disabled={action.disabled}
                >
                  {action.icon && <span className="mr-2">{action.icon}</span>}
                  {action.label}
                </Button>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Delete Button - Only shown if showDeleteButton is true */}
      {showDeleteButton && onDelete && (
        <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <DialogTrigger asChild>
            <Button variant="destructive" size="sm">
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Are you sure you want to delete?</DialogTitle>
              <DialogDescription>
                This action cannot be undone. This will permanently delete this record.
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <DialogClose asChild>
                <Button variant="outline">Cancel</Button>
              </DialogClose>
              <Button variant="destructive" onClick={handleDelete}>
                Delete
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}

export default DetailHeader;
