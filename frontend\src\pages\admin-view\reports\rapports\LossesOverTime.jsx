import React, { useState, useEffect, useRef } from 'react';
import { Bar } from 'react-chartjs-2';
import axios from 'axios';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend } from 'chart.js';
import { jsPDF } from "jspdf";
import * as XLSX from "https://cdn.jsdelivr.net/npm/xlsx@0.18.5/xlsx.mjs";
import { Button } from "@/components/ui/button";
import { getApiBaseUrl } from "@/utils/api-config";
import EmailReportModal from '@/components/reports/EmailReportModal';

// Register Chart.js components
ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);

const LossesOverTime = (props) => {
  const [chartData, setChartData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const chartRef = useRef(null); // Reference to the chart instance
  const API_BASE_URL = getApiBaseUrl();
  const [isEmailModalOpen, setEmailModalOpen] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const response = await axios.get(`${API_BASE_URL}/lossesOverTime`, {
          withCredentials: true,
          headers: { 'x-user-id': '1' },
          params: { timePeriod: 'month' },
        });
        const data = response.data.data;

        const labels = data.map(item => item.time_period);
        const grossLosses = data.map(item => item.gross_loss);
        const netLosses = data.map(item => item.net_loss);

        setChartData({
          labels,
          datasets: [
            {
              label: 'Gross Loss',
              data: grossLosses,
              backgroundColor: '#FF0000',
              barThickness: 20,
            },
            {
              label: 'Net Loss',
              data: netLosses,
              backgroundColor: '#0000FF',
              barThickness: 20,
            },
          ],
        });
      } catch (err) {
        console.error('Error fetching losses over time:', err);
        setError('Failed to load data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const options = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
        labels: {
          boxWidth: 20,
          boxHeight: 10,
          padding: 10,
          font: {
            size: 12,
          },
        },
      },
      title: {
        display: true,
        text: "Losses Over Time",
      },
      tooltip: {
        enabled: true,
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
        ticks: {
          font: {
            size: 12,
          },
        },
        title: {
          display: true,
          text: 'Period',
          font: {
            size: 14,
            weight: 'bold',
          },
        },
      },
      y: {
        grid: {
          color: '#E0E0E0',
        },
        ticks: {
          color: '#A0A0A0',
          font: {
            size: 12,
          },
        },
        title: {
          display: true,
          text: 'Loss Amount (€)',
          font: {
            size: 14,
            weight: 'bold',
          },
        },
        beginAtZero: true,
      },
    },
    categoryPercentage: 0.6,
    barPercentage: 0.8,
  };

  const downloadPDF = async () => {
    try {
      if (!chartRef.current) {
        console.error("Chart reference is not available.");
        alert("Error: Unable to generate PDF. The chart is not ready.");
        return;
      }

      // Get the chart as a base64 image
      const chartImage = chartRef.current.toBase64Image();
      console.log("Chart image generated successfully.");

      // Create a new jsPDF instance
      const pdf = new jsPDF({
        orientation: 'landscape',
        unit: 'in',
        format: 'letter'
      });

      // Calculate the dimensions for the chart image
      const imgProps = pdf.getImageProperties(chartImage);
      const pdfWidth = pdf.internal.pageSize.getWidth() - 1; // 1 inch margin
      const pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;

      // Add the chart image to the PDF
      pdf.addImage(chartImage, 'JPEG', 0.5, 0.5, pdfWidth, pdfHeight);

      // Add the Details section as text
      let yPosition = pdfHeight + 1; // Start below the chart
      pdf.setFontSize(16);
      pdf.setTextColor(45, 55, 72); // RGB equivalent of #2D3748
      pdf.text('Details', 0.5, yPosition);
      yPosition += 0.5;

      pdf.setFontSize(12);
      pdf.setTextColor(113, 128, 150); // RGB equivalent of #718096
      pdf.text('Total Gross Losses:', 0.5, yPosition);
      pdf.setTextColor(26, 41, 66); // RGB equivalent of #1A2942
      pdf.setFont('helvetica', 'bold');
      pdf.text(`€${totalGrossLoss.toLocaleString()}`, 3.5, yPosition);
      yPosition += 0.5;

      pdf.setFont('helvetica', 'normal');
      pdf.setTextColor(113, 128, 150); // RGB equivalent of #718096
      pdf.text('Total Net Losses:', 0.5, yPosition);
      pdf.setTextColor(26, 41, 66); // RGB equivalent of #1A2942
      pdf.setFont('helvetica', 'bold');
      pdf.text(`€${totalNetLoss.toLocaleString()}`, 3.5, yPosition);

      // Save the PDF
      pdf.save('losses_over_time.pdf');
      console.log("PDF generated and downloaded successfully.");
    } catch (err) {
      console.error("Error generating PDF:", err);
      alert("Error generating PDF. Please check the console for details.");
    }
  };

  const downloadExcel = () => {
    if (!chartData) return;
    const excelData = chartData.labels.map((label, index) => ({
      Period: label,
      'Gross Loss': chartData.datasets[0].data[index],
      'Net Loss': chartData.datasets[1].data[index],
    }));
    const worksheet = XLSX.utils.json_to_sheet(excelData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "LossesOverTime");
    XLSX.writeFile(workbook, "losses_over_time.xlsx");
  };

  const totalGrossLoss = chartData ? chartData.datasets[0].data.reduce((sum, value) => sum + value, 0) : 0;
  const totalNetLoss = chartData ? chartData.datasets[1].data.reduce((sum, value) => sum + value, 0) : 0;

  if (loading) return <div className="p-6 text-center">Loading...</div>;
  if (error) return <div className="p-6 text-center text-red-500">{error}</div>;

  return (
    <div className="p-6 bg-white rounded-lg shadow border border-gray-200">
      <Bar ref={chartRef} data={chartData} options={options} />
      <div className="flex gap-4 mt-4">
        <Button onClick={downloadPDF} className="bg-[#22c55e] hover:bg-[#16a34a] text-white">
          Download PDF
        </Button>
        <Button onClick={downloadExcel} className="bg-[#22c55e] hover:bg-[#16a34a] text-white">
          Download Excel
        </Button>
      </div>
      <div className="mt-6 bg-gray-50 rounded-xl p-4 border border-gray-200">
        <h3 className="text-lg font-semibold mb-3 text-gray-800">Details</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
            <p className="text-sm text-gray-500 mb-1">Total Gross Losses</p>
            <p className="text-xl font-bold text-[#1A2942]">€${totalGrossLoss.toLocaleString()}</p>
          </div>
          <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
            <p className="text-sm text-gray-500 mb-1">Total Net Losses</p>
            <p className="text-xl font-bold text-[#1A2942]">€${totalNetLoss.toLocaleString()}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LossesOverTime;
