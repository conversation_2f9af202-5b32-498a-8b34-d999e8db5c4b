const db = require('../../models');
const AuditConstat = db.AuditConstat;
const AuditActivity = db.AuditActivity;
const User = db.User;
const { v4: uuidv4 } = require('uuid');

// Get all audit constats
const getAllAuditConstats = async (req, res) => {
  try {
    const auditConstats = await AuditConstat.findAll({
      include: [
        {
          model: AuditActivity,
          as: 'auditActivity',
          attributes: ['id', 'name']
        },
        {
          model: User,
          as: 'responsableUser',
          attributes: ['id', 'username', 'email']
        }
      ]
    });
    
    return res.status(200).json({
      success: true,
      data: auditConstats
    });
  } catch (error) {
    console.error('Error fetching audit constats:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch audit constats',
      error: error.message
    });
  }
};

// Get audit constats by activity ID
const getConstatsByActivityId = async (req, res) => {
  try {
    const { activityId } = req.params;
    
    const auditConstats = await AuditConstat.findAll({
      where: { auditActivityID: activityId },
      include: [
        {
          model: User,
          as: 'responsableUser',
          attributes: ['id', 'username', 'email']
        }
      ]
    });
    
    return res.status(200).json({
      success: true,
      data: auditConstats
    });
  } catch (error) {
    console.error('Error fetching audit constats by activity ID:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch audit constats',
      error: error.message
    });
  }
};

// Create a new audit constat
const createAuditConstat = async (req, res) => {
  try {
    const {
      name,
      type,
      impact,
      description,
      causes,
      auditActivityID,
      responsable
    } = req.body;
    
    // Validate required fields
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Name is required for audit constat'
      });
    }
    
    if (!auditActivityID) {
      return res.status(400).json({
        success: false,
        message: 'Audit activity ID is required'
      });
    }
    
    // Check if audit activity exists
    const auditActivity = await AuditActivity.findByPk(auditActivityID);
    if (!auditActivity) {
      return res.status(404).json({
        success: false,
        message: 'Audit activity not found'
      });
    }
    
    // If responsable is not provided, use the one from the audit activity
    let responsableId = responsable;
    if (!responsableId) {
      responsableId = auditActivity.responsable;
    }
    
    // Validate type is one of the allowed values
    const allowedTypes = ['Point Fort', 'Point Faible'];
    if (type && !allowedTypes.includes(type)) {
      return res.status(400).json({
        success: false,
        message: `Type must be one of: ${allowedTypes.join(', ')}`
      });
    }
    
    // Create the audit constat
    const auditConstat = await AuditConstat.create({
      id: `AC_${uuidv4().substring(0, 8)}`, // Generate a unique ID with prefix
      name,
      type: type || 'Point Faible',
      impact,
      description,
      causes: causes || null,
      auditActivityID,
      responsable: responsableId
    });

    // Add the missing success response
    return res.status(201).json({
      success: true,
      message: 'Audit constat created successfully',
      data: auditConstat
    });
  } catch (error) {
    console.error('Error creating audit constat:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to create audit constat',
      error: error.message
    });
  }
};

// Get audit constat by ID
const getAuditConstatById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const auditConstat = await AuditConstat.findByPk(id, {
      include: [
        {
          model: AuditActivity,
          as: 'auditActivity',
          attributes: ['id', 'name']
        },
        {
          model: User,
          as: 'responsableUser',
          attributes: ['id', 'username', 'email']
        }
      ]
    });
    
    if (!auditConstat) {
      return res.status(404).json({
        success: false,
        message: 'Audit constat not found'
      });
    }
    
    return res.status(200).json({
      success: true,
      data: auditConstat
    });
  } catch (error) {
    console.error('Error fetching audit constat:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch audit constat',
      error: error.message
    });
  }
};

// Update audit constat
const updateAuditConstat = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      type,
      impact,
      description,
      causes,
      auditActivityID,
      responsable
    } = req.body;
    
    const auditConstat = await AuditConstat.findByPk(id);
    
    if (!auditConstat) {
      return res.status(404).json({
        success: false,
        message: 'Audit constat not found'
      });
    }
    
    // If auditActivityID is being changed, check if the new activity exists
    if (auditActivityID && auditActivityID !== auditConstat.auditActivityID) {
      const auditActivity = await AuditActivity.findByPk(auditActivityID);
      if (!auditActivity) {
        return res.status(404).json({
          success: false,
          message: 'New audit activity not found'
        });
      }
    }
    
    // Update the audit constat
    await auditConstat.update({
      name: name || auditConstat.name,
      type: type || auditConstat.type,
      impact: impact !== undefined ? impact : auditConstat.impact,
      description: description !== undefined ? description : auditConstat.description,
      causes: causes !== undefined ? causes : auditConstat.causes,
      auditActivityID: auditActivityID || auditConstat.auditActivityID,
      responsable: responsable !== undefined ? responsable : auditConstat.responsable
    });
    
    return res.status(200).json({
      success: true,
      message: 'Audit constat updated successfully',
      data: auditConstat
    });
  } catch (error) {
    console.error('Error updating audit constat:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to update audit constat',
      error: error.message
    });
  }
};

// Delete audit constat
const deleteAuditConstat = async (req, res) => {
  try {
    const { id } = req.params;
    
    const auditConstat = await AuditConstat.findByPk(id);
    
    if (!auditConstat) {
      return res.status(404).json({
        success: false,
        message: 'Audit constat not found'
      });
    }
    
    await auditConstat.destroy();
    
    return res.status(200).json({
      success: true,
      message: 'Audit constat deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting audit constat:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to delete audit constat',
      error: error.message
    });
  }
};

module.exports = {
  getAllAuditConstats,
  getConstatsByActivityId,
  createAuditConstat,
  getAuditConstatById,
  updateAuditConstat,
  deleteAuditConstat
};



