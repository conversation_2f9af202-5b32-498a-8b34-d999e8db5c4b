// Map of English strings to translation keys
export const englishToKeyMap = {
  // Admin Sidebar items
  "Home": "admin.sidebar.home",
  "Dashboard": "admin.sidebar.dashboard",
  "Incidents": "admin.sidebar.incidents",
  "Incidents List": "admin.sidebar.incidents_list",
  "Create Incident": "admin.sidebar.create_incident",
  "AI Assistant": "admin.sidebar.ai_assistant",
  "Incident Types": "admin.sidebar.incident_types",
  "Risks": "admin.sidebar.risks",
  "Risks List": "admin.sidebar.risks_list",
  "Risk Types": "admin.sidebar.risk_types",
  "Controls": "admin.sidebar.controls",
  "Processes": "admin.sidebar.processes",
  "Tree View": "admin.sidebar.tree_view",
  "Business Processes": "admin.sidebar.business_processes",
  "Org. Processes": "admin.sidebar.organizational_processes",
  "Operations": "admin.sidebar.operations",
  "Environment": "admin.sidebar.environment",
  "Entities": "admin.sidebar.entities",
  "Control Types": "admin.sidebar.control_types",
  "Business Lines": "admin.sidebar.business_lines",
  "Applications": "admin.sidebar.applications",
  "Action Plans": "admin.sidebar.action_plans",
  "Actions": "admin.sidebar.actions",
  "Reports": "admin.sidebar.reports",

  // Audit Sidebar items
  "Audit Plans": "audit.sidebar.plans_daudit",
  "Audit Missions": "audit.sidebar.missions_audits",

  // Breadcrumbs
  "Audit": "audit.breadcrumb.audit",
  "Audit Plans": "audit.breadcrumb.plans_daudit",
  "Audit Mission": "audit.breadcrumb.mission_daudit",

  // Page titles
  "Edit Audit Mission": "audit.page_titles.edit_mission_audit",
  "Audit Plans": "audit.page_titles.plans_daudit",
  "Create Audit Plan": "audit.page_titles.create_plan_daudit",
  "Edit Audit Plan": "audit.page_titles.edit_plan_daudit",

  // Buttons
  "Back": "audit.buttons.back",
  "Save": "audit.buttons.save",
  "Cancel": "audit.buttons.cancel",
  "Add": "audit.buttons.add",
  "Edit": "audit.buttons.edit",
  "Delete": "audit.buttons.delete",
  "Create": "audit.buttons.create",
  "Update": "audit.buttons.update",

  // Tabs
  "Mission Details": "audit.tabs.mission_details",
  "Characteristics": "audit.tabs.caracteristiques",
  "Scope": "audit.tabs.perimetre",
  "Risk Assessment": "audit.tabs.evaluation_risques",
  "Activities": "audit.tabs.activites",
  "Recommendations": "audit.tabs.recommandations",
  "Documents": "audit.tabs.documents",
  "Expenses": "audit.tabs.depenses",
  "Report": "audit.tabs.rapport",
  "Workflow": "audit.tabs.workflow",
  "Overview": "audit.tabs.overview",
  "Financial Analysis": "audit.tabs.analyse_financiere",
  "Action Plan": "audit.tabs.plan_daction",
  "Activity Feed": "audit.tabs.fil_dactivite",

  // Fields
  "Name": "audit.fields.name",
  "Code": "audit.fields.code",
  "Status": "audit.fields.status",
  "Department": "audit.fields.department",
  "Start Date": "audit.fields.start_date",
  "End Date": "audit.fields.end_date",
  "Duration": "audit.fields.duration",
  "Description": "audit.fields.description",
  "Objectives": "audit.fields.objectives",
  "Scope": "audit.fields.scope",
  "Resources": "audit.fields.resources",
  "Progress": "audit.fields.progress",
  "Findings": "audit.fields.findings",
  "Recommendations": "audit.fields.recommendations",

  // Status
  "In Progress": "audit.status.in_progress",
  "Completed": "audit.status.completed",
  "Planned": "audit.status.planned",
  "Delayed": "audit.status.delayed",
  "Cancelled": "audit.status.cancelled"
};

/**
 * Translates text using the mapping and i18next
 * @param {string} text - The original text (usually English)
 * @param {function} t - The translation function from useTranslation()
 * @returns {string} - The translated text
 */
export const translateText = (text, t) => {
  const key = englishToKeyMap[text];
  if (key) {
    return t(key);
  }
  // If no mapping is found, return the original text
  return text;
};
