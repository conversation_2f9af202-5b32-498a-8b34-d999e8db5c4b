import { useState, useEffect } from "react";
import { useOutletContext } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import axios from "axios";
import { useTranslation } from "react-i18next";
import {
  Plus,
  Trash2,
  Loader2,
  Edit,
  User,
  Calendar,
  CheckCircle2,
  AlertCircle,
  Link,
  Unlink
} from "lucide-react";
import {
  getActionsByActionPlanId,
  createAction,
  updateAction,
  deleteAction,
  deleteMultipleActions,
  reset
} from "@/store/slices/actionSlice";
import { getAllUsers } from "@/store/slices/userSlice";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { toast } from "sonner";
import { getApiBaseUrl } from "@/utils/api-config";
function ActionPlanActions() {
  const { actionPlan } = useOutletContext();
  const dispatch = useDispatch();
  const API_BASE_URL = getApiBaseUrl();
  const { t } = useTranslation();
  // Get actions and users from Redux store
  const { actions, isLoading } = useSelector((state) => state.action);
  const { users } = useSelector((state) => state.user);

  const [selectedActions, setSelectedActions] = useState([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isLinkModalOpen, setLinkModalOpen] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [currentAction, setCurrentAction] = useState(null);
  const [formData, setFormData] = useState({
    name: "",
    priority: "Low",
    status: "Not Started",
    description: "",
    startDate: "",
    endDate: "",
    assigneeId: "none"
  });

  // State variables for linking existing actions
  const [unlinkedActions, setUnlinkedActions] = useState([]);
  const [actionsToLink, setActionsToLink] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLinkLoading, setIsLinkLoading] = useState(false);
  const [unlinkingActionId, setUnlinkingActionId] = useState(null);

  // Fetch actions and users on component mount
  useEffect(() => {
    if (actionPlan?.actionPlanID) {
      dispatch(getActionsByActionPlanId(actionPlan.actionPlanID));
      dispatch(getAllUsers());
    }

    return () => {
      dispatch(reset());
    };
  }, [dispatch, actionPlan]);

  // Handle checkbox change for selecting actions
  const handleSelectAction = (actionID) => {
    setSelectedActions(prev => {
      if (prev.includes(actionID)) {
        return prev.filter(id => id !== actionID);
      } else {
        return [...prev, actionID];
      }
    });
  };

  // Handle select all checkbox
  const handleSelectAll = () => {
    if (selectedActions.length === actions.length) {
      setSelectedActions([]);
    } else {
      setSelectedActions(actions.map(action => action.actionID));
    }
  };

  // Handle delete selected actions
  const handleDeleteSelected = () => {
    if (selectedActions.length === 0) return;

    if (window.confirm(t('admin.action_plans.actions.delete_action_confirm', 'Are you sure you want to delete this action?'))) {
      dispatch(deleteMultipleActions(selectedActions))
        .then(() => {
          setSelectedActions([]);
        });
    }
  };

  // Handle input change
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle select change
  const handleSelectChange = (name, value) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Open modal for creating a new action
  const handleAddAction = () => {
    setIsEditMode(false);
    setCurrentAction(null);
    setFormData({
      name: "",
      priority: "Low",
      status: "Not Started",
      description: "",
      startDate: "",
      endDate: "",
      assigneeId: "none"
    });
    setIsModalOpen(true);
  };

  // Open modal for editing an action
  const handleEditAction = (action) => {
    setIsEditMode(true);
    setCurrentAction(action);
    setFormData({
      name: action.name,
      priority: action.priority || "Low",
      status: action.status || "Not Started",
      description: action.description || "",
      startDate: action.startDate ? new Date(action.startDate).toISOString().split('T')[0] : "",
      endDate: action.endDate ? new Date(action.endDate).toISOString().split('T')[0] : "",
      assigneeId: action.assigneeId ? action.assigneeId.toString() : "none"
    });
    setIsModalOpen(true);
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();

    if (!formData.name) {
      toast.error("Name is required");
      return;
    }

    if (isEditMode && currentAction) {
      // Update existing action
      dispatch(updateAction({
        id: currentAction.actionID,
        actionData: {
          name: formData.name,
          priority: formData.priority,
          status: formData.status,
          description: formData.description,
          startDate: formData.startDate || null,
          endDate: formData.endDate || null,
          assigneeId: formData.assigneeId !== "none" ? parseInt(formData.assigneeId) : null
        }
      }))
        .then((result) => {
          if (!result.error) {
            setIsModalOpen(false);
          }
        });
    } else {
      // Create new action
      dispatch(createAction({
        name: formData.name,
        priority: formData.priority,
        status: formData.status,
        description: formData.description,
        startDate: formData.startDate || null,
        endDate: formData.endDate || null,
        assigneeId: formData.assigneeId !== "none" ? parseInt(formData.assigneeId) : null,
        actionPlanID: actionPlan.actionPlanID
      }))
        .then((result) => {
          if (!result.error) {
            setIsModalOpen(false);
          }
        });
    }
  };

  // Handle delete action
  const handleDeleteAction = (actionID) => {
    if (window.confirm("Are you sure you want to delete this action?")) {
      dispatch(deleteAction(actionID));
    }
  };

  // Get status badge color
  const getStatusBadgeColor = (status) => {
    switch (status) {
      case "Completed":
        return "bg-green-100 text-green-800";
      case "In Progress":
        return "bg-blue-100 text-blue-800";
      case "Cancelled":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Get priority badge color
  const getPriorityBadgeColor = (priority) => {
    switch (priority) {
      case "Critical":
        return "bg-red-100 text-red-800";
      case "High":
        return "bg-orange-100 text-orange-800";
      case "Medium":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-green-100 text-green-800";
    }
  };

  // Function to fetch unlinked actions
  const fetchUnlinkedActions = async () => {
    try {
      setIsLinkLoading(true);
      // First get all actions
      const allActionsResponse = await axios.get(`${API_BASE_URL}/actions`, {
        withCredentials: true
      });

      // Then get actions linked to this plan
      const linkedActionsResponse = await axios.get(`${API_BASE_URL}/actions/action-plan/${actionPlan.actionPlanID}`, {
        withCredentials: true
      });

      if (allActionsResponse.data.success && linkedActionsResponse.data.success) {
        const allActions = allActionsResponse.data.data || [];
        const linkedActions = linkedActionsResponse.data.data || [];

        // Create a set of linked action IDs for faster lookup
        const linkedActionIds = new Set(linkedActions.map(action => action.actionID));

        // Filter out actions that are already linked to this plan
        const unlinkedActionsArray = allActions.filter(action => !linkedActionIds.has(action.actionID));

        setUnlinkedActions(unlinkedActionsArray);
      } else {
        toast.error('Failed to fetch actions');
      }
    } catch (error) {
      console.error('Error fetching unlinked actions:', error);
      toast.error('Failed to fetch unlinked actions');
    } finally {
      setIsLinkLoading(false);
    }
  };

  // Handler for opening the link modal
  const handleOpenLinkModal = () => {
    fetchUnlinkedActions();
    setLinkModalOpen(true);
    setActionsToLink([]);
    setSearchTerm('');
  };

  // Handler for selecting actions to link
  const handleSelectActionToLink = (actionID) => {
    setActionsToLink((prev) => {
      if (prev.includes(actionID)) {
        return prev.filter(id => id !== actionID);
      } else {
        return [...prev, actionID];
      }
    });
  };

  // Handler for selecting all actions to link
  const handleSelectAllToLink = (checked) => {
    if (checked) {
      const filteredActionIds = filteredUnlinkedActions.map(action => action.actionID);
      setActionsToLink(filteredActionIds);
    } else {
      setActionsToLink([]);
    }
  };

  // Handler for linking selected actions
  const handleLinkActions = async () => {
    if (actionsToLink.length === 0) {
      toast.error('Please select at least one action to link');
      return;
    }

    setIsLinkLoading(true);
    try {
      // First verify the action plan exists
      if (!actionPlan?.actionPlanID) {
        throw new Error('Invalid action plan ID');
      }

      console.log('Linking actions:', {
        actionPlanID: actionPlan.actionPlanID,
        actionsToLink,
        actionPlan
      });

      // Update each action with the action plan ID using Redux
      const updatePromises = actionsToLink.map(actionId => {
        console.log(`Updating action ${actionId} with actionPlanID ${actionPlan.actionPlanID}`);
        return dispatch(updateAction({
          id: actionId,
          actionData: {
            actionPlanID: actionPlan.actionPlanID
          }
        })).then(result => {
          console.log(`Update result for action ${actionId}:`, result);
          return result;
        }).catch(error => {
          console.error(`Error updating action ${actionId}:`, error);
          throw error;
        });
      });

      const results = await Promise.all(updatePromises);
      console.log('All update results:', results);

      // Verify all updates were successful
      const failedUpdates = results.filter(result => !result.payload?.success);
      if (failedUpdates.length > 0) {
        console.error('Some updates failed:', failedUpdates);
        throw new Error(`Failed to update ${failedUpdates.length} actions`);
      }

      // Log the result to verify operations
      console.log(`Successfully linked ${actionsToLink.length} actions to action plan ${actionPlan.actionPlanID}`);

      // Clear the selected actions
      setActionsToLink([]);

      // Close the modal
      setLinkModalOpen(false);

      // Refresh the actions list
      await dispatch(getActionsByActionPlanId(actionPlan.actionPlanID));

      toast.success('Actions linked successfully');
    } catch (error) {
      console.error('Error linking actions:', error);
      toast.error('Failed to link actions: ' + (error.response?.data?.message || error.message));
    } finally {
      setIsLinkLoading(false);
    }
  };

  // Filter unlinked actions based on search term
  const filteredUnlinkedActions = searchTerm.trim() === ''
    ? unlinkedActions
    : unlinkedActions.filter(action =>
        action.name.toLowerCase().includes(searchTerm.toLowerCase())
      );

  // Handle unlink action
  const handleUnlinkAction = (actionID) => {
    if (window.confirm("Are you sure you want to unlink this action from the action plan?")) {
      setUnlinkingActionId(actionID);
      dispatch(updateAction({
        id: actionID,
        actionData: {
          actionPlanID: null
        }
      }))
      .then((result) => {
        if (result.payload?.success) {
          toast.success("Action unlinked successfully");
          // Refresh the actions list
          if (actionPlan?.actionPlanID) {
            dispatch(getActionsByActionPlanId(actionPlan.actionPlanID));
          }
        } else {
          console.error("Failed to unlink action:", result.payload);
          toast.error("Failed to unlink action: " + (result.payload?.message || "Unknown error"));
        }
      })
      .catch((error) => {
        console.error("Error unlinking action:", error);
        toast.error("Error unlinking action: " + (error.message || "Unknown error"));
      })
      .finally(() => {
        setUnlinkingActionId(null);
      });
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">{t('admin.action_plans.actions.title', 'Actions')}</h2>
        <div className="flex gap-2">
          {selectedActions.length > 0 && (
            <Button
              variant="destructive"
              size="sm"
              onClick={handleDeleteSelected}
              className="flex items-center gap-1"
            >
              <Trash2 className="h-4 w-4" />
              {t('common.buttons.delete', 'Delete')} ({selectedActions.length})
            </Button>
          )}
          <div className="flex gap-2">
            <Button
              onClick={handleAddAction}
              className="flex items-center gap-1"
            >
              <Plus className="h-4 w-4" />
              {t('admin.action_plans.actions.add_action', 'Add Action')}
            </Button>
            <Button
              variant="outline"
              onClick={handleOpenLinkModal}
              className="flex items-center gap-1"
              title={t('admin.action_plans.actions.link_action', 'Link Existing Action')}
            >
              <Link className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
        </div>
      ) : actions.length === 0 ? (
        <div className="text-center py-8 bg-gray-50 rounded-lg">
          <p className="text-gray-500">{t('admin.action_plans.actions.no_actions', 'No actions found for this action plan.')}</p>
          <Button
            onClick={handleAddAction}
            variant="outline"
            className="mt-4"
          >
            {t('admin.action_plans.actions.create_first', 'Create your first action by clicking the button above.')}
          </Button>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white rounded-lg overflow-hidden">
            <thead className="bg-gray-50">
              <tr>
                <th className="w-10 px-6 py-3 text-left">
                  <Checkbox
                    checked={selectedActions.length === actions.length && actions.length > 0}
                    onCheckedChange={handleSelectAll}
                  />
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('admin.action_plans.actions.action_name', 'Action Name')}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('common.fields.priority', 'Priority')}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('admin.action_plans.actions.status', 'Status')}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('admin.action_plans.actions.assignee', 'Assignee')}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('common.fields.start_date', 'Start Date')}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('common.fields.end_date', 'End Date')}
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('common.fields.actions', 'Actions')}
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {actions.map((action) => (
                <tr key={action.actionID} className="hover:bg-gray-50">
                  <td className="px-6 py-4">
                    <Checkbox
                      checked={selectedActions.includes(action.actionID)}
                      onCheckedChange={() => handleSelectAction(action.actionID)}
                    />
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900">
                    {action.name}
                  </td>
                  <td className="px-6 py-4 text-sm">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityBadgeColor(action.priority)}`}>
                      {action.priority || "Low"}
                    </span>
                  </td>
                  <td className="px-6 py-4 text-sm">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusBadgeColor(action.status)}`}>
                      {action.status || "Not Started"}
                    </span>
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900">
                    {action.assignee ? (
                      <div className="flex items-center">
                        <User className="h-4 w-4 mr-1 text-gray-500" />
                        {action.assignee.username}
                      </div>
                    ) : "-"}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900">
                    {action.startDate ? new Date(action.startDate).toLocaleDateString() : "-"}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900">
                    {action.endDate ? new Date(action.endDate).toLocaleDateString() : "-"}
                  </td>
                  <td className="px-6 py-4 text-sm text-right">
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleEditAction(action)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleUnlinkAction(action.actionID)}
                        className="text-blue-500 hover:text-blue-700 hover:bg-blue-50"
                        disabled={unlinkingActionId === action.actionID}
                      >
                        {unlinkingActionId === action.actionID ? <Loader2 className="h-4 w-4 animate-spin" /> : <Unlink className="h-4 w-4" />}
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteAction(action.actionID)}
                        className="text-red-500 hover:text-red-700 hover:bg-red-50"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* Create/Edit Action Modal */}
      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="sm:max-w-[550px]">
          <DialogHeader>
            <DialogTitle>{isEditMode ? t('admin.action_plans.actions.edit_action', 'Edit Action') : t('admin.action_plans.actions.add_new_action', 'Create New Action')}</DialogTitle>
            <DialogDescription>
              {isEditMode
                ? t('admin.action_plans.actions.update_action_desc', 'Update the details of this action.')
                : t('admin.action_plans.actions.add_action_desc', 'Add a new action to this action plan.')}
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleSubmit} className="space-y-4 py-4">
            <div className="grid grid-cols-1 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">{t('admin.action_plans.actions.action_name', 'Action Name')} *</Label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  placeholder={t('admin.action_plans.actions.action_name_placeholder', 'Enter action name')}
                  required
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="priority">{t('common.fields.priority', 'Priority')}</Label>
                  <Select
                    value={formData.priority}
                    onValueChange={(value) => handleSelectChange("priority", value)}
                  >
                    <SelectTrigger id="priority">
                      <SelectValue placeholder={t('admin.action_plans.filters.select_priority', 'Select priority')} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Low">{t('common.priority.low', 'Low')}</SelectItem>
                      <SelectItem value="Medium">{t('common.priority.medium', 'Medium')}</SelectItem>
                      <SelectItem value="High">{t('common.priority.high', 'High')}</SelectItem>
                      <SelectItem value="Critical">{t('common.priority.critical', 'Critical')}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="status">{t('admin.action_plans.actions.status', 'Status')}</Label>
                  <Select
                    value={formData.status}
                    onValueChange={(value) => handleSelectChange("status", value)}
                  >
                    <SelectTrigger id="status">
                      <SelectValue placeholder={t('admin.action_plans.filters.select_status', 'Select status')} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Not Started">{t('common.status.not_started', 'Not Started')}</SelectItem>
                      <SelectItem value="In Progress">{t('common.status.in_progress', 'In Progress')}</SelectItem>
                      <SelectItem value="Completed">{t('common.status.completed', 'Completed')}</SelectItem>
                      <SelectItem value="Cancelled">{t('common.status.cancelled', 'Cancelled')}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="assigneeId">{t('admin.action_plans.actions.assignee', 'Assignee')}</Label>
                <Select
                  value={formData.assigneeId}
                  onValueChange={(value) => handleSelectChange("assigneeId", value)}
                >
                  <SelectTrigger id="assigneeId">
                    <SelectValue placeholder={t('admin.action_plans.filters.select_assignee', 'Select assignee')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">{t('common.none', 'None')}</SelectItem>
                    {users.map((user) => (
                      <SelectItem key={user.id} value={user.id.toString()}>
                        {user.username} ({user.role})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="startDate">{t('common.fields.start_date', 'Start Date')}</Label>
                  <div className="relative">
                    <Calendar className="absolute left-3 top-2.5 h-4 w-4 text-gray-500" />
                    <Input
                      id="startDate"
                      name="startDate"
                      type="date"
                      value={formData.startDate}
                      onChange={handleInputChange}
                      className="pl-10"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="endDate">{t('common.fields.end_date', 'End Date')}</Label>
                  <div className="relative">
                    <Calendar className="absolute left-3 top-2.5 h-4 w-4 text-gray-500" />
                    <Input
                      id="endDate"
                      name="endDate"
                      type="date"
                      value={formData.endDate}
                      onChange={handleInputChange}
                      className="pl-10"
                    />
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">{t('admin.action_plans.actions.description', 'Description')}</Label>
                <Textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  placeholder={t('admin.action_plans.actions.description_placeholder', 'Enter action description')}
                  rows={4}
                />
              </div>
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsModalOpen(false)}
              >
                {t('common.buttons.cancel', 'Cancel')}
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isEditMode ? t('common.buttons.update', 'Update') : t('common.buttons.create', 'Create')}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Link Existing Actions Modal */}
      <Dialog open={isLinkModalOpen} onOpenChange={setLinkModalOpen}>
        <DialogContent className="sm:max-w-[550px]">
          <DialogHeader>
            <DialogTitle>{t('admin.action_plans.actions.link_actions', 'Link Existing Actions')}</DialogTitle>
            <DialogDescription>
              {t('admin.action_plans.actions.link_actions_desc', 'Select existing actions to link to this action plan.')}
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            <div className="mb-4">
              <Input
                placeholder={t('admin.action_plans.actions.search_actions', 'Search actions...')}
                className="w-full"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            <div className="border rounded-md max-h-[300px] overflow-y-auto">
              {isLinkLoading ? (
                <div className="flex justify-center items-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
                </div>
              ) : filteredUnlinkedActions.length === 0 ? (
                <div className="p-4 text-center text-gray-500">
                  {t('admin.action_plans.actions.no_actions_to_link', 'No actions available to link')}
                </div>
              ) : (
                <>
                  <div className="p-2 border-b bg-gray-50">
                    <div className="flex items-center">
                      <Checkbox
                        id="select-all-actions"
                        checked={actionsToLink.length === filteredUnlinkedActions.length && filteredUnlinkedActions.length > 0}
                        onCheckedChange={handleSelectAllToLink}
                      />
                      <Label htmlFor="select-all-actions" className="ml-2 cursor-pointer flex-1">
                        {t('common.select_all', 'Select All')}
                      </Label>
                    </div>
                  </div>
                  {filteredUnlinkedActions.map((action) => (
                    <div key={action.actionID} className="p-4 border-b hover:bg-gray-50">
                      <div className="flex items-center">
                        <Checkbox
                          id={`action-${action.actionID}`}
                          checked={actionsToLink.includes(action.actionID)}
                          onCheckedChange={() => handleSelectActionToLink(action.actionID)}
                        />
                        <Label htmlFor={`action-${action.actionID}`} className="ml-2 cursor-pointer flex-1">
                          <div className="font-medium">{action.name}</div>
                          <div className="text-sm text-gray-500 flex items-center gap-2">
                            <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeColor(action.status)}`}>
                              {action.status || "Not Started"}
                            </span>
                            <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${getPriorityBadgeColor(action.priority)}`}>
                              {action.priority || "Low"}
                            </span>
                          </div>
                        </Label>
                      </div>
                    </div>
                  ))}
                </>
              )}
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setLinkModalOpen(false)}
            >
              {t('common.buttons.cancel', 'Cancel')}
            </Button>
            <Button
              type="button"
              onClick={handleLinkActions}
              disabled={isLinkLoading || actionsToLink.length === 0}
            >
              {isLinkLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {t('admin.action_plans.actions.link_selected', 'Link Selected')} ({actionsToLink.length})
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default ActionPlanActions;
