const { 
  Incident, 
  IncidentLoss, 
  IncidentGain, 
  IncidentRecovery, 
  IncidentProvision 
} = require('../../models');

// Get all financial entries for an incident
const getIncidentFinancialData = async (req, res) => {
  try {
    const { incidentId } = req.params;
    
    // Check if incident exists
    const incident = await Incident.findByPk(incidentId);
    if (!incident) {
      return res.status(404).json({
        success: false,
        message: 'Incident not found'
      });
    }
    
    // Fetch all financial entries for this incident
    const [losses, gains, recoveries, provisions] = await Promise.all([
      IncidentLoss.findAll({
        where: { incidentID: incidentId },
        order: [['createdAt', 'DESC']]
      }),
      IncidentGain.findAll({
        where: { incidentID: incidentId },
        order: [['createdAt', 'DESC']]
      }),
      IncidentRecovery.findAll({
        where: { incidentID: incidentId },
        order: [['createdAt', 'DESC']]
      }),
      IncidentProvision.findAll({
        where: { incidentID: incidentId },
        order: [['createdAt', 'DESC']]
      })
    ]);
    
    // Group entries by category
    const financialData = {
      losses,
      gains,
      recoveries,
      provisions
    };
    
    res.json({
      success: true,
      data: financialData
    });
  } catch (error) {
    console.error('Error fetching financial data for incident:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch financial data',
      error: error.message
    });
  }
};

// Update all financial entries for an incident
const updateIncidentFinancialData = async (req, res) => {
  try {
    const { incidentId } = req.params;
    const { losses, gains, recoveries, provisions } = req.body;
    
    // console.log(`Updating financial data for incident ${incidentId}:`, 
    //   `losses: ${losses?.length || 0}, gains: ${gains?.length || 0}, ` +
    //   `recoveries: ${recoveries?.length || 0}, provisions: ${provisions?.length || 0}`);
    
    // Check if incident exists
    const incident = await Incident.findByPk(incidentId);
    if (!incident) {
      return res.status(404).json({
        success: false,
        message: 'Incident not found'
      });
    }
    
    const results = {
      created: 0,
      updated: 0,
      deleted: 0
    };
    
    // Process losses
    await processEntries(IncidentLoss, 'lossID', losses, incidentId, results);
    
    // Process gains
    await processEntries(IncidentGain, 'gainID', gains, incidentId, results);
    
    // Process recoveries
    await processEntries(IncidentRecovery, 'recoveryID', recoveries, incidentId, results);
    
    // Process provisions
    await processEntries(IncidentProvision, 'provisionID', provisions, incidentId, results);
    
    // Update incident totals
    await updateIncidentTotals(incidentId);
    
    // console.log(`Financial data updated for incident ${incidentId}: created: ${results.created}, updated: ${results.updated}, deleted: ${results.deleted}`);
    
    res.json({
      success: true,
      message: 'Financial data updated successfully',
      results
    });
  } catch (error) {
    console.error('Error updating financial data:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update financial data',
      error: error.message
    });
  }
};

// Helper function to process entries for a specific category
async function processEntries(Model, idField, entries, incidentId, results) {
  if (!Array.isArray(entries)) return;
  
  // Get existing entries
  const existingEntries = await Model.findAll({
    where: { incidentID: incidentId }
  });
  
  // console.log(`Processing ${Model.name}: Found ${existingEntries.length} existing entries, received ${entries.length} entries`);
  
  // Track IDs to determine which existing entries to delete
  const processedIds = [];
  
  // Create or update entries
  for (const entry of entries) {
    if (entry.id) {
      // Entry has ID so it might already exist
      const existingEntry = existingEntries.find(e => e[idField] === entry.id);
      if (existingEntry) {
        // Update existing entry
        await existingEntry.update({
          name: entry.name,
          amount: Number(entry.amount) || 0,
          localAmount: entry.localAmount ? Number(entry.localAmount) : null,
          currency: entry.currency || 'XOF'
        });
        results.updated++;
        processedIds.push(entry.id);
        // console.log(`Updated ${Model.name} entry: ${entry.id} - ${entry.name}`);
      } else {
        // Create new entry with provided ID
        await Model.create({
          [idField]: entry.id,
          name: entry.name,
          amount: Number(entry.amount) || 0,
          localAmount: entry.localAmount ? Number(entry.localAmount) : null,
          currency: entry.currency || 'XOF',
          incidentID: incidentId
        });
        results.created++;
        processedIds.push(entry.id);
        // console.log(`Created ${Model.name} entry with existing ID: ${entry.id} - ${entry.name}`);
      }
    } else {
      // Create new entry with generated ID
      const newEntryId = `${idField.replace('ID', '')}_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`;
      const newEntry = await Model.create({
        [idField]: newEntryId,
        name: entry.name,
        amount: Number(entry.amount) || 0,
        localAmount: entry.localAmount ? Number(entry.localAmount) : null,
        currency: entry.currency || 'XOF',
        incidentID: incidentId
      });
      results.created++;
      processedIds.push(newEntry[idField]);
      // console.log(`Created new ${Model.name} entry with ID: ${newEntry[idField]} - ${entry.name}`);
    }
  }
  
  // Delete entries that weren't in the update
  for (const existingEntry of existingEntries) {
    if (!processedIds.includes(existingEntry[idField])) {
      await existingEntry.destroy();
      results.deleted++;
      // console.log(`Deleted ${Model.name} entry: ${existingEntry[idField]} - ${existingEntry.name}`);
    }
  }
}

// Helper function to update incident totals
async function updateIncidentTotals(incidentId) {
  try {
    // Get all financial entries for this incident
    const [losses, recoveries, provisions] = await Promise.all([
      IncidentLoss.findAll({
        where: { incidentID: incidentId }
      }),
      IncidentRecovery.findAll({
        where: { incidentID: incidentId }
      }),
      IncidentProvision.findAll({
        where: { incidentID: incidentId }
      })
    ]);
    
    // Calculate totals
    const grossLoss = losses.reduce((sum, entry) => sum + (Number(entry.amount) || 0), 0);
    const recoveriesTotal = recoveries.reduce((sum, entry) => sum + (Number(entry.amount) || 0), 0);
    const provisionsTotal = provisions.reduce((sum, entry) => sum + (Number(entry.amount) || 0), 0);
    
    // Update incident
    const incident = await Incident.findByPk(incidentId);
    if (incident) {
      await incident.update({
        grossLoss,
        recoveries: recoveriesTotal,
        provisions: provisionsTotal
      });
      // console.log(`Updated incident ${incidentId} totals: grossLoss=${grossLoss}, recoveries=${recoveriesTotal}, provisions=${provisionsTotal}`);
    }
  } catch (error) {
    console.error(`Error updating incident totals for ${incidentId}:`, error);
    throw error;
  }
}

module.exports = {
  getIncidentFinancialData,
  updateIncidentFinancialData
};
