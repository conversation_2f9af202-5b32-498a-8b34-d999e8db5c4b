const express = require('express');
const router = express.Router();
const { verifyToken, authorizeRoles } = require('../../middleware/auth');
const {
  getAllControls,
  createControl,
  getControlById,
  updateControl,
  deleteControl
} = require('../../controllers/data/control-controller');

// Apply authentication middleware to all routes
router.use(verifyToken);

// Get all controls
router.get('/', getAllControls);

// Create new control
router.post('/', authorizeRoles(['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager']), createControl);

// Get control by ID
router.get('/:id', getControlById);

// Update control
router.put('/:id', authorizeRoles(['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager']), updateControl);

// Delete control
router.delete('/:id', authorizeRoles(['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager']), deleteControl);

module.exports = router;
