import React from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Globe } from 'lucide-react';

const LanguageSwitcher = () => {
  const { language, changeLanguage } = useLanguage();
  const { t } = useTranslation();

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button 
          variant="ghost" 
          size="icon" 
          className="p-2 text-[#555F6D] hover:text-[#F62D51] transition-colors"
        >
          <Globe size={24} />
          <span className="sr-only">{t('common.header.language')}</span>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-48 bg-white shadow-md rounded-md p-2">
        <div className="flex flex-col">
          <button
            onClick={() => changeLanguage('fr')}
            className={`flex items-center gap-2 px-3 py-2 hover:bg-gray-100 hover:rounded-md transition-all ${
              language === 'fr' ? 'text-[#F62D51] font-medium' : 'text-[#555F6D]'
            }`}
          >
            <span className="text-lg">🇫🇷</span> Français
          </button>
          <button
            onClick={() => changeLanguage('en')}
            className={`flex items-center gap-2 px-3 py-2 hover:bg-gray-100 hover:rounded-md transition-all ${
              language === 'en' ? 'text-[#F62D51] font-medium' : 'text-[#555F6D]'
            }`}
          >
            <span className="text-lg">🇬🇧</span> English
          </button>
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default LanguageSwitcher;
