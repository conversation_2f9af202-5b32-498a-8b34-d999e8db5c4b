import React, { useState, useEffect, useRef, useCallback } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Paperclip, ChevronUp, ChevronDown, Save, Loader2, FileText, Plus, Trash2, Upload, ArrowUp, ArrowDown } from "lucide-react";
import { AuditTravailFicheAttachmentsSection } from "@/components/audit/AuditTravailFicheAttachmentsSection";
import { useCustomOutletContext } from "../../edit-fiches-travail";
import { updateFicheDeTravail, deleteQuestion, reorderQuestions } from "@/services/fiche-de-travail-service";
import { toast } from "sonner";
import { Select, SelectTrigger, SelectContent, SelectItem, SelectValue } from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import { DateInput } from "@/components/ui/date-input";
import { Combobox } from "@/components/ui/combobox";
import { debounce } from "lodash";

function FichesTravailCaracteristiquesTab() {
  const context = useCustomOutletContext();
  const fiche = context?.fiche;

  // Section toggles
  const [isCaracteristiquesOpen, setIsCaracteristiquesOpen] = useState(true);
  const [isQuestionnaireOpen, setIsQuestionnaireOpen] = useState(true);
  const [isAttachmentsOpen, setIsAttachmentsOpen] = useState(true);

  // Loading states
  const [isSaving, setIsSaving] = useState(false);

  // Form state for fiche
  const [formData, setFormData] = useState({
    name: "",
    auditMissionID: "",
    auditActivityID: "",
    tailleEchantillon: "",
    tacheDetail: "",
    commentaire: ""
  });

  // Quiz state
  const [questions, setQuestions] = useState([]);
  const [newQuestion, setNewQuestion] = useState({
    question_text: "",
    input_type: "text",
    options: []
  });
  const [option, setOption] = useState("");

  // Track which question is being deleted
  const [deletingQuestionId, setDeletingQuestionId] = useState(null);

  // Track which question is being reordered
  const [reorderingQuestionId, setReorderingQuestionId] = useState(null);

  const questionnaireRef = useRef(null);

  // Debounced save function for main form fields
  const debouncedSaveFiche = useCallback(
    debounce(async (currentFormData) => {
      if (!fiche?.id) {
        console.warn("Fiche ID is missing for auto-save.");
        return;
      }
      try {
        // Only save form data, questions are saved separately
        const response = await updateFicheDeTravail(fiche.id, currentFormData);
        if (response && response.success) {
          toast.success("Fiche de travail sauvegardée automatiquement");
          if (context?.setFiche) {
            context.setFiche(response.data);
          }
        } else {
          throw new Error(response?.message || "Erreur lors de la sauvegarde automatique");
        }
      } catch (error) {
        console.error('Error auto-saving fiche:', error);
        toast.error(error.message || "Erreur lors de la sauvegarde automatique de la fiche de travail");
      }
    }, 1000), // 1 second debounce delay
    [fiche?.id, context?.setFiche]
  );

  // Initialize form data when fiche is loaded
  useEffect(() => {
    if (fiche) {
      setFormData({
        name: fiche.name || "",
        auditMissionID: fiche.auditMissionID || "",
        auditActivityID: fiche.auditActivityID || '',
        tailleEchantillon: fiche.tailleEchantillon || 'N/A',
        tacheDetail: fiche.tacheDetail || '',
        commentaire: fiche.commentaire || ''
      });
      // Initialize questions from fiche if available
      setQuestions(fiche.questions || []);
    }
  }, [fiche]);

  // Handlers for fiche form
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => {
      const newState = { ...prev, [name]: value };
      debouncedSaveFiche(newState); // Trigger debounced save
      return newState;
    });
  };

  // Handlers for quiz form
  const handleQuestionInputChange = (e) => {
    const { name, value } = e.target;
    setNewQuestion((prev) => ({ ...prev, [name]: value }));
  };

  const handleAddOption = () => {
    if (option.trim()) {
      setNewQuestion((prev) => ({
        ...prev,
        options: [...prev.options, option.trim()]
      }));
      setOption("");
    }
  };

  const handleRemoveOption = (index) => {
    setNewQuestion((prev) => ({
      ...prev,
      options: prev.options.filter((_, i) => i !== index)
    }));
  };

  const handleAddQuestion = async () => {
    if (!newQuestion.question_text.trim()) {
      toast.error("Veuillez entrer une question");
      return;
    }
    if (["radio", "select", "multi-select"].includes(newQuestion.input_type) && newQuestion.options.length === 0) {
      toast.error("Veuillez ajouter au moins une option pour ce type de question");
      return;
    }

    // Add the new question to the local state
    const updatedQuestions = [
      ...questions,
      {
        // No id, let backend assign
        question_text: newQuestion.question_text,
        input_type: newQuestion.input_type,
        options: ["radio", "select", "multi-select"].includes(newQuestion.input_type) ? [...newQuestion.options] : null
      }
    ];

    // Save immediately to backend
    if (!fiche?.id) {
      toast.error("ID de fiche manquant");
      return;
    }
    setIsSaving(true);
    try {
      const response = await updateFicheDeTravail(fiche.id, { ...formData, questions: updatedQuestions });
      if (response && response.success) {
        toast.success("Question ajoutée et sauvegardée avec succès");
        // Update local state with backend's latest questions (with IDs)
        if (response.data?.questions) {
          setQuestions(response.data.questions);
        } else {
          setQuestions(updatedQuestions);
        }
        if (context?.setFiche) {
          context.setFiche(response.data);
        }
      } else {
        throw new Error(response?.message || "Erreur lors de la sauvegarde de la question");
      }
    } catch (error) {
      console.error('Error saving question:', error);
      toast.error(error.message || "Erreur lors de la sauvegarde de la question");
    } finally {
      setIsSaving(false);
    }

    // Reset form
    setNewQuestion({
      question_text: "",
      input_type: "text",
      options: []
    });
    setOption("");
  };

  const handleDeleteQuestion = async (id) => {
    if (!fiche?.id) {
      toast.error("ID de fiche manquant");
      return;
    }
    setDeletingQuestionId(id);
    setIsSaving(true);
    try {
      // Delete question from backend
      await deleteQuestion(id);
      // Fetch updated questions from backend
      const response = await updateFicheDeTravail(fiche.id, { ...formData, questions: questions.filter((q) => q.id !== id) });
      if (response && response.success) {
    toast.success("Question supprimée avec succès");
        if (response.data?.questions) {
          setQuestions(response.data.questions);
        } else {
          setQuestions(questions.filter((q) => q.id !== id));
        }
        if (context?.setFiche) {
          context.setFiche(response.data);
        }
      } else {
        throw new Error(response?.message || "Erreur lors de la suppression de la question");
      }
    } catch (error) {
      console.error('Error deleting question:', error);
      toast.error(error.message || "Erreur lors de la suppression de la question");
    } finally {
      setIsSaving(false);
      setDeletingQuestionId(null);
    }
  };

  const handleMoveUp = async (index) => {
    if (index === 0) return;
    setReorderingQuestionId(questions[index].id);
    const newQuestions = [...questions];
      [newQuestions[index - 1], newQuestions[index]] = [newQuestions[index], newQuestions[index - 1]];
    setQuestions(newQuestions);
    // Call backend to reorder
    try {
      await reorderQuestions(fiche.id, newQuestions.map(q => q.id));
    } finally {
      setReorderingQuestionId(null);
    }
  };

  const handleMoveDown = async (index) => {
    if (index === questions.length - 1) return;
    setReorderingQuestionId(questions[index].id);
    const newQuestions = [...questions];
      [newQuestions[index], newQuestions[index + 1]] = [newQuestions[index + 1], newQuestions[index]];
    setQuestions(newQuestions);
    // Call backend to reorder
    try {
      await reorderQuestions(fiche.id, newQuestions.map(q => q.id));
    } finally {
      setReorderingQuestionId(null);
    }
  };

  if (!fiche) {
    return (
      <div className="flex justify-center items-center h-48">
        <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
        <p className="text-gray-500 ml-2">Chargement de la fiche de travail...</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      {/* Section 1: Caractéristiques de la fiche de travail */}
      <div className="border rounded-lg shadow-sm">
        <button
          type="button"
          className="w-full flex items-center p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-lg"
          onClick={() => setIsCaracteristiquesOpen((v) => !v)}
        >
          <div className="flex items-center gap-2">
            {isCaracteristiquesOpen ? (
              <ChevronUp className="h-5 w-5 text-blue-600" />
            ) : (
              <ChevronDown className="h-5 w-5 text-blue-600" />
            )}
            <span className="text-lg font-medium text-blue-800">Caractéristiques de la Fiche de Travail</span>
          </div>
        </button>
        {isCaracteristiquesOpen && (
          <div className="p-5 bg-white space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Nom de la Fiche de Travail</Label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  placeholder="Entrez le nom de la fiche"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="auditMissionID">Mission d'Audit Associée</Label>
                <Input
                  id="auditMissionID"
                  name="auditMissionID"
                  value={fiche?.auditMission?.name || "N/A"}
                  onChange={handleInputChange}
                  placeholder="Nom de la mission d'audit"
                  disabled
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="auditActivityID">Activité d'Audit Associée</Label>
                <Input
                  id="auditActivityID"
                  name="auditActivityID"
                  value={fiche?.auditActivity?.name || "N/A"}
                  onChange={handleInputChange}
                  placeholder="Nom de l'activité d'audit"
                  disabled
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="tailleEchantillon">Taille de l'échantillon</Label>
                <Input
                  id="tailleEchantillon"
                  name="tailleEchantillon"
                  value={formData.tailleEchantillon}
                  onChange={handleInputChange}
                  placeholder="Ex: 100"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="tacheDetail">Détail de la Tâche</Label>
              <Textarea
                id="tacheDetail"
                name="tacheDetail"
                value={formData.tacheDetail}
                onChange={handleInputChange}
                placeholder="Décrivez les détails de la tâche"
                rows="3"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="commentaire">Commentaire</Label>
              <Textarea
                id="commentaire"
                name="commentaire"
                value={formData.commentaire}
                onChange={handleInputChange}
                placeholder="Ajoutez un commentaire"
                rows="3"
              />
            </div>
          </div>
        )}
      </div>

      {/* Section 2: Questionnaire */}
      <div className="border rounded-lg shadow-sm mt-6">
        <button
          type="button"
          className="w-full flex items-center p-4 bg-gradient-to-r from-green-50 to-teal-50 rounded-t-lg"
          onClick={() => setIsQuestionnaireOpen((v) => !v)}
        >
          <div className="flex items-center gap-2">
            {isQuestionnaireOpen ? (
              <ChevronUp className="h-5 w-5 text-green-600" />
            ) : (
              <ChevronDown className="h-5 w-5 text-green-600" />
            )}
            <span className="text-lg font-medium text-green-800">Questionnaire</span>
          </div>
        </button>
        {isQuestionnaireOpen && (
          <div className="p-5 bg-white space-y-6">
            <h4 className="text-md font-semibold text-gray-700 mb-4">Ajouter une nouvelle question</h4>
            <div className="space-y-4 border p-4 rounded-lg bg-gray-50">
              <div className="space-y-2">
                <Label htmlFor="new-question-text">Texte de la question</Label>
                <Input
                  id="new-question-text"
                  name="question_text"
                  value={newQuestion.question_text}
                  onChange={handleQuestionInputChange}
                  placeholder="Ex: Quel est le risque associé ?"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="input-type">Type de réponse</Label>
                <Select
                  value={newQuestion.input_type}
                  onValueChange={(value) => setNewQuestion((prev) => ({ ...prev, input_type: value, options: [] }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionnez un type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="text">Texte libre</SelectItem>
                    <SelectItem value="number">Nombre</SelectItem>
                    <SelectItem value="date">Date</SelectItem>
                    <SelectItem value="radio">Choix unique (radio)</SelectItem>
                    <SelectItem value="multi-select">Choix multiple (checkbox)</SelectItem>
                    <SelectItem value="document">Document métier</SelectItem>
                    <SelectItem value="reference">Référence</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {["radio", "select", "multi-select"].includes(newQuestion.input_type) && (
                <div className="space-y-2">
                  <Label htmlFor="option-input">Options de réponse</Label>
                  <div className="flex gap-2">
                    <Input
                      id="option-input"
                      value={option}
                      onChange={(e) => setOption(e.target.value)}
                      placeholder="Ajouter une option"
                    />
                    <Button type="button" onClick={handleAddOption} variant="outline">
                      <Plus className="h-4 w-4 mr-2" /> Ajouter
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {newQuestion.options.map((opt, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10"
                      >
                        {opt}
                        <button
                          type="button"
                          onClick={() => handleRemoveOption(index)}
                          className="ml-1 -mr-0.5 h-4 w-4 flex-shrink-0 rounded-full hover:bg-blue-200 inline-flex items-center justify-center text-blue-400"
                        >
                          <Trash2 className="h-3 w-3" />
                        </button>
                      </span>
                    ))}
                  </div>
                </div>
              )}
              <Button onClick={handleAddQuestion} disabled={isSaving} className="w-full">
                {isSaving ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : <Plus className="h-4 w-4 mr-2" />}
                {isSaving && deletingQuestionId === null ? "Ajout..." : "Ajouter la question"}
              </Button>
            </div>

            <h4 className="text-md font-semibold text-gray-700 mb-4">Questions existantes</h4>
            {questions.length === 0 ? (
              <p className="text-sm text-gray-500">Aucune question ajoutée pour le moment.</p>
            ) : (
              <div className="space-y-4" ref={questionnaireRef}>
                {questions.map((question, index) => (
                  <Card key={question.id} className="relative">
                    <CardContent className="p-4">
                      <div className="flex justify-between items-center">
                        <p className="font-medium">
                          {index + 1}. {question.question_text} ({question.input_type})
                        </p>
                        <div className="flex items-center space-x-2">
                          {question.input_type !== 'text' && question.input_type !== 'number' && question.input_type !== 'date' && (
                            <span className="text-xs text-gray-500">Options: {question.options?.join(", ")}</span>
                          )}
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleMoveUp(index)}
                            disabled={index === 0 || reorderingQuestionId === question.id || isSaving}
                            title="Déplacer vers le haut"
                          >
                            <ArrowUp className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleMoveDown(index)}
                            disabled={index === questions.length - 1 || reorderingQuestionId === question.id || isSaving}
                            title="Déplacer vers le bas"
                          >
                            <ArrowDown className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleDeleteQuestion(question.id)}
                            disabled={deletingQuestionId === question.id || isSaving}
                            title="Supprimer la question"
                          >
                            {deletingQuestionId === question.id ? (
                              <Loader2 className="h-4 w-4 animate-spin text-red-500" />
                            ) : (
                              <Trash2 className="h-4 w-4 text-red-500" />
                            )}
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Section 3: Pièces jointes */}
      <div className="border rounded-lg shadow-sm mt-6">
        <button
          type="button"
          className="w-full flex items-center p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-t-lg"
          onClick={() => setIsAttachmentsOpen((v) => !v)}
        >
          <div className="flex items-center gap-2">
            {isAttachmentsOpen ? (
              <ChevronUp className="h-5 w-5 text-purple-600" />
            ) : (
              <ChevronDown className="h-5 w-5 text-purple-600" />
            )}
            <Paperclip className="h-5 w-5 text-purple-600" />
            <span className="text-lg font-medium text-purple-800">Pièces jointes</span>
          </div>
        </button>
        {isAttachmentsOpen && (
          <div className="p-5 bg-white space-y-6">
            <AuditTravailFicheAttachmentsSection ficheTravail={fiche} />
          </div>
        )}
      </div>
    </div>
  );
}

export default FichesTravailCaracteristiquesTab;