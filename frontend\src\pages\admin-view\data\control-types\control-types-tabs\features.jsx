import { useState, useEffect } from "react";
import { useNavigate, useOutletContext } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { Loader2 } from "lucide-react";
import { updateControlType, getAllControlTypes } from "@/store/slices/controlTypeSlice";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { useTranslation } from "react-i18next";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

function ControlTypesFeatures() {
  const { controlType } = useOutletContext();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { t } = useTranslation();

  const { isLoading, controlTypes } = useSelector((state) => state.controlType);

  const [formData, setFormData] = useState({
    name: "",
    code: "",
    comment: "",
    parentControlTypeID: "none",
  });

  // Initialize form data when control type is loaded
  useEffect(() => {
    if (controlType) {
      // Use null check instead of falsy check to handle empty string vs null correctly
      const parentID = controlType.parentControlTypeID !== null && controlType.parentControlTypeID !== undefined
        ? controlType.parentControlTypeID
        : "none";

      setFormData({
        name: controlType.name || "",
        code: controlType.code || "",
        comment: controlType.comment || "",
        parentControlTypeID: parentID,
      });
    }
  }, [controlType]);

  // Fetch control types if they're not already loaded
  useEffect(() => {
    const shouldFetch = !controlTypes || controlTypes.length === 0;
    if (shouldFetch) {
      dispatch(getAllControlTypes());
    }
  }, [dispatch]);

  // Monitor form data changes
  useEffect(() => {
    // This effect is intentionally left empty to avoid unnecessary logging
  }, [formData]);

  // Get parent control type name
  const getParentControlTypeName = (parentId) => {
    if (!parentId || parentId === "none") return "None";

    const parent = controlTypes?.find(type => type.controlTypeID === parentId);
    return parent ? parent.name : "Unknown";
  };

  // Handle form input changes
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle select changes
  const handleSelectChange = (name, value) => {
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      // Prepare data for submission
      const updateData = {
        name: formData.name,
        code: formData.code,
        comment: formData.comment,
        // Handle empty strings and "none" value by converting to null
        parentControlTypeID: (!formData.parentControlTypeID || formData.parentControlTypeID === "" || formData.parentControlTypeID === "none") ? null : formData.parentControlTypeID,
      };

      console.log("Form data before submission:", formData);
      console.log("Sanitized data for submission:", updateData);

      // Data is ready for submission

      // Check if trying to set parent to self
      if (updateData.parentControlTypeID === controlType.controlTypeID) {
        toast.error(t('admin.control_types.error.self_parent', 'A control type cannot be its own parent'));
        return;
      }

      // Ready to update control type

      await dispatch(updateControlType({
        id: controlType.controlTypeID,
        controlTypeData: updateData
      })).unwrap();

      toast.success(t('admin.control_types.success.updated', 'Control type updated successfully'));
      navigate(`/admin/data/control-types/${controlType.controlTypeID}`);
    } catch (error) {
      console.error("Update error:", error);

      // Check for foreign key constraint error
      if (error.name === 'SequelizeForeignKeyConstraintError' ||
          (error.response?.data?.name === 'SequelizeForeignKeyConstraintError') ||
          (error.response?.data?.error && error.response?.data?.error.includes('foreign key constraint'))) {
        toast.error(t('admin.control_types.error.invalid_parent', "Invalid parent control type. Please select a valid parent or 'None'."));
      } else if (error.response?.status === 500) {
        toast.error(t('admin.control_types.error.server_error', 'Server error. Please check the console for details.'));
      } else {
        toast.error(error?.message || t('admin.control_types.error.update_failed_toast', 'Failed to update control type'));
      }
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <h2 className="text-xl font-semibold mb-6">{t('admin.control_types.features.title', 'Edit Control Type')}</h2>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Name Field */}
        <div className="space-y-2">
          <Label htmlFor="name">{t('admin.control_types.form.name', 'Name')}</Label>
          <Input
            id="name"
            name="name"
            value={formData.name}
            onChange={handleChange}
            required
          />
        </div>

        {/* Code Field */}
        <div className="space-y-2">
          <Label htmlFor="code">{t('admin.control_types.form.code', 'Code')}</Label>
          <Input
            id="code"
            name="code"
            value={formData.code}
            onChange={handleChange}
          />
        </div>
      </div>

      {/* Parent Control Type Field */}
      <div className="space-y-2">
        <Label htmlFor="parentControlTypeID">{t('admin.control_types.form.parent', 'Parent Control Type')}</Label>
        {/* Custom Select implementation */}
        <div className="relative">
          <select
            id="parentControlTypeID"
            className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
            value={formData.parentControlTypeID || "none"}
            onChange={(e) => handleSelectChange("parentControlTypeID", e.target.value)}
          >
            <option value="none">{t('admin.control_types.form.none', 'None')}</option>
            {controlTypes && controlTypes
              .filter(type => type.controlTypeID !== controlType.controlTypeID)
              .map(type => (
                <option key={type.controlTypeID} value={type.controlTypeID}>
                  {type.name}
                </option>
              ))}
          </select>
        </div>
      </div>

      {/* Comment Field */}
      <div className="space-y-2">
        <Label htmlFor="comment">{t('admin.control_types.form.comment', 'Comment')}</Label>
        <Textarea
          id="comment"
          name="comment"
          value={formData.comment}
          onChange={handleChange}
          rows={4}
        />
      </div>

      {/* Submit Button */}
      <div className="flex justify-end space-x-4">
        <Button
          type="button"
          variant="outline"
          onClick={() => navigate(`/admin/data/control-types/${controlType.controlTypeID}`)}
        >
          {t('common.buttons.cancel', 'Cancel')}
        </Button>
        <Button
          type="submit"
          disabled={isLoading}
          className="bg-[#F62D51] hover:bg-[#F62D51]/90 text-white"
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {t('common.saving', 'Saving...')}
            </>
          ) : (
            t('common.buttons.save', 'Save Changes')
          )}
        </Button>
      </div>
    </form>
  );
}

export default ControlTypesFeatures;
