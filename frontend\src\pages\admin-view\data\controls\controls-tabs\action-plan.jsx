import { useState, useEffect } from "react";
import { useOutletContext } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import { getAllActionPlans } from "@/store/slices/actionPlanSlice";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { getApiBaseUrl } from "@/utils/api-config";
import { Loader2, Plus, Link as LinkIcon } from "lucide-react";
import { toast } from "sonner";
import axios from "axios";
import { useTranslation } from "react-i18next";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { getControlById, updateControl } from "@/store/slices/controlSlice";

function ControlActionPlan() {
  const { control } = useOutletContext();
  const dispatch = useDispatch();
  const API_BASE_URL = getApiBaseUrl();
  const { t } = useTranslation();
  // Add debug logging for control data
  useEffect(() => {
    console.log("Control data in ControlActionPlan:", control);
  }, [control]);

  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isLinkModalOpen, setIsLinkModalOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [linkedActionPlan, setLinkedActionPlan] = useState(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [newActionPlan, setNewActionPlan] = useState({
    name: "",
    nature: "",
    comment: ""
  });

  // Get action plans from Redux
  const { actionPlans, isLoading: actionPlansLoading } = useSelector((state) => state.actionPlan);

  // Fetch action plans when component mounts
  useEffect(() => {
    dispatch(getAllActionPlans());
  }, [dispatch]);

  // Find the linked action plan if any
  useEffect(() => {
    if (control?.implementingActionPlan && actionPlans?.length > 0) {
      const plan = actionPlans.find(plan => plan.actionPlanID === control.implementingActionPlan);
      setLinkedActionPlan(plan || null);
    } else {
      setLinkedActionPlan(null);
    }
  }, [control, actionPlans]);

  // Handle create action plan form input changes
  const handleCreateInputChange = (e) => {
    const { name, value } = e.target;
    setNewActionPlan(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle select input changes
  const handleSelectChange = (name, value) => {
    setNewActionPlan(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle create action plan form submission
  const handleCreateActionPlan = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Prepare the data for submission
      const actionPlanData = {
        ...newActionPlan,
        actionPlanID: `AP_${Date.now()}`, // Generate ID
      };

      // Create the action plan
      const response = await axios.post(
        `${API_BASE_URL}/actionPlans`,
        actionPlanData,
        {
          withCredentials: true,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.data.success) {
        // Link the action plan to the control
        await linkActionPlanToControl(response.data.data.actionPlanID);

        // Show success message
        toast.success(t('admin.controls.action_plan.success.created_linked', 'Action plan created and linked successfully'));

        // Reset form and close modal
        setNewActionPlan({
          name: "",
          nature: "",
          comment: ""
        });
        setIsCreateModalOpen(false);

        // Refresh action plans list
        dispatch(getAllActionPlans());
      }
    } catch (error) {
      console.error(t('admin.controls.action_plan.error.create', 'Error creating action plan:'), error);
      toast.error(error?.response?.data?.message || error?.message || t('admin.controls.action_plan.error.create_failed', 'Failed to create action plan'));
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle link action plan to control
  const handleLinkActionPlan = async (actionPlanID) => {
    setIsSubmitting(true);
    try {
      await linkActionPlanToControl(actionPlanID);
      setIsLinkModalOpen(false);
      toast.success(t('admin.controls.action_plan.success.linked', 'Action plan linked successfully'));
    } catch (error) {
      console.error(t('admin.controls.action_plan.error.link', 'Error linking action plan:'), error);
      toast.error(error?.response?.data?.message || error?.message || t('admin.controls.action_plan.error.link_failed', 'Failed to link action plan'));
    } finally {
      setIsSubmitting(false);
    }
  };

  // Helper function to link action plan to control
  const linkActionPlanToControl = async (actionPlanID) => {
    if (!control || !control.controlID) {
      console.error(t('admin.controls.action_plan.error.control_missing', 'Control not available or missing controlID'));
      throw new Error(t('admin.controls.action_plan.error.control_refresh', 'Control not available. Please try refreshing the page.'));
    }

    try {
      // Use Redux to update the control
      await dispatch(updateControl({
        id: control.controlID,
        controlData: { ...control, implementingActionPlan: actionPlanID }
      })).unwrap();

      // Refresh control data
      await dispatch(getControlById(control.controlID)).unwrap();

      // Wait a moment to ensure state is updated before any possible navigation
      await new Promise(resolve => setTimeout(resolve, 100));

      return true;
    } catch (error) {
      console.error(t('admin.controls.action_plan.error.link', 'Error linking action plan:'), error);
      throw new Error(error.message || t('admin.controls.action_plan.error.link_failed', 'Failed to link action plan'));
    }
  };

  // Handle unlink action plan from control
  const handleUnlinkActionPlan = async () => {
    if (!control || !control.controlID) {
      toast.error(t('admin.controls.action_plan.error.control_refresh', 'Control not available. Please try refreshing the page.'));
      return;
    }

    if (!window.confirm(t('admin.controls.action_plan.confirm.unlink', 'Are you sure you want to unlink this action plan?'))) {
      return;
    }

    try {
      setIsSubmitting(true);

      // Use Redux to update the control
      await dispatch(updateControl({
        id: control.controlID,
        controlData: { ...control, implementingActionPlan: null }
      })).unwrap();

      // Refresh control data
      await dispatch(getControlById(control.controlID)).unwrap();

      // Wait a moment to ensure state is updated
      await new Promise(resolve => setTimeout(resolve, 100));

      toast.success(t('admin.controls.action_plan.success.unlinked', 'Action plan unlinked successfully'));
      setLinkedActionPlan(null);
    } catch (error) {
      console.error(t('admin.controls.action_plan.error.unlink', 'Error unlinking action plan:'), error);
      toast.error(error?.response?.data?.message || error?.message || t('admin.controls.action_plan.error.unlink_failed', 'Failed to unlink action plan'));
    } finally {
      setIsSubmitting(false);
    }
  };

  if (actionPlansLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="flex flex-col items-center">
          <Loader2 className="h-12 w-12 animate-spin text-[#F62D51]" />
          <span className="mt-4 text-gray-600">{t('admin.controls.action_plan.loading', 'Loading action plans...')}</span>
        </div>
      </div>
    );
  }

  // Add a check for control availability
  if (!control || !control.controlID) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
        <h3 className="text-lg font-medium text-yellow-800">{t('admin.controls.action_plan.error.data_not_available', 'Control data not available')}</h3>
        <p className="mt-2 text-sm text-yellow-700">
          {t('admin.controls.action_plan.error.data_refresh', 'The control data is not properly loaded. Please try refreshing the page.')}
        </p>
      </div>
    );
  }

  // Filter action plans based on search query
  const filteredActionPlans = actionPlans?.filter(plan =>
    plan.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (plan.comment && plan.comment.toLowerCase().includes(searchQuery.toLowerCase()))
  ) || [];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">{t('admin.controls.action_plan.title', 'Control Action Plan')}</h2>

        {/* Only show top buttons when there is no linked action plan to avoid duplication */}
        {!linkedActionPlan && (
          <div className="flex gap-2">
            <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
              <DialogTrigger asChild>
                <Button className="bg-[#F62D51]/90 hover:bg-[#F62D51] text-white">
                  <Plus className="h-4 w-4 mr-2" />
                  {t('admin.controls.action_plan.buttons.create', 'Create Action Plan')}
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle>{t('admin.controls.action_plan.dialog.create_title', 'Create New Action Plan')}</DialogTitle>
                  <DialogDescription>
                    {t('admin.controls.action_plan.dialog.create_description', 'Fill in the details below to create a new action plan.')}
                  </DialogDescription>
                </DialogHeader>
                <form onSubmit={handleCreateActionPlan} className="space-y-4 mt-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">{t('admin.controls.action_plan.form.name', 'Name')} *</Label>
                    <Input
                      id="name"
                      name="name"
                      value={newActionPlan.name}
                      onChange={handleCreateInputChange}
                      placeholder={t('admin.controls.action_plan.form.name_placeholder', 'Enter action plan name')}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="nature">{t('admin.controls.action_plan.form.nature', 'Nature')}</Label>
                    <Select
                      value={newActionPlan.nature}
                      onValueChange={(value) => handleSelectChange("nature", value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder={t('admin.controls.action_plan.form.nature_placeholder', 'Select nature')} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="preventive">{t('admin.controls.action_plan.form.preventive', 'Preventive')}</SelectItem>
                        <SelectItem value="corrective">{t('admin.controls.action_plan.form.corrective', 'Corrective')}</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="comment">{t('admin.controls.action_plan.form.comment', 'Comment')}</Label>
                    <Textarea
                      id="comment"
                      name="comment"
                      value={newActionPlan.comment}
                      onChange={handleCreateInputChange}
                      placeholder={t('admin.controls.action_plan.form.comment_placeholder', 'Enter comment')}
                      rows={3}
                    />
                  </div>
                  <div className="flex justify-end gap-3 mt-6">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setIsCreateModalOpen(false)}
                    >
                      {t('common.buttons.cancel', 'Cancel')}
                    </Button>
                    <Button
                      type="submit"
                      className="bg-[#F62D51] hover:bg-[#F62D51]/90 text-white"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          {t('common.creating', 'Creating...')}
                        </>
                      ) : (
                        t('admin.controls.action_plan.buttons.create', 'Create Action Plan')
                      )}
                    </Button>
                  </div>
                </form>
              </DialogContent>
            </Dialog>

            <Dialog open={isLinkModalOpen} onOpenChange={setIsLinkModalOpen}>
              <DialogTrigger asChild>
                <Button variant="outline">
                  <LinkIcon className="h-4 w-4 mr-2" />
                  {t('admin.controls.action_plan.buttons.link', 'Link Action Plan')}
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>{t('admin.controls.action_plan.dialog.link_title', 'Link Existing Action Plan')}</DialogTitle>
                  <DialogDescription>
                    {t('admin.controls.action_plan.dialog.link_description', 'Select an action plan to link to this control.')}
                  </DialogDescription>
                </DialogHeader>
                <div className="mt-4">
                  <div className="mb-4">
                    <Input
                      placeholder={t('admin.controls.action_plan.search_placeholder', 'Search action plans...')}
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-full"
                    />
                  </div>

                  {isSubmitting ? (
                    <div className="flex items-center justify-center py-8">
                      <Loader2 className="h-8 w-8 animate-spin text-[#F62D51] mr-2" />
                      <span className="text-gray-500">{t('admin.controls.action_plan.linking', 'Linking action plan...')}</span>
                    </div>
                  ) : (
                    <div className="max-h-[400px] overflow-y-auto">
                      {filteredActionPlans.length > 0 ? (
                        <div className="grid gap-3">
                          {filteredActionPlans.map((plan) => (
                            <div
                              key={plan.actionPlanID}
                              className="border rounded-lg p-4 hover:bg-gray-50 cursor-pointer transition-colors"
                              onClick={() => handleLinkActionPlan(plan.actionPlanID)}
                            >
                              <div className="flex justify-between items-start">
                                <div>
                                  <h3 className="font-medium text-[#242A33]">{plan.name}</h3>
                                  <p className="text-sm text-gray-500 mt-1">
                                    {plan.nature ? `${t('admin.controls.action_plan.form.nature', 'Nature')}: ${t(`admin.controls.action_plan.form.${plan.nature}`, plan.nature)}` : t('admin.controls.action_plan.no_nature', 'No nature specified')}
                                  </p>
                                </div>
                                <span className="text-xs text-gray-400">{plan.actionPlanID}</span>
                              </div>
                              {plan.comment && (
                                <p className="text-sm mt-2 text-gray-600 line-clamp-2">{plan.comment}</p>
                              )}
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-8 text-gray-500">
                          {searchQuery ? t('admin.controls.action_plan.no_search_results', 'No action plans match your search') : t('admin.controls.action_plan.no_plans', 'No action plans available')}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </DialogContent>
            </Dialog>
          </div>
        )}
      </div>

      <div className="bg-white rounded-lg shadow-sm">
        {linkedActionPlan ? (
          <div className="p-5 border border-gray-200 rounded-lg">
            <div className="flex justify-between items-start mb-4">
              <div>
                <h3 className="font-medium text-lg text-[#242A33]">{linkedActionPlan.name}</h3>
                <div className="flex items-center mt-1">
                  {linkedActionPlan.nature && (
                    <span className="text-xs text-gray-500 mr-3">
                      {t('admin.controls.action_plan.form.nature', 'Nature')}: {t(`admin.controls.action_plan.form.${linkedActionPlan.nature}`, linkedActionPlan.nature)}
                    </span>
                  )}
                  <span className="text-xs text-gray-500">{t('admin.controls.action_plan.id', 'ID')}: {linkedActionPlan.actionPlanID}</span>
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                className="text-red-500 border-red-200 hover:bg-red-50 hover:text-red-600"
                onClick={handleUnlinkActionPlan}
              >
                {t('admin.controls.action_plan.buttons.unlink', 'Unlink')}
              </Button>
            </div>

            {linkedActionPlan.comment && (
              <div>
                <h4 className="text-sm font-medium text-gray-500 mb-1">{t('admin.controls.action_plan.form.comment', 'Comment')}</h4>
                <p className="text-[#242A33]">{linkedActionPlan.comment}</p>
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-8 px-6">
            <h3 className="text-lg font-medium text-[#242A33] mb-2">{t('admin.controls.action_plan.no_linked', 'No Action Plan Linked')}</h3>
            <p className="text-gray-500 mb-6 max-w-md mx-auto">
              {t('admin.controls.action_plan.no_linked_description', 'Link an existing action plan or create a new one to help implement this control.')}
            </p>
            <div className="flex justify-center gap-4">
              <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
                <DialogTrigger asChild>
                  <Button className="bg-[#F62D51]/90 hover:bg-[#F62D51] text-white">
                    <Plus className="h-4 w-4 mr-2" />
                    {t('admin.controls.action_plan.buttons.create', 'Create Action Plan')}
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-md">
                  <DialogHeader>
                    <DialogTitle>{t('admin.controls.action_plan.dialog.create_title', 'Create New Action Plan')}</DialogTitle>
                    <DialogDescription>
                      {t('admin.controls.action_plan.dialog.create_description', 'Fill in the details below to create a new action plan.')}
                    </DialogDescription>
                  </DialogHeader>
                  <form onSubmit={handleCreateActionPlan} className="space-y-4 mt-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">{t('admin.controls.action_plan.form.name', 'Name')} *</Label>
                      <Input
                        id="name"
                        name="name"
                        value={newActionPlan.name}
                        onChange={handleCreateInputChange}
                        placeholder={t('admin.controls.action_plan.form.name_placeholder', 'Enter action plan name')}
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="nature">{t('admin.controls.action_plan.form.nature', 'Nature')}</Label>
                      <Select
                        value={newActionPlan.nature}
                        onValueChange={(value) => handleSelectChange("nature", value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder={t('admin.controls.action_plan.form.nature_placeholder', 'Select nature')} />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="preventive">{t('admin.controls.action_plan.form.preventive', 'Preventive')}</SelectItem>
                          <SelectItem value="corrective">{t('admin.controls.action_plan.form.corrective', 'Corrective')}</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="comment">{t('admin.controls.action_plan.form.comment', 'Comment')}</Label>
                      <Textarea
                        id="comment"
                        name="comment"
                        value={newActionPlan.comment}
                        onChange={handleCreateInputChange}
                        placeholder={t('admin.controls.action_plan.form.comment_placeholder', 'Enter comment')}
                        rows={3}
                      />
                    </div>
                    <div className="flex justify-end gap-3 mt-6">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => setIsCreateModalOpen(false)}
                      >
                        {t('common.buttons.cancel', 'Cancel')}
                      </Button>
                      <Button
                        type="submit"
                        className="bg-[#F62D51] hover:bg-[#F62D51]/90 text-white"
                        disabled={isSubmitting}
                      >
                        {isSubmitting ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            {t('common.creating', 'Creating...')}
                          </>
                        ) : (
                          t('admin.controls.action_plan.buttons.create', 'Create Action Plan')
                        )}
                      </Button>
                    </div>
                  </form>
                </DialogContent>
              </Dialog>

              <Dialog open={isLinkModalOpen} onOpenChange={setIsLinkModalOpen}>
                <DialogTrigger asChild>
                  <Button variant="outline">
                    <LinkIcon className="h-4 w-4 mr-2" />
                    Link Action Plan
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl">
                  <DialogHeader>
                    <DialogTitle>Link Existing Action Plan</DialogTitle>
                    <DialogDescription>
                      Select an action plan to link to this control.
                    </DialogDescription>
                  </DialogHeader>
                  <div className="mt-4">
                    <div className="mb-4">
                      <Input
                        placeholder="Search action plans..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="w-full"
                      />
                    </div>

                    {isSubmitting ? (
                      <div className="flex items-center justify-center py-8">
                        <Loader2 className="h-8 w-8 animate-spin text-[#F62D51] mr-2" />
                        <span className="text-gray-500">Linking action plan...</span>
                      </div>
                    ) : (
                      <div className="max-h-[400px] overflow-y-auto">
                        {filteredActionPlans.length > 0 ? (
                          <div className="grid gap-3">
                            {filteredActionPlans.map((plan) => (
                              <div
                                key={plan.actionPlanID}
                                className="border rounded-lg p-4 hover:bg-gray-50 cursor-pointer transition-colors"
                                onClick={() => handleLinkActionPlan(plan.actionPlanID)}
                              >
                                <div className="flex justify-between items-start">
                                  <div>
                                    <h3 className="font-medium text-[#242A33]">{plan.name}</h3>
                                    <p className="text-sm text-gray-500 mt-1">
                                      {plan.nature ? `Nature: ${plan.nature}` : 'No nature specified'}
                                    </p>
                                  </div>
                                  <span className="text-xs text-gray-400">{plan.actionPlanID}</span>
                                </div>
                                {plan.comment && (
                                  <p className="text-sm mt-2 text-gray-600 line-clamp-2">{plan.comment}</p>
                                )}
                              </div>
                            ))}
                          </div>
                        ) : (
                          <div className="text-center py-8 text-gray-500">
                            {searchQuery ? 'No action plans match your search' : 'No action plans available'}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default ControlActionPlan;