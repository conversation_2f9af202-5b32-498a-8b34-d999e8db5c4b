import { useState, useEffect } from "react";
import axios from "axios";
import { useSelector } from "react-redux";
import { toast } from "sonner";
import { Loader2, UserPlus, UserX, Users } from "lucide-react";
import { Button } from "../ui/button";
import { <PERSON><PERSON>, <PERSON>alogContent, <PERSON>alogFooter, DialogHeader, DialogTitle, DialogTrigger } from "../ui/dialog";
import { ScrollArea } from "../ui/scroll-area";
import { hasPermission } from "@/store/auth-slice";
import { getApiBaseUrl } from "@/utils/api-config";
import { useTranslation } from 'react-i18next';

function ContributorsSection({ riskId }) {
  const { t } = useTranslation();
  const [contributors, setContributors] = useState([]);
  const [users, setUsers] = useState([]);
  const [selectedUser, setSelectedUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [open, setOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const state = useSelector(state => state || {});
  const canAssignContributors = state && hasPermission(state, 'update');

  const API_BASE_URL = getApiBaseUrl();

  // Safeguard for null riskId
  const safeRiskId = riskId || '';

  // Fetch contributors on mount and when riskId changes
  useEffect(() => {
    if (safeRiskId) {
      fetchContributors();
    }
  }, [safeRiskId]);

  const fetchContributors = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`${API_BASE_URL}/risks/${safeRiskId}/contributors`, {
        withCredentials: true,
        headers: { "Content-Type": "application/json" }
      });

      if (response.data.success) {
        console.log('Contributors data received:', JSON.stringify(response.data.data));
        setContributors(response.data.data || []);
      }
    } catch (error) {
      console.error("Error loading contributors:", error);
      toast.error(t('admin.risks.contributors.error_loading', "Failed to load contributors"));
    } finally {
      setLoading(false);
    }
  };

  const fetchUsers = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/users`, {
        withCredentials: true,
        headers: { "Content-Type": "application/json" }
      });

      if (response.data.success) {
        // Filter out users who are already contributors
        const currentContributorIds = contributors.map(c =>
          c.user_id || (c.contributor && c.contributor.id) || ''
        ).filter(id => id); // Remove any null/undefined/empty values

        const availableUsers = response.data.data.filter(user => {
          const userId = user.id || user.userId || '';
          return userId && !currentContributorIds.includes(userId);
        });

        setUsers(availableUsers);
      }
    } catch (error) {
      console.error("Error loading users:", error);
      toast.error(t('admin.risks.contributors.error_loading_users', "Failed to load users"));
    }
  };

  const handleAssignContributor = async () => {
    if (!selectedUser) {
      toast.error(t('admin.risks.contributors.select_user', "Please select a user to assign"));
      return;
    }

    if (!safeRiskId) {
      toast.error(t('admin.risks.contributors.no_risk', "No risk selected"));
      return;
    }

    try {
      setIsSubmitting(true);
      const userId = selectedUser.id || selectedUser.userId || '';

      if (!userId) {
        toast.error(t('admin.risks.contributors.invalid_user', "Invalid user selected"));
        return;
      }

      const response = await axios.post(
        `${API_BASE_URL}/risks/${safeRiskId}/contributors`,
        { userId },
        {
          withCredentials: true,
          headers: { "Content-Type": "application/json" }
        }
      );

      if (response.data.success) {
        fetchContributors();
        setOpen(false);
        setSelectedUser(null);
      }
    } catch (error) {
      console.error("Error assigning contributor:", error);
      toast.error(error.response?.data?.message || t('admin.risks.contributors.error_assign', "Failed to assign contributor"));
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleRemoveContributor = async (contributorId) => {
    if (!contributorId) {
      toast.error(t('admin.risks.contributors.invalid_contributor', "Invalid contributor"));
      return;
    }

    if (!safeRiskId) {
      toast.error(t('admin.risks.contributors.no_risk', "No risk selected"));
      return;
    }

    if (!window.confirm(t('admin.risks.contributors.remove_confirm', "Are you sure you want to remove this contributor?"))) {
      return;
    }

    try {
      const response = await axios.delete(
        `${API_BASE_URL}/risks/${safeRiskId}/contributors/${contributorId}`,
        {
          withCredentials: true,
          headers: { "Content-Type": "application/json" }
        }
      );

      if (response.data.success) {
        fetchContributors();
      }
    } catch (error) {
      console.error("Error removing contributor:", error);
      toast.error(error.response?.data?.message || t('admin.risks.contributors.error_remove', "Failed to remove contributor"));
    }
  };

  return (
    <div>
      {loading ? (
        <div className="flex justify-center items-center h-32">
          <Loader2 className="h-6 w-6 animate-spin text-gray-500" />
          <span className="ml-2">{t('admin.risks.contributors.loading', 'Loading contributors...')}</span>
        </div>
      ) : (
        <div className="space-y-2">
          {contributors.length === 0 ? (
            <p className="text-sm text-gray-500 py-4">{t('admin.risks.contributors.no_contributors', 'No contributors assigned to this risk.')}</p>
          ) : (
            contributors.map(contributor => {
              const contributorId = contributor.user_id || (contributor.contributor && contributor.contributor.id) || '';
              const username = contributor.contributor?.username || t('admin.risks.contributors.unknown_user', 'Unknown User');
              const email = contributor.contributor?.email || '';
              const assignerUsername = contributor.assigner?.username || '';

              return (
                <div
                  key={contributorId || `temp-${Math.random()}`}
                  className="flex items-center justify-between p-3 rounded-md border border-gray-100 hover:bg-gray-50"
                >
                  <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-700">
                      {username.charAt(0) || "U"}
                    </div>
                    <div className="ml-3">
                      <p className="font-medium text-gray-800">{username}</p>
                      {email && <p className="text-xs text-gray-500">{email}</p>}
                      {assignerUsername && (
                        <p className="text-xs text-gray-400">
                          {t('admin.risks.contributors.added_by', 'Added by')}: {assignerUsername}
                        </p>
                      )}
                    </div>
                  </div>

                  {canAssignContributors ? (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveContributor(contributorId)}
                      className="text-red-600 hover:text-red-800 hover:bg-red-50"
                      disabled={!contributorId}
                    >
                      <UserX className="h-4 w-4" />
                    </Button>
                  ) : (
                    <Button
                      variant="ghost"
                      size="sm"
                      disabled
                      title={t('admin.risks.contributors.no_permission_remove', "You don't have permission to remove contributors")}
                      className="text-gray-400 cursor-not-allowed"
                    >
                      <UserX className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              );
            })
          )}

          {canAssignContributors ? (
            <Dialog open={open} onOpenChange={(isOpen) => {
              setOpen(isOpen);
              if (isOpen) fetchUsers();
            }}>
              <DialogTrigger asChild>
                <Button
                  className="bg-blue-600 hover:bg-blue-700 mt-3"
                  size="sm"
                  disabled={!safeRiskId}
                >
                  <UserPlus className="h-4 w-4 mr-1" />
                  {t('admin.risks.contributors.assign_contributor', 'Assign Contributor')}
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>{t('admin.risks.contributors.assign_title', 'Assign Risk Contributor')}</DialogTitle>
                </DialogHeader>

                <div className="py-4">
                  <p className="mb-4 text-sm text-gray-600">
                    {t('admin.risks.contributors.assign_description', 'Select a user to assign as a contributor to this risk:')}
                  </p>

                  <ScrollArea className="h-[200px] rounded-md border p-2">
                    <div className="space-y-1">
                      {users.map(user => {
                        const userId = user.id || user.userId || '';
                        const username = user.username || 'Unknown User';
                        const email = user.email || '';

                        return (
                          <div
                            key={userId || `temp-${Math.random()}`}
                            onClick={() => setSelectedUser(user)}
                            className={`p-2 rounded-md cursor-pointer flex items-center ${
                              selectedUser && (selectedUser.id || selectedUser.userId) === userId
                                ? 'bg-blue-100 text-blue-800'
                                : 'hover:bg-gray-100'
                            }`}
                          >
                            <div className="flex-1">
                              <p className="font-medium">{username}</p>
                              {email && <p className="text-xs text-gray-500">{email}</p>}
                            </div>
                          </div>
                        );
                      })}

                      {users.length === 0 && (
                        <p className="text-sm text-gray-500 p-2">{t('admin.risks.contributors.no_users', 'No available users to assign')}</p>
                      )}
                    </div>
                  </ScrollArea>
                </div>

                <DialogFooter>
                  <Button
                    variant="outline"
                    onClick={() => setOpen(false)}
                  >
                    {t('admin.risks.contributors.cancel', 'Cancel')}
                  </Button>
                  <Button
                    onClick={handleAssignContributor}
                    disabled={!selectedUser || isSubmitting || !safeRiskId}
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        {t('admin.risks.contributors.assigning', 'Assigning...')}
                      </>
                    ) : (
                      t('admin.risks.contributors.assign', 'Assign')
                    )}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          ) : (
            <div className="mt-3 text-sm text-gray-500 flex items-center p-2 bg-gray-50 rounded-md">
              <UserPlus className="h-4 w-4 mr-2 text-gray-400" />
              {t('admin.risks.contributors.no_permission', 'You need update permission to manage contributors')}
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export default ContributorsSection;