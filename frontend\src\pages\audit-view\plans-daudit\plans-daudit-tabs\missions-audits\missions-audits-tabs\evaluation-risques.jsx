import React, { useState, useContext } from 'react';

import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Plus, AlertCircle, Save, Edit, Trash2, AlertTriangle } from "lucide-react";
import { toast } from "sonner";
import { useMissionAuditContext } from '@/utils/context-helpers';
import { OutletContext } from '@/pages/audit-view/missions-audits/edit-missions-audits';

function EvaluationRisquesTab() {
  const { missionAudit } = useMissionAuditContext();
  const [risks, setRisks] = useState([]);
  
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingRisk, setEditingRisk] = useState(null);
  const [newRisk, setNewRisk] = useState({
    name: "",
    description: "",
    category: "",
    impact: "Medium",
    probability: "Medium",
    severity: "Medium",
    mitigationPlan: ""
  });

  if (!missionAudit || !missionAudit.id) {
    return (
      <div className="flex items-center justify-center h-48">
        <p className="text-gray-500">Chargement des informations d'évaluation des risques...</p>
      </div>
    );
  }

  const handleSave = () => {
    toast.success("Évaluation des risques sauvegardée avec succès");
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewRisk(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name, value) => {
    setNewRisk(prev => ({ ...prev, [name]: value }));
  };

  const handleAddRisk = () => {
    if (!newRisk.name || !newRisk.description) {
      toast.error("Veuillez remplir au moins le nom et la description du risque");
      return;
    }

    if (editingRisk) {
      // Update existing risk
      setRisks(prev => prev.map(risk => 
        risk.id === editingRisk.id ? { ...newRisk, id: risk.id } : risk
      ));
      toast.success("Risque mis à jour avec succès");
    } else {
      // Add new risk
      setRisks(prev => [...prev, { ...newRisk, id: Date.now() }]);
      toast.success("Nouveau risque ajouté avec succès");
    }

    // Reset form and close dialog
    setNewRisk({
      name: "",
      description: "",
      category: "",
      impact: "Medium",
      probability: "Medium",
      severity: "Medium",
      mitigationPlan: ""
    });
    setEditingRisk(null);
    setIsDialogOpen(false);
  };

  const handleEditRisk = (risk) => {
    setEditingRisk(risk);
    setNewRisk(risk);
    setIsDialogOpen(true);
  };

  const handleDeleteRisk = (id) => {
    setRisks(prev => prev.filter(risk => risk.id !== id));
    toast.success("Risque supprimé avec succès");
  };

  const getLevelBadge = (level) => {
    const colors = {
      Low: "bg-green-100 text-green-800",
      Medium: "bg-yellow-100 text-yellow-800",
      High: "bg-red-100 text-red-800"
    };
    
    const labels = {
      Low: "Faible",
      Medium: "Moyen",
      High: "Élevé"
    };
    
    return <Badge className={colors[level]}>{labels[level]}</Badge>;
  };

  const getProgressColor = (level) => {
    switch (level) {
      case "Élevé":
        return "text-red-600 bg-red-500";
      case "Moyen":
        return "text-yellow-600 bg-yellow-500";
      case "Faible":
        return "text-green-600 bg-green-500";
      default:
        return "text-gray-600 bg-gray-500";
    }
  };

  const getOverallRiskLevel = () => {
    if (risks.length === 0) return "Non évalué";
    
    const highRisks = risks.filter(risk => risk.severity === "High").length;
    const mediumRisks = risks.filter(risk => risk.severity === "Medium").length;
    
    if (highRisks > risks.length * 0.25) {
      return "Élevé";
    } else if (highRisks > 0 || mediumRisks > risks.length * 0.5) {
      return "Moyen";
    } else {
      return "Faible";
    }
  };

  const calculateRiskProgress = (level) => {
    switch (level) {
      case "Élevé":
        return 100;
      case "Moyen":
        return 66;
      case "Faible":
        return 33;
      default:
        return 0;
    }
  };

  const risksByCategory = risks.reduce((acc, risk) => {
    if (risk.category) {
      acc[risk.category] = (acc[risk.category] || 0) + 1;
    }
    return acc;
  }, {});

  const overallRiskLevel = getOverallRiskLevel();
  const overallRiskColor = getProgressColor(overallRiskLevel);

  return (
    <div className="space-y-8">
      <div>
        <h2 className="text-lg font-semibold mb-4">Évaluation des Risques</h2>
        <Card>
          <CardContent className="p-6">
            <div className="space-y-6">
              <div>
                <h3 className="text-md font-semibold mb-2">Méthodologie d'évaluation</h3>
                <p className="text-gray-700">
                  L'évaluation des risques pour cette mission d'audit utilise une approche standardisée basée 
                  sur l'impact potentiel et la probabilité d'occurrence.
                </p>
              </div>

              <div>
                <h3 className="text-md font-semibold mb-2">Risques principaux identifiés</h3>
                <ul className="list-disc pl-5 space-y-2">
                  <li>Risque de non-conformité aux réglementations comptables en vigueur</li>
                  <li>Risque d'erreurs dans le traitement des opérations de clôture</li>
                  <li>Risque lié aux accès et à la séparation des tâches dans les systèmes comptables</li>
                  <li>Risque de retard dans la production des états financiers</li>
                </ul>
              </div>

              <div>
                <h3 className="text-md font-semibold mb-2">Évaluation</h3>
                <table className="min-w-full bg-white border border-gray-200">
                  <thead>
                    <tr>
                      <th className="py-2 px-4 border-b text-left">Risque</th>
                      <th className="py-2 px-4 border-b text-left">Impact</th>
                      <th className="py-2 px-4 border-b text-left">Probabilité</th>
                      <th className="py-2 px-4 border-b text-left">Score</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td className="py-2 px-4 border-b">Non-conformité réglementaire</td>
                      <td className="py-2 px-4 border-b">Élevé</td>
                      <td className="py-2 px-4 border-b">Moyen</td>
                      <td className="py-2 px-4 border-b">12</td>
                    </tr>
                    <tr>
                      <td className="py-2 px-4 border-b">Erreurs de clôture</td>
                      <td className="py-2 px-4 border-b">Moyen</td>
                      <td className="py-2 px-4 border-b">Élevé</td>
                      <td className="py-2 px-4 border-b">9</td>
                    </tr>
                    <tr>
                      <td className="py-2 px-4 border-b">Problèmes d'accès</td>
                      <td className="py-2 px-4 border-b">Moyen</td>
                      <td className="py-2 px-4 border-b">Faible</td>
                      <td className="py-2 px-4 border-b">4</td>
                    </tr>
                    <tr>
                      <td className="py-2 px-4 border-b">Retard états financiers</td>
                      <td className="py-2 px-4 border-b">Faible</td>
                      <td className="py-2 px-4 border-b">Moyen</td>
                      <td className="py-2 px-4 border-b">3</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-[#1A202C] flex items-center">
          <AlertTriangle className="h-6 w-6 mr-3 text-[#F62D51]" />
          Évaluation des Risques
        </h2>
        <div className="flex space-x-2">
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button className="bg-[#F62D51] hover:bg-[#F62D51]/90">
                <Plus className="h-4 w-4 mr-2" />
                Ajouter un Risque
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>{editingRisk ? "Modifier le Risque" : "Ajouter un Nouveau Risque"}</DialogTitle>
                <DialogDescription>
                  Complétez les informations ci-dessous pour {editingRisk ? "modifier" : "ajouter"} un risque identifié.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Nom du Risque *</Label>
                  <Input 
                    id="name" 
                    name="name"
                    value={newRisk.name}
                    onChange={handleInputChange}
                    placeholder="Nom du risque identifié"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="description">Description *</Label>
                  <Textarea 
                    id="description" 
                    name="description"
                    value={newRisk.description}
                    onChange={handleInputChange}
                    placeholder="Description détaillée du risque"
                    rows={3}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="category">Catégorie</Label>
                  <Input 
                    id="category" 
                    name="category"
                    value={newRisk.category}
                    onChange={handleInputChange}
                    placeholder="Ex: Financier, Opérationnel, Conformité, IT"
                  />
                </div>
                
                <div className="grid grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="impact">Impact</Label>
                    <Select 
                      name="impact"
                      value={newRisk.impact} 
                      onValueChange={(value) => handleSelectChange("impact", value)}
                    >
                      <SelectTrigger id="impact">
                        <SelectValue placeholder="Impact" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Low">Faible</SelectItem>
                        <SelectItem value="Medium">Moyen</SelectItem>
                        <SelectItem value="High">Élevé</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="probability">Probabilité</Label>
                    <Select 
                      name="probability"
                      value={newRisk.probability} 
                      onValueChange={(value) => handleSelectChange("probability", value)}
                    >
                      <SelectTrigger id="probability">
                        <SelectValue placeholder="Probabilité" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Low">Faible</SelectItem>
                        <SelectItem value="Medium">Moyenne</SelectItem>
                        <SelectItem value="High">Élevée</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="severity">Criticité</Label>
                    <Select 
                      name="severity"
                      value={newRisk.severity} 
                      onValueChange={(value) => handleSelectChange("severity", value)}
                    >
                      <SelectTrigger id="severity">
                        <SelectValue placeholder="Criticité" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Low">Faible</SelectItem>
                        <SelectItem value="Medium">Moyenne</SelectItem>
                        <SelectItem value="High">Élevée</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="mitigationPlan">Plan de Mitigation</Label>
                  <Textarea 
                    id="mitigationPlan" 
                    name="mitigationPlan"
                    value={newRisk.mitigationPlan}
                    onChange={handleInputChange}
                    placeholder="Actions prévues pour mitiger ce risque"
                    rows={3}
                  />
                </div>
              </div>
              
              <div className="flex justify-end gap-2 mt-4">
                <Button variant="outline" onClick={() => {
                  setIsDialogOpen(false);
                  setEditingRisk(null);
                  setNewRisk({
                    name: "",
                    description: "",
                    category: "",
                    impact: "Medium",
                    probability: "Medium",
                    severity: "Medium",
                    mitigationPlan: ""
                  });
                }}>
                  Annuler
                </Button>
                <Button className="bg-[#F62D51] hover:bg-[#F62D51]/90" onClick={handleAddRisk}>
                  {editingRisk ? "Mettre à jour" : "Ajouter"}
                </Button>
              </div>
            </DialogContent>
          </Dialog>
          
          <Button onClick={handleSave} variant="outline" className="border-[#F62D51] text-[#F62D51]">
            <Save className="h-4 w-4 mr-2" />
            Sauvegarder
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg">Synthèse de l'Évaluation des Risques</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div className="bg-gray-50 p-4 rounded-md">
              <div className="flex items-center space-x-2 mb-3">
                <AlertTriangle className="h-5 w-5 text-[#F62D51]" />
                <h3 className="font-semibold">Niveau de Risque Global</h3>
              </div>
              <div className={`text-4xl font-bold ${overallRiskLevel === "Non évalué" ? "text-gray-600" : overallRiskColor.split(" ")[0]}`}>{overallRiskLevel}</div>
              <div className="h-2 w-full bg-gray-200 rounded-full overflow-hidden">
                <div 
                  className={`h-full ${overallRiskLevel === "Non évalué" ? "bg-gray-500" : overallRiskColor.split(" ")[1]}`}
                  style={{ width: `${calculateRiskProgress(overallRiskLevel)}%` }}
                ></div>
              </div>
              <p className="text-sm text-gray-600 mt-2">
                Basé sur {risks.length} risques identifiés durant cette mission d'audit
              </p>
            </div>
            
            <div className="bg-gray-50 p-4 rounded-md">
              <h3 className="font-semibold mb-3">Répartition par Catégorie</h3>
              {Object.keys(risksByCategory).length > 0 ? (
                <div className="space-y-3">
                  {Object.entries(risksByCategory).map(([category, count]) => (
                    <div key={category} className="flex items-center justify-between">
                      <div className="text-sm">{category}</div>
                      <Badge variant="outline" className="ml-2">{count}</Badge>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-gray-500">Aucune catégorie définie</p>
              )}
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-gray-50 p-3 rounded-md flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Risques Élevés</p>
                <p className="text-2xl font-bold text-red-600">
                  {risks.filter(risk => risk.severity === 'High').length}
                </p>
              </div>
              <Badge className="bg-red-100 text-red-800">
                Attention Requise
              </Badge>
            </div>
            <div className="bg-gray-50 p-3 rounded-md flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Risques Moyens</p>
                <p className="text-2xl font-bold text-yellow-600">
                  {risks.filter(risk => risk.severity === 'Medium').length}
                </p>
              </div>
              <Badge className="bg-yellow-100 text-yellow-800">
                À Surveiller
              </Badge>
            </div>
            <div className="bg-gray-50 p-3 rounded-md flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Risques Faibles</p>
                <p className="text-2xl font-bold text-green-600">
                  {risks.filter(risk => risk.severity === 'Low').length}
                </p>
              </div>
              <Badge className="bg-green-100 text-green-800">
                Sous Contrôle
              </Badge>
            </div>
          </div>
          
          {risks.length === 0 && (
            <div className="text-center py-8 border rounded-md mt-4">
              <AlertCircle className="h-12 w-12 mx-auto text-gray-300 mb-3" />
              <p className="text-gray-500">Aucun risque identifié pour cette mission d'audit.</p>
              <p className="text-sm text-gray-400">Cliquez sur "Ajouter un Risque" pour commencer l'évaluation.</p>
            </div>
          )}
        </CardContent>
      </Card>

      {risks.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Liste des Risques Identifiés</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Risque</TableHead>
                  <TableHead>Catégorie</TableHead>
                  <TableHead>Impact</TableHead>
                  <TableHead>Probabilité</TableHead>
                  <TableHead>Criticité</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {risks.map((risk) => (
                  <TableRow key={risk.id}>
                    <TableCell className="font-medium">
                      <div>{risk.name}</div>
                      {risk.description && <div className="text-xs text-gray-500 mt-1">{risk.description}</div>}
                      {risk.mitigationPlan && (
                        <div className="text-xs text-gray-500 mt-2">
                          <span className="font-medium">Plan de mitigation:</span> {risk.mitigationPlan}
                        </div>
                      )}
                    </TableCell>
                    <TableCell>{risk.category || "-"}</TableCell>
                    <TableCell>{getLevelBadge(risk.impact)}</TableCell>
                    <TableCell>{getLevelBadge(risk.probability)}</TableCell>
                    <TableCell>{getLevelBadge(risk.severity)}</TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-2">
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          onClick={() => handleEditRisk(risk)}
                          className="h-8 w-8 p-0"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          onClick={() => handleDeleteRisk(risk.id)}
                          className="h-8 w-8 p-0 text-red-500 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

export default EvaluationRisquesTab;