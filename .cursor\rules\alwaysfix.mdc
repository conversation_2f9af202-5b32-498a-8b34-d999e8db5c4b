---
description: 
globs: 
alwaysApply: true
---
# Your rule content

- If I ever gave you a problem or a console error, always try to fix it automatically, not asking me if i want to fix it or not.

# Date Format Rule
- All dates in the application must be displayed in French format (dd/mm/yyyy)
- For input fields: Use type="date" but format the display value in dd/mm/yyyy
- For display fields: Use toLocaleDateString("fr-FR") with options { day: "2-digit", month: "2-digit", year: "numeric" }
- When sending dates to the API: Always use ISO format (YYYY-MM-DD)
- When receiving dates from the API: Convert to French format for display
- This rule applies to all components that handle dates

