import { Outlet, useLocation } from "react-router-dom";
import Audit<PERSON>ideBar from "./sidebar";
import AuditHeader from "./header";
import { useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import TranslateWrapper from "../TranslateWrapper";

function AuditLayout({ notifications, unreadCount, markAsRead, markAllAsRead, setNotifications }) {
  const [openSidebar, setOpenSidebar] = useState(false);
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const location = useLocation();

  // Handle sidebar collapse state changes
  const handleSidebarCollapseChange = (collapsed) => {
    setIsSidebarCollapsed(collapsed);
  };

  // Listen for window resize to auto-collapse sidebar on small screens
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 1024) {
        setIsSidebarCollapsed(false);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <div className="min-h-screen w-full bg-gray-100 flex flex-col">
      {/* Fixed Header */}
      <div className="fixed top-0 right-0 left-0 z-20">
        <AuditHeader
          setOpen={setOpenSidebar}
          notifications={notifications}
          unreadCount={unreadCount}
          markAsRead={markAsRead}
          markAllAsRead={markAllAsRead}
          setNotifications={setNotifications}
        />
      </div>

      {/* Fixed Sidebar */}
      <div className="fixed top-0 left-0 h-full z-30">
        <AuditSideBar
          open={openSidebar}
          setOpen={setOpenSidebar}
          onCollapseChange={handleSidebarCollapseChange}
        />
      </div>

      {/* Main Content Area with proper padding */}
      <div className={cn(
        "pt-[61px] transition-all duration-300 flex-grow", /* 61px is header height */
        isSidebarCollapsed ? "lg:ml-16" : "lg:ml-64"
      )}>
        <main className="bg-gray-100 min-h-[calc(100vh-61px)] h-full">
          <TranslateWrapper>
            <Outlet />
          </TranslateWrapper>
        </main>
      </div>
    </div>
  );
}

export default AuditLayout;
