import React, { useState, useEffect } from 'react';
import { CheckCircle, XCircle, Loader, AlertTriangle, User, ChevronLeft, ChevronRight } from 'lucide-react';
import axios from 'axios';
import { useParams } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { toast } from 'sonner';
import { getApiBaseUrl } from "@/utils/api-config";
import { hasWorkflowPermission } from '@/store/auth-slice';
import { useTranslation } from 'react-i18next';
// API Base URL - use the same one as your other API calls
const API_BASE_URL = getApiBaseUrl();

export default function RisksWorkflow() {
  const { t } = useTranslation();
  // Get the risk ID from URL params
  const { id: riskId } = useParams();
  const { user: currentUser } = useSelector((state) => state.auth);

  // State for workflow data
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingRoles, setIsLoadingRoles] = useState(false);
  const [error, setError] = useState(null);
  const [currentStep, setCurrentStep] = useState(null);
  const [events, setEvents] = useState([]);
  const [participants, setParticipants] = useState([]);
  const [userRoles, setUserRoles] = useState({});
  const [availableTransitions, setAvailableTransitions] = useState({});
  const [riskCreator, setRiskCreator] = useState(null);

  // State for dialogs and modals
  const [showRejectDialog, setShowRejectDialog] = useState(false);
  const [rejectMessage, setRejectMessage] = useState('');

  // State for active tab
  const [activeTab, setActiveTab] = useState('Activity');

  // Step keys and their English backend values
  const stepKeys = [
    'risk_created',
    'to_submit',
    'to_validate',
    'validated',
    'rejected'
  ];
  // Map of backend values (case-insensitive) to step keys
  const backendStepMap = {
    'risk created': 'risk_created',
    'to submit': 'to_submit',
    'to validate': 'to_validate',
    'validated': 'validated',
    'rejected': 'rejected'
  };
  // Use t() for display only
  const steps = stepKeys.map(key => t(`admin.risks.workflow.status.${key}`));

  // Add pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(5);

  // Permission checks
  const reduxState = useSelector(state => state);

  // Check if user has permissions to advance workflow
  const canAdvanceWorkflow = hasWorkflowPermission(reduxState, 'advance', currentStep);

  // Check if user has permissions to validate
  const canValidateWorkflow = hasWorkflowPermission(reduxState, 'validate', currentStep);

  // Check if user has permissions to reject
  const canRejectWorkflow = hasWorkflowPermission(reduxState, 'reject', currentStep, currentUser?.id === riskCreator);

  // Fetch risk workflow data on component mount
  useEffect(() => {
    if (riskId) {
      fetchWorkflowData();
      fetchUserRoles(); // Fetch user roles separately
    }
  }, [riskId]);

  // Fetch workflow data (state and events)
  const fetchWorkflowData = async () => {
    if (!riskId) {
      setError(t('admin.risks.workflow.error_missing_id'));
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      console.log('Fetching workflow data for risk ID:', riskId);

      // Get current state and events
      const stateResponse = await axios.get(`${API_BASE_URL}/risks/${riskId}/workflow/state`, {
        withCredentials: true
      });

      console.log('State response:', stateResponse.data);

      if (stateResponse.data && stateResponse.data.success) {
        setCurrentStep(stateResponse.data.data.current_state);
        const timeline = stateResponse.data.data.timeline || [];

        // Sort timeline in reverse chronological order (newest first)
        const sortedTimeline = [...timeline].sort((a, b) =>
          new Date(b.timestamp) - new Date(a.timestamp)
        );

        setEvents(sortedTimeline);

        // Find risk creator from the event with Create transition (should be first chronologically)
        const createEvent = timeline.find(e => e.transition === 'Create');
        if (createEvent) {
          setRiskCreator(createEvent.user);
        }

        // Extract unique participants with their actions
        const participantsMap = new Map();

        timeline.forEach(event => {
          if (!participantsMap.has(event.user)) {
            participantsMap.set(event.user, {
              name: event.user,
              actions: [],
              lastAction: null
            });
          }

          const participant = participantsMap.get(event.user);

          // Add this action
          participant.actions.push({
            transition: event.transition,
            timestamp: event.timestamp,
            step: event.step
          });

          // Update last action if this is more recent
          if (!participant.lastAction || new Date(event.timestamp) > new Date(participant.lastAction.timestamp)) {
            participant.lastAction = {
              transition: event.transition,
              timestamp: event.timestamp,
              step: event.step
            };
          }
        });

        // Convert map to array and sort by most recent activity
        const participantsList = Array.from(participantsMap.values())
          .sort((a, b) => new Date(b.lastAction.timestamp) - new Date(a.lastAction.timestamp));

        setParticipants(participantsList);

        // Get available transitions
        const transitionsResponse = await axios.get(`${API_BASE_URL}/risks/${riskId}/workflow/transitions`, {
          withCredentials: true
        });

        console.log('Transitions response:', transitionsResponse.data);

        if (transitionsResponse.data && transitionsResponse.data.success) {
          setAvailableTransitions(transitionsResponse.data.data.available_transitions || {});
        } else {
          console.error('Failed to get transitions:', transitionsResponse.data);
          setError(t('admin.risks.workflow.error_transitions'));
        }
      } else {
        console.error('Failed to get state:', stateResponse.data);
        setError(t('admin.risks.workflow.error_workflow_data'));
      }
    } catch (err) {
      console.error('Error fetching workflow data:', err);
      console.error('Error details:', err.response?.data || err.message);

      // More descriptive error message based on status code
      if (err.response?.status === 401) {
        setError(t('admin.risks.workflow.error_unauthorized'));
      } else if (err.response?.status === 404) {
        setError(t('admin.risks.workflow.error_not_found'));
      } else {
        setError(err.response?.data?.message || t('admin.risks.workflow.error_workflow_data'));
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch user roles from backend
  const fetchUserRoles = async () => {
    setIsLoadingRoles(true);
    try {
      const response = await axios.get(`${API_BASE_URL}/users`, {
        withCredentials: true
      });

      if (response.data && response.data.success) {
        // Create a map of username/email to role
        const rolesMap = {};
        response.data.data.forEach(user => {
          // Assuming user has username/email and roles properties
          const identifier = user.username || user.email;
          const role = user.roles && user.roles.length > 0
            ? user.roles[0].name
            : 'User';

          rolesMap[identifier] = role;
        });

        setUserRoles(rolesMap);
      }
    } catch (err) {
      console.error('Error fetching user roles:', err);
    } finally {
      setIsLoadingRoles(false);
    }
  };

  // Helper function to get role display
  const getUserRoleDisplay = (userName) => {
    if (isLoadingRoles) {
      return <span className="flex items-center"><Loader className="h-3 w-3 animate-spin mr-1" /> {t('admin.risks.workflow.loading_roles')}</span>;
    }
    return userRoles[userName] || t('admin.risks.workflow.unknown_role');
  };

  // Handle state transition
  const handleTransition = async (transition, message = '') => {
    if (!currentUser?.id) {
      setError(t('admin.risks.workflow.error_user_missing'));
      return;
    }

    // Check permissions before proceeding with the transition
    if ((transition === 'Validate' || transition === 'Reject' || transition === 'Reset') && !canValidateWorkflow) {
      toast.error(t('admin.risks.workflow.error_no_permission'));
      return;
    }

    if (transition === 'Advance' && !canAdvanceWorkflow) {
      toast.error(t('admin.risks.workflow.error_no_permission'));
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // For rejection, we'll use the Reset transition to go back to first step
      const actualTransition = transition === 'Reject' ? 'Reset' : transition;

      console.log('Transitioning workflow:', { riskId, action: actualTransition, message });

      // Include the current user ID in the request for backward compatibility
      const response = await axios.post(`${API_BASE_URL}/risks/${riskId}/workflow/transition`, {
        action: actualTransition,
        message: message, // Pass optional message
        userId: currentUser?.id // Include user ID for backward compatibility
      }, {
        withCredentials: true
      });

      console.log('Transition response:', response.data);

      if (response.data && response.data.success) {
        setCurrentStep(response.data.data.current_state);

        // Ensure we have the timeline data
        if (response.data.data.timeline && response.data.data.timeline.length > 0) {
          setEvents(response.data.data.timeline);
        } else {
          // If timeline is empty or missing, fetch it explicitly
          await fetchWorkflowData();
        }

        // Reset dialog states
        setShowRejectDialog(false);
        setRejectMessage('');

        // Refresh available transitions
        const transitionsResponse = await axios.get(`${API_BASE_URL}/risks/${riskId}/workflow/transitions`, {
          withCredentials: true
        });

        if (transitionsResponse.data && transitionsResponse.data.success) {
          setAvailableTransitions(transitionsResponse.data.data.available_transitions || {});
        } else {
          console.error('Failed to refresh transitions:', transitionsResponse.data);
        }
      } else {
        setError(response.data?.message || t('admin.risks.workflow.error_workflow_data'));
      }
    } catch (err) {
      console.error('Error updating workflow:', err);
      console.error('Error details:', err.response?.data || err.message);

      if (err.response?.status === 401) {
        setError('Unauthorized: Please log in again');
      } else if (err.response?.status === 403) {
        toast.error(err.response.data?.message || 'You do not have permission to perform this action');
      } else if (err.response?.status === 400) {
        setError(err.response.data?.message || 'Invalid transition requested');
      } else {
        setError(err.response?.data?.message || t('admin.risks.workflow.error_workflow_data'));
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Handle rejection with message
  const handleReject = () => {
    if (!canRejectWorkflow) {
      toast.error(t('admin.risks.workflow.permissions.no_reject_permission'));
      return;
    }

    if (rejectMessage.trim() === '') {
      setError(t('admin.risks.workflow.validation.rejection_reason_required'));
      return;
    }

    // Close the dialog immediately after submitting
    handleTransition('Reject', rejectMessage);
    setShowRejectDialog(false);
    setRejectMessage('');
  };

  // Helper function to get the current step index
  const getCurrentIndex = () => {
    if (!currentStep) return -1;
    // Try to map backend value (case-insensitive)
    const key = backendStepMap[(currentStep || '').toLowerCase()];
    if (key) return stepKeys.indexOf(key);
    // Fallback: try to match translated value
    const foundKey = stepKeys.find(k => t(`admin.risks.workflow.status.${k}`) === currentStep);
    return foundKey ? stepKeys.indexOf(foundKey) : -1;
  };

  // Function to format date nicely in dd/mm/yyyy format
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Helper functions for pagination
  const indexOfLastEvent = currentPage * itemsPerPage;
  const indexOfFirstEvent = indexOfLastEvent - itemsPerPage;
  const currentEvents = events.slice(indexOfFirstEvent, indexOfLastEvent);
  const totalPages = Math.ceil(events.length / itemsPerPage);

  const nextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const prevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  if (isLoading && !currentStep) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader className="h-8 w-8 animate-spin" />
        <span className="ml-2">{t('admin.risks.workflow.loading_workflow_data')}</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 text-red-500">
        <p>{t('admin.risks.workflow.error_label')}: {error}</p>
        <button
          className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          onClick={fetchWorkflowData}
        >
          {t('admin.risks.workflow.retry')}
        </button>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Enhanced Status Indicator - Positioned at top left */}
      <div className="mb-6 flex items-start">
        {currentStep === t('admin.risks.workflow.status.validated') ? (
          <div className="flex items-center bg-green-100 px-4 py-2 rounded-lg">
            <CheckCircle className="h-6 w-6 text-green-500 mr-2" />
            <span className="text-lg font-medium text-green-700">{t('admin.risks.workflow.status.validated')}</span>
          </div>
        ) : currentStep === t('admin.risks.workflow.status.rejected') ? (
          <div className="flex items-center bg-red-100 px-4 py-2 rounded-lg">
            <XCircle className="h-6 w-6 text-red-500 mr-2" />
            <span className="text-lg font-medium text-red-700">{t('admin.risks.workflow.status.rejected')}</span>
          </div>
        ) : currentStep === t('admin.risks.workflow.status.risk_created') ? (
          <div className="flex items-center bg-blue-100 px-4 py-2 rounded-lg">
            <AlertTriangle className="h-6 w-6 text-blue-500 mr-2" />
            <span className="text-lg font-medium text-blue-700">{t('admin.risks.workflow.status.risk_created')}</span>
          </div>
        ) : currentStep === t('admin.risks.workflow.status.to_submit') ? (
          <div className="flex items-center bg-yellow-100 px-4 py-2 rounded-lg">
            <AlertTriangle className="h-6 w-6 text-yellow-500 mr-2" />
            <span className="text-lg font-medium text-yellow-700">{t('admin.risks.workflow.status.to_submit')}</span>
          </div>
        ) : currentStep === t('admin.risks.workflow.status.to_validate') ? (
          <div className="flex items-center bg-orange-100 px-4 py-2 rounded-lg">
            <AlertTriangle className="h-6 w-6 text-orange-500 mr-2" />
            <span className="text-lg font-medium text-orange-700">{t('admin.risks.workflow.status.to_validate')}</span>
          </div>
        ) : (
          <div className="flex items-center bg-gray-100 px-4 py-2 rounded-lg">
            <AlertTriangle className="h-6 w-6 text-gray-500 mr-2" />
            <span className="text-lg font-medium text-gray-700">{currentStep}</span>
          </div>
        )}
        {isLoading &&
          <div className="flex items-center bg-blue-50 px-3 py-1 rounded ml-4">
            <Loader className="h-4 w-4 mr-2 animate-spin text-blue-500" />
            <span className="text-blue-500 text-sm">{t('admin.risks.workflow.updating')}</span>
          </div>
        }
      </div>

      {/* Horizontal Step Progress Bar */}
      <div className="flex items-center justify-between mb-6">
        {steps.map((stepLabel, index) => (
          <React.Fragment key={stepKeys[index]}>
            <div className="flex flex-col items-center">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                index === getCurrentIndex()
                  ? 'bg-blue-500' :
                index < getCurrentIndex()
                  ? 'bg-blue-500' :
                stepKeys[index] === 'validated' && getCurrentIndex() === stepKeys.length - 2
                  ? 'bg-green-500' :
                stepKeys[index] === 'rejected' && getCurrentIndex() === stepKeys.length - 1
                  ? 'bg-red-500' :
                'bg-gray-300'
              }`}>
                <span className="text-white">{index + 1}</span>
              </div>
              <p className="mt-2 text-sm text-center">{stepLabel}</p>
            </div>
            {index < steps.length - 1 && <div className="flex-1 h-1 bg-gray-300 mx-2"></div>}
          </React.Fragment>
        ))}
      </div>

      {/* Action Buttons - Based on available transitions */}
      <div className="flex justify-start space-x-4 mb-6">
        {/* Handle special case for Reject button in Validated state */}
        {currentStep === t('admin.risks.workflow.status.validated') && canRejectWorkflow && (
          <button
            className="px-4 py-2 rounded bg-red-500 text-white hover:bg-red-600"
            onClick={() => setShowRejectDialog(true)}
            disabled={isLoading}
          >
            {t('admin.risks.workflow.buttons.reject')}
          </button>
        )}

        {Object.entries(availableTransitions).map(([action]) => (
          action !== 'Reset' && (
            <button
              key={action}
              className={`px-4 py-2 rounded ${
                action === "Advance"
                  ? canAdvanceWorkflow
                    ? "border border-blue-500 text-blue-500 bg-white hover:bg-blue-50"
                    : "text-gray-400 bg-gray-100 cursor-not-allowed hover:bg-gray-100"
                  : action === "Validate"
                    ? canValidateWorkflow
                      ? "border border-blue-500 text-blue-500 bg-white hover:bg-blue-50"
                      : "text-gray-400 bg-gray-100 cursor-not-allowed hover:bg-gray-100"
                    : action === "Reject"
                      ? canRejectWorkflow
                        ? "bg-red-500 text-white hover:bg-red-600"
                        : "bg-red-200 text-gray-400 cursor-not-allowed hover:bg-red-200"
                      : "bg-gray-300 text-gray-700 hover:bg-gray-400"
              }`}
              onClick={() => {
                if (action === "Reject") {
                  if (canRejectWorkflow) {
                    setShowRejectDialog(true);
                  } else {
                    toast.error(t('admin.risks.workflow.permissions.no_reject_permission'));
                  }
                } else if (action === "Validate") {
                  if (canValidateWorkflow) {
                    handleTransition(action);
                  } else {
                    toast.error(t('admin.risks.workflow.permissions.no_validate_permission'));
                  }
                } else if (action === "Advance") {
                  if (canAdvanceWorkflow) {
                    handleTransition(action);
                  } else {
                    toast.error(t('admin.risks.workflow.permissions.no_advance_permission'));
                  }
                } else {
                  handleTransition(action);
                }
              }}
              disabled={isLoading ||
                (action === "Validate" && !canValidateWorkflow) ||
                (action === "Reject" && !canRejectWorkflow) ||
                (action === "Advance" && !canAdvanceWorkflow)}
            >
              {action === "Advance" && currentStep === t('admin.risks.workflow.status.risk_created') ? t('admin.risks.workflow.buttons.submit') :
               action === "Advance" && currentStep === t('admin.risks.workflow.status.to_submit') ? t('admin.risks.workflow.buttons.submit_for_validation') :
               action === "Validate" ? t('admin.risks.workflow.buttons.validate_risk') :
               action === "Reject" ? t('admin.risks.workflow.buttons.reject') : action}
            </button>
          )
        ))}
      </div>

      {/* Tab Buttons */}
      <div className="flex space-x-4 mb-6">
        <button
          className={`px-4 py-2 rounded ${
            activeTab === 'Activity' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700'
          }`}
          onClick={() => setActiveTab('Activity')}
        >
          {t('admin.risks.workflow.tabs.activity')}
        </button>
        <button
          className={`px-4 py-2 rounded ${
            activeTab === 'Participants' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700'
          }`}
          onClick={() => setActiveTab('Participants')}
        >
          {t('admin.risks.workflow.tabs.participants')}
        </button>
      </div>

      {/* Activity Timeline */}
      {activeTab === 'Activity' && (
        <div className="mt-4 space-y-6">
          {riskCreator && (
            <div className="mb-4 p-3 bg-blue-50 rounded-md border border-blue-100">
              <p className="font-medium">{t('admin.risks.workflow.activity.risk_creator')}: <span className="font-normal">{riskCreator}</span></p>
            </div>
          )}

          {events.length === 0 ? (
            <div className="text-gray-500">{t('admin.risks.workflow.activity.no_activity')}</div>
          ) : (
            <>
              {currentEvents.map((event, index) => (
                <div key={index} className="flex">
                  <div className="flex flex-col items-center mr-4">
                    <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                    {index < currentEvents.length - 1 && <div className="w-0.5 h-full bg-gray-300"></div>}
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">
                      {formatDate(event.timestamp)}
                    </p>
                    <p className="font-medium">{t('admin.risks.workflow.activity.step_reached')}: {event.step}</p>
                    <p className="text-sm">{t('admin.risks.workflow.activity.performed_by')}: {event.user}</p>
                    {event.transition && (
                      <p className="text-sm text-gray-500">{t('admin.risks.workflow.activity.using_transition')}: '{event.transition}'</p>
                    )}
                    {event.message && (
                      <p className="text-sm mt-1 italic bg-gray-50 p-2 rounded">"{event.message}"</p>
                    )}
                  </div>
                </div>
              ))}

              {/* Pagination Controls */}
              {totalPages > 1 && (
                <div className="flex justify-between items-center mt-4 pt-4 border-t border-gray-200">
                  <button
                    onClick={prevPage}
                    disabled={currentPage === 1}
                    className={`flex items-center ${currentPage === 1 ? 'text-gray-300 cursor-not-allowed' : 'text-blue-600 hover:text-blue-800'}`}
                  >
                    <ChevronLeft className="h-5 w-5" />
                    <span>{t('admin.risks.workflow.activity.previous')}</span>
                  </button>

                  <span className="text-sm text-gray-600">{t('admin.risks.workflow.activity.page_of', { current: currentPage, total: totalPages })}</span>

                  <button
                    onClick={nextPage}
                    disabled={currentPage === totalPages}
                    className={`flex items-center ${currentPage === totalPages ? 'text-gray-300 cursor-not-allowed' : 'text-blue-600 hover:text-blue-800'}`}
                  >
                    <span>{t('admin.risks.workflow.activity.next')}</span>
                    <ChevronRight className="h-5 w-5" />
                  </button>
                </div>
              )}
            </>
          )}
        </div>
      )}

      {/* Participants tab content */}
      {activeTab === 'Participants' && (
        <div className="mt-4">
          {participants.length === 0 ? (
            <div className="text-gray-500">{t('admin.risks.workflow.participants.no_participants')}</div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('admin.risks.workflow.participants.table.name')}
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('admin.risks.workflow.participants.table.role')}
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('admin.risks.workflow.participants.table.last_action')}
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('admin.risks.workflow.participants.table.date')}
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {participants.map((participant, index) => (
                    <tr key={index}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {participant.name}
                        {participant.name === riskCreator && (
                          <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                            {t('admin.risks.workflow.participants.creator_badge')}
                          </span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {getUserRoleDisplay(participant.name)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {participant.lastAction?.transition} ({participant.lastAction?.step})
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {participant.lastAction?.timestamp ? formatDate(participant.lastAction.timestamp) : ''}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      )}

      {/* Reject Dialog with Message Field */}
      {showRejectDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <div className="flex items-center mb-4">
              <AlertTriangle className="h-6 w-6 text-red-500 mr-2" />
              <h3 className="text-lg font-medium">{t('admin.risks.workflow.reject_dialog.title')}</h3>
            </div>
            <p className="mb-4">{t('admin.risks.workflow.reject_dialog.message')}</p>
            <textarea
              className="w-full border border-gray-300 rounded p-2 mb-4"
              rows="3"
              placeholder={t('admin.risks.workflow.reject_dialog.placeholder')}
              value={rejectMessage}
              onChange={(e) => setRejectMessage(e.target.value)}
            ></textarea>
            <div className="flex justify-end space-x-2">
              <button
                className="px-4 py-2 border border-gray-300 rounded hover:bg-gray-100"
                onClick={() => {
                  setShowRejectDialog(false);
                  setRejectMessage('');
                }}
              >
                {t('admin.risks.workflow.cancel')}
              </button>
              <button
                className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
                onClick={handleReject}
                disabled={rejectMessage.trim() === ''}
              >
                {t('admin.risks.workflow.buttons.reject')}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}