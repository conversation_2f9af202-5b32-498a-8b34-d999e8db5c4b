import { useState, useEffect, useCallback, useRef } from "react";
import axios from "axios";
import { Upload, File, Trash2, FileText, ExternalLink, Download, Loader2 } from "lucide-react";
import { Button } from "../../components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "../../components/ui/tabs";
import { toast } from "sonner";
import { getApiBaseUrl } from '../../utils/api-config';
import { useTranslation } from 'react-i18next';
export function RiskAttachmentsSection({ risk }) {
  const { t } = useTranslation();
  const [businessDocuments, setBusinessDocuments] = useState([]);
  const [externalReferences, setExternalReferences] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  // We need to keep this state for the Tabs component even though it's not directly used
  const [, setActiveTab] = useState("business-documents");
  const [isDraggingBusiness, setIsDraggingBusiness] = useState(false);
  const [isDraggingExternal, setIsDraggingExternal] = useState(false);
  const API_BASE_URL = getApiBaseUrl();
  // Refs for the file inputs
  const businessDocInputRef = useRef(null);
  const externalRefInputRef = useRef(null);

  // Fetch attachments from the server
  const fetchAttachments = useCallback(async () => {
    if (!risk?.riskID) return;

    try {
      setIsLoading(true);

      // Add timestamp to prevent caching
      const timestamp = new Date().getTime();

      // Fetch business documents
      const businessDocsResponse = await axios.get(`${API_BASE_URL}/risk-uploads?riskID=${risk.riskID}&type=business-document&_t=${timestamp}`, {
        withCredentials: true,
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      });

      // Fetch external references
      const externalRefsResponse = await axios.get(`${API_BASE_URL}/risk-uploads?riskID=${risk.riskID}&type=external-reference&_t=${timestamp}`, {
        withCredentials: true,
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      });

      if (businessDocsResponse.data.success) {
        setBusinessDocuments(businessDocsResponse.data.data);
      }

      if (externalRefsResponse.data.success) {
        setExternalReferences(externalRefsResponse.data.data);
      }
    } catch (error) {
      console.error('Error fetching attachments:', error);

      // Don't show error toast if it's a 'relation does not exist' error
      // This happens when the RiskAttachment table hasn't been created yet
      if (error.response?.data?.message?.includes('relation') &&
          error.response?.data?.message?.includes('does not exist')) {
        console.log('RiskAttachment table does not exist yet. This is normal if no attachments have been uploaded.');
      } else {
        toast.error(t('admin.risks.attachments.error_loading', 'Failed to load attachments'), {
          description: t('admin.risks.attachments.error_loading_description', 'Please try refreshing the page'),
          duration: 5000,
        });
      }
    } finally {
      setIsLoading(false);
    }
  }, [risk?.riskID]);

  // Fetch attachments on component mount and when risk changes
  useEffect(() => {
    fetchAttachments();

    // Set up polling for attachments (every 30 seconds)
    if (risk?.riskID) {
      const pollingInterval = setInterval(fetchAttachments, 30000);
      return () => clearInterval(pollingInterval);
    }
  }, [risk?.riskID, fetchAttachments]);

  // Handle file upload for business documents
  const handleBusinessDocumentUpload = async (e) => {
    const files = Array.from(e.target.files);
    if (files.length === 0) return;

    // Define allowed file extensions
    const allowedExtensions = [
      '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
      '.txt', '.csv', '.rtf', '.odt', '.ods', '.odp',
      '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg',
      '.zip', '.rar', '.7z', '.tar', '.gz'
    ];

    // Check file size before uploading (50MB limit)
    const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB in bytes
    const oversizedFiles = files.filter(file => file.size > MAX_FILE_SIZE);

    if (oversizedFiles.length > 0) {
      const fileNames = oversizedFiles.map(file => file.name).join(', ');
      toast.error('Files too large', {
        description: `The following files exceed the 50MB limit: ${fileNames}`,
        duration: 5000,
      });
      e.target.value = ""; // Reset input
      return;
    }

    // Check file types
    const invalidFiles = files.filter(file => {
      const extension = '.' + file.name.split('.').pop().toLowerCase();
      return !allowedExtensions.includes(extension);
    });

    if (invalidFiles.length > 0) {
      const fileNames = invalidFiles.map(file => file.name).join(', ');
      toast.error('Unsupported file type', {
        description: `The following files have unsupported formats: ${fileNames}`,
        duration: 5000,
      });
      e.target.value = ""; // Reset input
      return;
    }

    try {
      setIsLoading(true);
      setUploadProgress(0);

      const formData = new FormData();
      formData.append('type', 'business-document');
      formData.append('riskID', risk.riskID);

      files.forEach(file => {
        formData.append('files', file);
      });

      const response = await axios.post(`${API_BASE_URL}/risk-uploads`, formData, {
        withCredentials: true,
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        onUploadProgress: (progressEvent) => {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          setUploadProgress(percentCompleted);
        }
      });

      if (response.data.success) {
        toast.success('Upload successful', {
          description: `${files.length} document${files.length !== 1 ? 's' : ''} uploaded successfully`,
          duration: 3000,
        });
        fetchAttachments(); // Refresh the list
      }
    } catch (error) {
      console.error('Error uploading business documents:', error);

      // Handle specific error types
      if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
        toast.error('Upload timed out. Please try again with smaller files or a better connection.', {
          description: 'The server took too long to respond',
          duration: 5000,
        });
      } else if (error.response?.status === 413) {
        toast.error('File too large', {
          description: 'The file exceeds the maximum size limit of 50MB',
          duration: 5000,
        });
      } else if (error.response?.data?.message?.includes('file type')) {
        toast.error('Unsupported file type', {
          description: error.response.data.message,
          duration: 5000,
        });
      } else if (error.response?.data?.message?.includes('relation') &&
                 error.response?.data?.message?.includes('does not exist')) {
        toast.error('Database setup required', {
          description: 'The attachment storage is not set up yet. Please contact the administrator.',
          duration: 5000,
        });
        console.error('Database error: RiskAttachment table does not exist');
      } else {
        const errorMsg = error.response?.data?.message || 'Failed to upload documents';
        toast.error('Upload failed', {
          description: errorMsg,
          duration: 5000,
        });
        console.error('Upload error:', errorMsg);
      }
    } finally {
      setIsLoading(false);
      setUploadProgress(0);
      e.target.value = ""; // Reset input
    }
  };

  // Handle file upload for external references
  const handleExternalReferenceUpload = async (e) => {
    const files = Array.from(e.target.files);
    if (files.length === 0) return;

    // Define allowed file extensions
    const allowedExtensions = [
      '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
      '.txt', '.csv', '.rtf', '.odt', '.ods', '.odp',
      '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg',
      '.zip', '.rar', '.7z', '.tar', '.gz'
    ];

    // Check file size before uploading (50MB limit)
    const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB in bytes
    const oversizedFiles = files.filter(file => file.size > MAX_FILE_SIZE);

    if (oversizedFiles.length > 0) {
      const fileNames = oversizedFiles.map(file => file.name).join(', ');
      toast.error('Files too large', {
        description: `The following files exceed the 50MB limit: ${fileNames}`,
        duration: 5000,
      });
      e.target.value = ""; // Reset input
      return;
    }

    // Check file types
    const invalidFiles = files.filter(file => {
      const extension = '.' + file.name.split('.').pop().toLowerCase();
      return !allowedExtensions.includes(extension);
    });

    if (invalidFiles.length > 0) {
      const fileNames = invalidFiles.map(file => file.name).join(', ');
      toast.error('Unsupported file type', {
        description: `The following files have unsupported formats: ${fileNames}`,
        duration: 5000,
      });
      e.target.value = ""; // Reset input
      return;
    }

    try {
      setIsLoading(true);
      setUploadProgress(0);

      const formData = new FormData();
      formData.append('type', 'external-reference');
      formData.append('riskID', risk.riskID);

      files.forEach(file => {
        formData.append('files', file);
      });

      const response = await axios.post(`${API_BASE_URL}/risk-uploads`, formData, {
        withCredentials: true,
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        onUploadProgress: (progressEvent) => {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          setUploadProgress(percentCompleted);
        }
      });

      if (response.data.success) {
        toast.success('Upload successful', {
          description: `${files.length} reference${files.length !== 1 ? 's' : ''} uploaded successfully`,
          duration: 3000,
        });
        fetchAttachments(); // Refresh the list
      }
    } catch (error) {
      console.error('Error uploading external references:', error);

      // Handle specific error types
      if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
        toast.error('Upload timed out. Please try again with smaller files or a better connection.', {
          description: 'The server took too long to respond',
          duration: 5000,
        });
      } else if (error.response?.status === 413) {
        toast.error('File too large', {
          description: 'The file exceeds the maximum size limit of 50MB',
          duration: 5000,
        });
      } else if (error.response?.data?.message?.includes('file type')) {
        toast.error('Unsupported file type', {
          description: error.response.data.message,
          duration: 5000,
        });
      } else if (error.response?.data?.message?.includes('relation') &&
                 error.response?.data?.message?.includes('does not exist')) {
        toast.error('Database setup required', {
          description: 'The attachment storage is not set up yet. Please contact the administrator.',
          duration: 5000,
        });
        console.error('Database error: RiskAttachment table does not exist');
      } else {
        const errorMsg = error.response?.data?.message || 'Failed to upload references';
        toast.error('Upload failed', {
          description: errorMsg,
          duration: 5000,
        });
        console.error('Upload error:', errorMsg);
      }
    } finally {
      setIsLoading(false);
      setUploadProgress(0);
      e.target.value = ""; // Reset input
    }
  };

  // Download an attachment
  const downloadAttachment = async (id, fileName) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/risk-uploads/download/${id}`, {
        withCredentials: true,
        responseType: 'blob'
      });

      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', fileName);
      document.body.appendChild(link);
      link.click();
      link.remove();
    } catch (error) {
      console.error('Error downloading attachment:', error);
      toast.error('Download failed', {
        description: 'Unable to download the file. Please try again later.',
        duration: 5000,
      });
    }
  };

  // Delete a business document
  const deleteBusinessDocument = async (id) => {
    if (!window.confirm('Are you sure you want to delete this document?')) return;

    try {
      const response = await axios.delete(`${API_BASE_URL}/risk-uploads/${id}`, {
        withCredentials: true
      });

      if (response.data.success) {
        toast.success('Document deleted', {
          description: 'The document was successfully deleted',
          duration: 3000,
        });
        fetchAttachments(); // Refresh the list
      }
    } catch (error) {
      console.error('Error deleting document:', error);
      toast.error('Delete failed', {
        description: 'Unable to delete the document. Please try again later.',
        duration: 5000,
      });
    }
  };

  // Delete an external reference
  const deleteExternalReference = async (id) => {
    if (!window.confirm('Are you sure you want to delete this reference?')) return;

    try {
      const response = await axios.delete(`${API_BASE_URL}/risk-uploads/${id}`, {
        withCredentials: true
      });

      if (response.data.success) {
        toast.success('Reference deleted', {
          description: 'The reference was successfully deleted',
          duration: 3000,
        });
        fetchAttachments(); // Refresh the list
      }
    } catch (error) {
      console.error('Error deleting reference:', error);
      toast.error('Delete failed', {
        description: 'Unable to delete the reference. Please try again later.',
        duration: 5000,
      });
    }
  };

  // Format file size
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Trigger file input click
  const triggerBusinessDocUpload = () => {
    businessDocInputRef.current?.click();
  };

  const triggerExternalRefUpload = () => {
    externalRefInputRef.current?.click();
  };

  // Drag and drop handlers for business documents
  const handleBusinessDragOver = (e) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleBusinessDragEnter = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingBusiness(true);
  };

  const handleBusinessDragLeave = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingBusiness(false);
  };

  const handleBusinessDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingBusiness(false);

    if (isLoading) return;

    const files = Array.from(e.dataTransfer.files);
    if (files.length === 0) return;

    // Trigger the file input's onChange handler with the dropped files
    if (businessDocInputRef.current) {
      // Create a new event and set the files
      const dataTransfer = new DataTransfer();
      files.forEach(file => dataTransfer.items.add(file));

      const fileList = dataTransfer.files;
      businessDocInputRef.current.files = fileList;

      // Manually trigger the onChange event
      const event = new Event('change', { bubbles: true });
      businessDocInputRef.current.dispatchEvent(event);
    }
  };

  // Drag and drop handlers for external references
  const handleExternalDragOver = (e) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleExternalDragEnter = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingExternal(true);
  };

  const handleExternalDragLeave = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingExternal(false);
  };

  const handleExternalDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingExternal(false);

    if (isLoading) return;

    const files = Array.from(e.dataTransfer.files);
    if (files.length === 0) return;

    // Trigger the file input's onChange handler with the dropped files
    if (externalRefInputRef.current) {
      // Create a new event and set the files
      const dataTransfer = new DataTransfer();
      files.forEach(file => dataTransfer.items.add(file));

      const fileList = dataTransfer.files;
      externalRefInputRef.current.files = fileList;

      // Manually trigger the onChange event
      const event = new Event('change', { bubbles: true });
      externalRefInputRef.current.dispatchEvent(event);
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      <Tabs defaultValue="business-documents" className="w-full" onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-2 mb-4">
          <TabsTrigger value="business-documents" className="text-center">
            <FileText className="h-4 w-4 mr-2" />
            {t('admin.risks.attachments.business_documents', 'Business Documents')}
          </TabsTrigger>
          <TabsTrigger value="external-references" className="text-center">
            <ExternalLink className="h-4 w-4 mr-2" />
            {t('admin.risks.attachments.external_references', 'External References')}
          </TabsTrigger>
        </TabsList>

        {/* Business Documents Tab */}
        <TabsContent value="business-documents" className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-md font-medium">{t('admin.risks.attachments.business_documents', 'Business Documents')}</h3>
            <div>
              <input
                type="file"
                id="business-document-upload"
                multiple
                className="hidden"
                onChange={handleBusinessDocumentUpload}
                disabled={isLoading}
                accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.csv,.rtf,.odt,.ods,.odp,.jpg,.jpeg,.png,.gif,.bmp,.svg,.zip,.rar,.7z,.tar,.gz"
                ref={businessDocInputRef}
              />
              <label htmlFor="business-document-upload">
                <Button
                  type="button"
                  variant="outline"
                  className="flex items-center gap-2 cursor-pointer"
                  disabled={isLoading}
                  asChild
                >
                  <span>
                    {isLoading ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin" />
                        {t('admin.risks.attachments.uploading', 'Uploading...')} {uploadProgress > 0 ? `${uploadProgress}%` : ''}
                      </>
                    ) : (
                      <>
                        <Upload className="h-4 w-4" />
                        {t('admin.risks.attachments.upload_document', 'Upload Document')}
                      </>
                    )}
                  </span>
                </Button>
              </label>
            </div>
          </div>

          {/* No separate progress bar - it's now in the button */}

          {/* Business Documents List */}
          {businessDocuments.length === 0 ? (
            <div
              className={`text-center py-8 border-2 border-dashed rounded-lg transition-colors ${
                isDraggingBusiness ? 'border-blue-500 bg-blue-50' : 'border-gray-300'
              }`}
              onDragOver={handleBusinessDragOver}
              onDragEnter={handleBusinessDragEnter}
              onDragLeave={handleBusinessDragLeave}
              onDrop={handleBusinessDrop}
            >
              <File className="h-12 w-12 mx-auto text-gray-300 mb-2" />
              <p className="text-gray-500">{isDraggingBusiness ? t('admin.risks.attachments.drop_files', 'Drop files here') : t('admin.risks.attachments.no_business_docs', 'No business documents uploaded yet')}</p>
              <p className="text-sm text-gray-400">{t('admin.risks.attachments.drag_drop_hint', 'Drag & drop files here or click the upload button')}</p>
              <p className="text-xs text-gray-400 mt-2">{t('admin.risks.attachments.file_restrictions', 'Max file size: 50MB. Allowed file types: PDF, Office documents, images, archives.')}</p>
            </div>
          ) : (
            <div
              className={`border rounded-lg overflow-hidden transition-colors ${isDraggingBusiness ? 'border-blue-500 border-2' : ''}`}
              onDragOver={handleBusinessDragOver}
              onDragEnter={handleBusinessDragEnter}
              onDragLeave={handleBusinessDragLeave}
              onDrop={handleBusinessDrop}
            >
              {isDraggingBusiness && (
                <div className="absolute inset-0 bg-blue-50 bg-opacity-70 flex items-center justify-center z-10 pointer-events-none">
                  <div className="text-center">
                    <Upload className="h-12 w-12 mx-auto text-blue-500 mb-2" />
                    <p className="text-blue-600 font-medium">{t('admin.risks.attachments.drop_to_upload', 'Drop files to upload')}</p>
                  </div>
                </div>
              )}
              <table className="w-full relative">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">{t('admin.risks.attachments.name', 'Name')}</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">{t('admin.risks.attachments.size', 'Size')}</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">{t('admin.risks.attachments.date', 'Date')}</th>
                    <th className="px-4 py-3 text-right text-sm font-medium text-gray-600">{t('admin.risks.attachments.actions', 'Actions')}</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {businessDocuments.map((doc) => (
                    <tr key={doc.id} className="hover:bg-gray-50">
                      <td className="px-4 py-3 text-sm text-gray-900 flex items-center">
                        <File className="h-4 w-4 mr-2 text-gray-500" />
                        <span className="truncate max-w-[200px]">{doc.name}</span>
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-500">
                        {formatFileSize(doc.size)}
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-500">
                        {formatDate(doc.uploadDate)}
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-500 text-right">
                        <div className="flex justify-end space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => downloadAttachment(doc.id, doc.name)}
                            title={t('admin.risks.attachments.download', 'Download')}
                          >
                            <Download className="h-4 w-4 text-gray-500" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => deleteBusinessDocument(doc.id)}
                            title={t('admin.risks.attachments.delete', 'Delete')}
                          >
                            <Trash2 className="h-4 w-4 text-red-500" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </TabsContent>

        {/* External References Tab */}
        <TabsContent value="external-references" className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-md font-medium">{t('admin.risks.attachments.external_references', 'External References')}</h3>
            <div>
              <input
                type="file"
                id="external-reference-upload"
                multiple
                className="hidden"
                onChange={handleExternalReferenceUpload}
                disabled={isLoading}
                accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.csv,.rtf,.odt,.ods,.odp,.jpg,.jpeg,.png,.gif,.bmp,.svg,.zip,.rar,.7z,.tar,.gz"
                ref={externalRefInputRef}
              />
              <label htmlFor="external-reference-upload">
                <Button
                  type="button"
                  variant="outline"
                  className="flex items-center gap-2 cursor-pointer"
                  disabled={isLoading}
                  asChild
                >
                  <span>
                    {isLoading ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin" />
                        {t('admin.risks.attachments.uploading', 'Uploading...')} {uploadProgress > 0 ? `${uploadProgress}%` : ''}
                      </>
                    ) : (
                      <>
                        <Upload className="h-4 w-4" />
                        {t('admin.risks.attachments.upload_reference', 'Upload Reference')}
                      </>
                    )}
                  </span>
                </Button>
              </label>
            </div>
          </div>

          {/* No separate progress bar - it's now in the button */}

          {/* External References List */}
          {externalReferences.length === 0 ? (
            <div
              className={`text-center py-8 border-2 border-dashed rounded-lg transition-colors ${
                isDraggingExternal ? 'border-green-500 bg-green-50' : 'border-gray-300'
              }`}
              onDragOver={handleExternalDragOver}
              onDragEnter={handleExternalDragEnter}
              onDragLeave={handleExternalDragLeave}
              onDrop={handleExternalDrop}
            >
              <ExternalLink className="h-12 w-12 mx-auto text-gray-300 mb-2" />
              <p className="text-gray-500">{isDraggingExternal ? 'Drop files here' : 'No external references uploaded yet'}</p>
              <p className="text-sm text-gray-400">Drag & drop files here or click the upload button</p>
              <p className="text-xs text-gray-400 mt-2">Max file size: 50MB. Allowed file types: PDF, Office documents, images, archives.</p>
            </div>
          ) : (
            <div
              className={`border rounded-lg overflow-hidden transition-colors ${isDraggingExternal ? 'border-green-500 border-2' : ''}`}
              onDragOver={handleExternalDragOver}
              onDragEnter={handleExternalDragEnter}
              onDragLeave={handleExternalDragLeave}
              onDrop={handleExternalDrop}
            >
              {isDraggingExternal && (
                <div className="absolute inset-0 bg-green-50 bg-opacity-70 flex items-center justify-center z-10 pointer-events-none">
                  <div className="text-center">
                    <Upload className="h-12 w-12 mx-auto text-green-500 mb-2" />
                    <p className="text-green-600 font-medium">Drop files to upload</p>
                  </div>
                </div>
              )}
              <table className="w-full relative">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">Name</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">Size</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">Date</th>
                    <th className="px-4 py-3 text-right text-sm font-medium text-gray-600">Actions</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {externalReferences.map((ref) => (
                    <tr key={ref.id} className="hover:bg-gray-50">
                      <td className="px-4 py-3 text-sm text-gray-900 flex items-center">
                        <File className="h-4 w-4 mr-2 text-gray-500" />
                        <span className="truncate max-w-[200px]">{ref.name}</span>
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-500">
                        {formatFileSize(ref.size)}
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-500">
                        {formatDate(ref.uploadDate)}
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-500 text-right">
                        <div className="flex justify-end space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => downloadAttachment(ref.id, ref.name)}
                            title="Download"
                          >
                            <Download className="h-4 w-4 text-gray-500" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => deleteExternalReference(ref.id)}
                            title="Delete"
                          >
                            <Trash2 className="h-4 w-4 text-red-500" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
