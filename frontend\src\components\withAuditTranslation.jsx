import React from 'react';
import Translate<PERSON>rapper from './TranslateWrapper';

/**
 * Higher-Order Component that wraps audit pages with translation
 * @param {React.Component} Component - The component to wrap
 * @returns {React.Component} - The wrapped component with translation
 */
const withAuditTranslation = (Component) => {
  const WithAuditTranslation = (props) => {
    return (
      <TranslateWrapper>
        <Component {...props} />
      </TranslateWrapper>
    );
  };

  // Set display name for debugging
  WithAuditTranslation.displayName = `withAuditTranslation(${
    Component.displayName || Component.name || 'Component'
  })`;

  return WithAuditTranslation;
};

export default withAuditTranslation;
