import React, { useState, useEffect, useCallback, useRef } from "react";
import { Upload, File, Trash2, FileText, ExternalLink, Download, Loader2, Link, Plus } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "sonner";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";

export function AuditTravailFicheAttachmentsSection({ ficheTravail }) {
  const [businessDocuments, setBusinessDocuments] = useState([]);
  const [externalReferences, setExternalReferences] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [, setActiveTab] = useState("business-documents");
  const [isDraggingBusiness, setIsDraggingBusiness] = useState(false);
  const [isReferenceModalOpen, setIsReferenceModalOpen] = useState(false);
  const [newReference, setNewReference] = useState({ url: "", description: "" });

  const businessDocInputRef = useRef(null);

  // Mock data fetching
  const fetchAttachments = useCallback(async () => {
    setIsLoading(true);
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Mock initial data
    setBusinessDocuments([
      { id: "bd1", name: "rapport_audit_Q3.pdf", size: 1234567, uploadDate: new Date().toISOString() },
      { id: "bd2", name: "plan_actions.xlsx", size: 543210, uploadDate: new Date(Date.now() - 86400000).toISOString() },
    ]);
    setExternalReferences([
      { id: "er1", name: "Politique de sécurité", url: "https://example.com/security-policy", description: "Politique de sécurité de l'entreprise", createdAt: new Date().toISOString() },
      { id: "er2", name: "Guide de conformité", url: "https://docs.example.com/compliance-guide", description: "Guide de conformité RGPD", createdAt: new Date(Date.now() - 172800000).toISOString() },
    ]);
    setIsLoading(false);
  }, []);

  useEffect(() => {
    fetchAttachments();
  }, [fetchAttachments]);

  const handleBusinessDocumentUpload = async (e) => {
    const files = Array.from(e.target.files);
    if (files.length === 0) return;

    const allowedExtensions = [
      ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx",
      ".txt", ".csv", ".rtf", ".odt", ".ods", ".odp",
      ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".svg",
      ".zip", ".rar", ".7z", ".tar", ".gz",
    ];

    const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB
    const oversizedFiles = files.filter((file) => file.size > MAX_FILE_SIZE);
    if (oversizedFiles.length > 0) {
      const fileNames = oversizedFiles.map((file) => file.name).join(", ");
      toast.error(`Fichiers trop volumineux : ${fileNames}`, {
        description: `Les fichiers suivants dépassent la limite de 50MB : ${fileNames}`,
        duration: 5000,
      });
      e.target.value = "";
      return;
    }

    const invalidFiles = files.filter((file) => {
      const extension = "." + file.name.split(".").pop().toLowerCase();
      return !allowedExtensions.includes(extension);
    });
    if (invalidFiles.length > 0) {
      const fileNames = invalidFiles.map((file) => file.name).join(", ");
      toast.error(`Types de fichiers non supportés : ${fileNames}`, {
        description: `Les fichiers suivants ont des formats non supportés : ${fileNames}`,
        duration: 5000,
      });
      e.target.value = "";
      return;
    }

    setIsLoading(true);
    setUploadProgress(0);

    // Simulate upload progress
    for (let i = 0; i <= 100; i += 10) {
      await new Promise(resolve => setTimeout(resolve, 100));
      setUploadProgress(i);
    }

    const newDocs = files.map(file => ({
      id: `mock-bd-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      name: file.name,
      size: file.size,
      uploadDate: new Date().toISOString(),
    }));

    setBusinessDocuments(prev => [...prev, ...newDocs]);

    toast.success(`${files.length} document${files.length !== 1 ? "s" : ""} téléchargé${files.length !== 1 ? "s" : ""} avec succès`, {
      description: `${files.length} document${files.length !== 1 ? "s" : ""} téléchargé${files.length !== 1 ? "s" : ""} avec succès`,
      duration: 3000,
    });
    setIsLoading(false);
    setUploadProgress(0);
    e.target.value = "";
  };

  const handleAddExternalReference = async () => {
    if (!newReference.url || !newReference.url.trim()) {
      toast.error("L'URL est requise");
      return;
    }

    setIsLoading(true);
    await new Promise(resolve => setTimeout(resolve, 500)); // Simulate API call

    const formattedUrl = newReference.url.startsWith("http://") || newReference.url.startsWith("https://")
      ? newReference.url
      : `https://${newReference.url}`;

    const newRef = {
      id: `mock-er-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      name: newReference.description || new URL(formattedUrl).hostname,
      url: formattedUrl,
      description: newReference.description,
      createdAt: new Date().toISOString(),
    };

    setExternalReferences(prev => [...prev, newRef]);

    toast.success("Référence ajoutée avec succès");
    setIsReferenceModalOpen(false);
    setNewReference({ url: "", description: "" });
    setIsLoading(false);
  };

  const downloadAttachment = async (id, fileName) => {
    toast.info(`Téléchargement de ${fileName}... (Fonctionnalité non implémentée pour la maquette)`);
  };

  const deleteBusinessDocument = async (id) => {
    if (!window.confirm("Êtes-vous sûr de vouloir supprimer ce document ?")) return;

    setIsLoading(true);
    await new Promise(resolve => setTimeout(resolve, 300)); // Simulate API call
    setBusinessDocuments(prev => prev.filter(doc => doc.id !== id));
    toast.success("Document supprimé");
    setIsLoading(false);
  };

  const deleteExternalReference = async (id) => {
    if (!window.confirm("Êtes-vous sûr de vouloir supprimer cette référence ?")) return;

    setIsLoading(true);
    await new Promise(resolve => setTimeout(resolve, 300)); // Simulate API call
    setExternalReferences(prev => prev.filter(ref => ref.id !== id));
    toast.success("Référence supprimée");
    setIsLoading(false);
  };

  const openExternalReference = (url) => {
    const formattedUrl = url.startsWith("http://") || url.startsWith("https://") ? url : `https://${url}`;
    window.open(formattedUrl, "_blank", "noopener,noreferrer");
  };

  const formatFileSize = (bytes) => {
    if (bytes == null || isNaN(bytes)) return "N/A";
    if (bytes === 0) return "0 Octets";
    const k = 1024;
    const sizes = ["Octets", "Ko", "Mo", "Go"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString("fr-FR", {
        year: "numeric",
        month: "short",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      });
    } catch {
      return "Date invalide";
    }
  };

  const handleBusinessDragOver = (e) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleBusinessDragEnter = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingBusiness(true);
  };

  const handleBusinessDragLeave = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingBusiness(false);
  };

  const handleBusinessDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingBusiness(false);
    const files = Array.from(e.dataTransfer.files);
    if (files.length === 0) return;
    const event = { target: { files, value: "" } };
    handleBusinessDocumentUpload(event);
  };

  const handleReferenceInputChange = (e) => {
    const { name, value } = e.target;
    setNewReference((prev) => ({ ...prev, [name]: value }));
  };

  return (
    <div>
      <Tabs defaultValue="business-documents" className="w-full" onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-2 mb-4">
          <TabsTrigger value="business-documents" className="text-center">
            <FileText className="h-4 w-4 mr-2" />
            Documents métier
          </TabsTrigger>
          <TabsTrigger value="external-references" className="text-center">
            <ExternalLink className="h-4 w-4 mr-2" />
            Références externes
          </TabsTrigger>
        </TabsList>

        <TabsContent value="business-documents" className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-md font-medium">Documents métier</h3>
            <div>
              <input
                type="file"
                id="business-document-upload"
                multiple
                className="hidden"
                onChange={handleBusinessDocumentUpload}
                disabled={isLoading}
                accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.csv,.rtf,.odt,.ods,.odp,.jpg,.jpeg,.png,.gif,.bmp,.svg,.zip,.rar,.7z,.tar,.gz"
                ref={businessDocInputRef}
              />
              <label htmlFor="business-document-upload">
                <Button
                  type="button"
                  variant="outline"
                  className="flex items-center gap-2 cursor-pointer"
                  disabled={isLoading}
                  asChild
                >
                  <span>
                    {isLoading ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin" />
                        Téléchargement... {uploadProgress > 0 ? `${uploadProgress}%` : ""}
                      </>
                    ) : (
                      <>
                        <Upload className="h-4 w-4" />
                        Télécharger un document
                      </>
                    )}
                  </span>
                </Button>
              </label>
            </div>
          </div>

          {businessDocuments.length === 0 ? (
            <div
              className={`text-center py-8 border-2 border-dashed rounded-lg transition-colors ${
                isDraggingBusiness ? "border-blue-500 bg-blue-50" : "border-gray-300"
              }`}
              onDragOver={handleBusinessDragOver}
              onDragEnter={handleBusinessDragEnter}
              onDragLeave={handleBusinessDragLeave}
              onDrop={handleBusinessDrop}
            >
              <File className="h-12 w-12 mx-auto text-gray-300 mb-2" />
              <p className="text-gray-500">
                {isDraggingBusiness ? "Déposez les fichiers ici" : "Aucun document métier téléchargé pour le moment"}
              </p>
              <p className="text-sm text-gray-400">Glissez-déposez les fichiers ici ou cliquez sur le bouton de téléchargement</p>
              <p className="text-xs text-gray-400 mt-2">Taille maximale : 50MB. Types de fichiers autorisés : PDF, documents Office, images, archives.</p>
            </div>
          ) : (
            <div className="border rounded-lg overflow-hidden">
              <table className="w-full relative">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">Nom</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">Taille</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">Date</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">Actions</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {businessDocuments.map((doc) => (
                    <tr key={doc.id} className="hover:bg-gray-50">
                      <td className="px-4 py-3 text-sm">
                        <div className="flex items-center">
                          <FileText className="h-4 w-4 mr-2 text-blue-500" />
                          {doc.name}
                        </div>
                      </td>
                      <td className="px-4 py-3 text-sm">{formatFileSize(doc.size)}</td>
                      <td className="px-4 py-3 text-sm">{formatDate(doc.uploadDate)}</td>
                      <td className="px-4 py-3 text-sm">
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0"
                            onClick={() => downloadAttachment(doc.id, doc.name)}
                            title="Télécharger"
                          >
                            <Download className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 text-red-500 hover:text-red-700 hover:bg-red-50"
                            onClick={() => deleteBusinessDocument(doc.id)}
                            title="Supprimer"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </TabsContent>

        <TabsContent value="external-references" className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-md font-medium">Références externes</h3>
            <Button
              type="button"
              variant="outline"
              className="flex items-center gap-2"
              disabled={isLoading}
              onClick={() => setIsReferenceModalOpen(true)}
            >
              <Plus className="h-4 w-4" />
              Ajouter une référence
            </Button>
          </div>

          {externalReferences.length === 0 ? (
            <div className="text-center py-8 border-2 border-dashed rounded-lg border-gray-300">
              <ExternalLink className="h-12 w-12 mx-auto text-gray-300 mb-2" />
              <p className="text-gray-500">Aucune référence externe ajoutée pour le moment</p>
              <p className="text-sm text-gray-400">Cliquez sur le bouton 'Ajouter une référence' pour ajouter un lien</p>
            </div>
          ) : (
            <div className="border rounded-lg overflow-hidden">
              <table className="w-full relative">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">Nom</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">Description</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">Date</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">Actions</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {externalReferences.map((ref) => (
                    <tr key={ref.id} className="hover:bg-gray-50">
                      <td className="px-4 py-3 text-sm">
                        <div className="flex items-center">
                          <Link className="h-4 w-4 mr-2 text-green-500" />
                          <span className="text-blue-600 hover:underline cursor-pointer" onClick={() => openExternalReference(ref.url)}>
                            {ref.name || new URL(ref.url).hostname}
                          </span>
                        </div>
                      </td>
                      <td className="px-4 py-3 text-sm">{ref.description}</td>
                      <td className="px-4 py-3 text-sm">{formatDate(ref.createdAt)}</td>
                      <td className="px-4 py-3 text-sm">
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0"
                            onClick={() => openExternalReference(ref.url)}
                            title="Ouvrir le lien"
                          >
                            <ExternalLink className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 text-red-500 hover:text-red-700 hover:bg-red-50"
                            onClick={() => deleteExternalReference(ref.id)}
                            title="Supprimer"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </TabsContent>
      </Tabs>

      <Dialog open={isReferenceModalOpen} onOpenChange={setIsReferenceModalOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Ajouter une référence externe</DialogTitle>
            <DialogDescription>
              Ajoutez un lien vers une ressource externe liée à cette fiche de travail.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="url">
                URL <span className="text-red-500">*</span>
              </Label>
              <Input
                id="url"
                name="url"
                placeholder="https://www.exemple.com"
                value={newReference.url}
                onChange={handleReferenceInputChange}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                name="description"
                placeholder="Décrivez brièvement cette référence"
                value={newReference.description}
                onChange={handleReferenceInputChange}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsReferenceModalOpen(false)} type="button">
              Annuler
            </Button>
            <Button onClick={handleAddExternalReference} disabled={isLoading} type="button">
              {isLoading ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : null}
              Ajouter la référence
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
} 