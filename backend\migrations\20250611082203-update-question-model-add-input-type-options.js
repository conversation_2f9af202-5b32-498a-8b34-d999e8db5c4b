'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
    // 1. Rename 'content' to 'question_text'
    await queryInterface.renameColumn("Questions", "content", "question_text");
    // 2. Add 'input_type'
    await queryInterface.addColumn("Questions", "input_type", {
      type: Sequelize.STRING(32),
      allowNull: false,
      defaultValue: 'text'
    });
    // 3. Add 'options'
    await queryInterface.addColumn("Questions", "options", {
      type: Sequelize.JSONB,
      allowNull: true
    });
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
    // Revert 'options'
    await queryInterface.removeColumn("Questions", "options");
    // Revert 'input_type'
    await queryInterface.removeColumn("Questions", "input_type");
    // Rename 'question_text' back to 'content'
    await queryInterface.renameColumn("Questions", "question_text", "content");
  }
};
