// backend/scripts/run-migrations.js
require('dotenv').config({ path: 'backend/.env' });
const { sequelize, Sequelize } = require('../models');
const { DataTypes } = Sequelize;

async function runMigrations() {
  console.log('Starting migrations...');
  
  try {
    // Create Roles table
    console.log('Creating Roles table...');
    await sequelize.getQueryInterface().createTable('Roles', {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true
      },
      code: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    }).catch(error => {
      if (error.name === 'SequelizeUniqueConstraintError' || error.message.includes('already exists')) {
        console.log('Roles table already exists, skipping...');
      } else {
        throw error;
      }
    });

    // Seed initial roles
    console.log('Seeding initial roles...');
    await sequelize.getQueryInterface().bulkInsert('Roles', [
      {
        name: 'GRC Administrator',
        description: 'Full access to all GRC features and administrative functions',
        code: 'grc_admin',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'GRC Manager',
        description: 'Manages GRC processes and has access to most features',
        code: 'grc_manager',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'Risk Manager',
        description: 'Manages risks and related processes',
        code: 'risk_manager',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'GRC Contributor',
        description: 'Can contribute to GRC processes with limited access',
        code: 'grc_contributor',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ]).catch(error => {
      if (error.name === 'SequelizeUniqueConstraintError') {
        console.log('Roles already seeded, skipping...');
      } else {
        throw error;
      }
    });

    // Create UserRoles table
    console.log('Creating UserRoles table...');
    await sequelize.getQueryInterface().createTable('UserRoles', {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      userId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'Users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      roleId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'Roles',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    }).catch(error => {
      if (error.name === 'SequelizeUniqueConstraintError' || error.message.includes('already exists')) {
        console.log('UserRoles table already exists, skipping...');
      } else {
        throw error;
      }
    });

    // Add unique constraint to prevent duplicate user-role assignments
    console.log('Adding unique constraint to UserRoles table...');
    await sequelize.getQueryInterface().addConstraint('UserRoles', {
      fields: ['userId', 'roleId'],
      type: 'unique',
      name: 'unique_user_role'
    }).catch(error => {
      if (error.message.includes('already exists')) {
        console.log('Unique constraint already exists, skipping...');
      } else {
        throw error;
      }
    });

    console.log('Migrations completed successfully!');
  } catch (error) {
    console.error('Error running migrations:', error);
  } finally {
    await sequelize.close();
  }
}

// Run the migrations
runMigrations()
  .then(() => {
    console.log('Migration script completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('Migration script failed:', error);
    process.exit(1);
  });
