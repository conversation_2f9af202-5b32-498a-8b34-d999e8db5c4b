import { useOutletContext } from "react-router-dom";
import { Tag, Calendar, Hash, FileText, AlertCircle, Shield, Layers, Cpu, Building, FileType, ShieldCheck, Activity, Info, BarChart, CheckCircle, ScrollText, AlertTriangle, GitBranch, Users } from "lucide-react";
import { useEffect, useState } from "react";
import { RiskAttachmentsSection } from "../../../../components/risk/RiskAttachmentsSection";
import axios from "axios";
import { getApiBaseUrl } from "@/utils/api-config";
import { useTranslation } from 'react-i18next';

function RisksOverview() {
  const { t } = useTranslation();
  const { risk, referenceData, workflowState } = useOutletContext();
  const [relatedData, setRelatedData] = useState({
    businessProcess: null,
    organizationalProcess: null,
    operation: null,
    application: null,
    entity: null,
    riskType: null,
    control: null,
    actionPlan: null
  });
  const [contributors, setContributors] = useState([]);
  const [loadingContributors, setLoadingContributors] = useState(false);
  
  const API_BASE_URL = getApiBaseUrl();

  // Helper function to format dates
  const _formatDate = (dateString) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString("en-GB", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Helper function to render values with N/A styled in grey
  const renderValue = (value) => {
    if (value === "N/A" || value === null || value === undefined) {
      return <span className="text-gray-400">N/A</span>;
    }
    return value;
  };

  // Helper functions for impact, DMR, and probability labels
  const getImpactLabel = (value) => {
    if (!value) return { label: 'N/A', color: 'bg-gray-100 text-gray-500' };
    const impactLabels = {
      '1': { label: 'Very Low', color: 'bg-blue-100 text-blue-800' },
      '2': { label: 'Low', color: 'bg-green-100 text-green-800' },
      '3': { label: 'Medium', color: 'bg-yellow-100 text-yellow-800' },
      '4': { label: 'High', color: 'bg-orange-100 text-orange-800' },
      '5': { label: 'Very High', color: 'bg-red-100 text-red-800' }
    };
    return impactLabels[value.toString()] || { label: value, color: 'bg-gray-100 text-gray-500' };
  };

  const getControlLevelLabel = (value) => {
    if (!value) return { label: 'N/A', color: 'bg-gray-100 text-gray-500' };
    const controlLevelLabels = {
      '1': { label: 'Very Strong', color: 'bg-green-100 text-green-800' },
      '4': { label: 'Strong', color: 'bg-blue-100 text-blue-800' },
      '9': { label: 'Medium', color: 'bg-yellow-100 text-yellow-800' },
      '16': { label: 'Weak', color: 'bg-orange-100 text-orange-800' },
      '25': { label: 'Very Weak', color: 'bg-red-100 text-red-800' }
    };
    return controlLevelLabels[value.toString()] || { label: value, color: 'bg-gray-100 text-gray-500' };
  };

  const getProbabilityLabel = (value) => {
    if (!value) return { label: 'N/A', color: 'bg-gray-100 text-gray-500' };
    const probabilityLabels = {
      '1': { label: 'Very Low', color: 'bg-blue-100 text-blue-800' },
      '2': { label: 'Low', color: 'bg-green-100 text-green-800' },
      '3': { label: 'Medium', color: 'bg-yellow-100 text-yellow-800' },
      '4': { label: 'High', color: 'bg-orange-100 text-orange-800' },
      '5': { label: 'Very High', color: 'bg-red-100 text-red-800' }
    };
    return probabilityLabels[value.toString()] || { label: value, color: 'bg-gray-100 text-gray-500' };
  };

  const getAppetiteLabel = (value) => {
    if (!value) return { label: 'N/A', color: 'bg-gray-100 text-gray-500' };
    const appetiteLabels = {
      '1': { label: 'Very Low', color: 'bg-blue-100 text-blue-800' },
      '2': { label: 'Low', color: 'bg-green-100 text-green-800' },
      '3': { label: 'Medium', color: 'bg-yellow-100 text-yellow-800' },
      '4': { label: 'High', color: 'bg-orange-100 text-orange-800' },
      '5': { label: 'Very High', color: 'bg-red-100 text-red-800' }
    };
    return appetiteLabels[value.toString()] || { label: value, color: 'bg-gray-100 text-gray-500' };
  };

  // Helper function to get English label for inherent and residual risk
  const getRiskLevelLabel = (riskLabel) => {
    if (!riskLabel) return 'N/A';
    
    switch(riskLabel) {
      case 'Très élevé': return 'Very High';
      case 'Élevé': return 'High';
      case 'Moyen': return 'Medium';
      case 'Faible': return 'Low';
      default: return riskLabel;
    }
  };

  // Helper function to get color for risk level
  const getRiskLevelColor = (riskLabel) => {
    if (!riskLabel) return 'bg-gray-100 text-gray-800';
    
    switch(riskLabel) {
      case 'Très élevé':
      case 'Very High': return 'bg-red-100 text-red-800';
      case 'Élevé':
      case 'High': return 'bg-orange-100 text-orange-800';
      case 'Moyen':
      case 'Medium': return 'bg-yellow-100 text-yellow-800';
      case 'Faible':
      case 'Low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Helper function to get color for workflow state
  const getWorkflowStateColor = (state) => {
    if (!state) return 'bg-gray-100 text-gray-800';
    
    switch(state) {
      case 'Validated': return 'bg-green-100 text-green-800';
      case 'Rejected': return 'bg-red-100 text-red-800';
      case 'To Submit': return 'bg-blue-100 text-blue-800';
      case 'To Validate': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Find related data
  useEffect(() => {
    if (risk && referenceData) {
      const {
        businessProcesses,
        organizationalProcesses,
        operations,
        applications,
        entities,
        riskTypes,
        controls,
        actionPlans
      } = referenceData;

      setRelatedData({
        businessProcess: businessProcesses?.find(bp => bp.businessProcessID === risk.businessProcessID) || null,
        organizationalProcess: organizationalProcesses?.find(op => op.organizationalProcessID === risk.organizationalProcessID) || null,
        operation: operations?.find(op => op.operationID === risk.operationID) || null,
        application: applications?.find(app => app.applicationID === risk.applicationID) || null,
        entity: entities?.find(entity => entity.entityID === risk.entityID) || null,
        riskType: riskTypes?.find(rt => rt.riskTypeID === risk.riskTypeID) || null,
        control: controls?.find(control => control.controlID === risk.controlID) || null,
        actionPlan: actionPlans?.find(plan => plan.actionPlanID === risk.mitigatingActionPlan) || null
      });
    }
  }, [risk, referenceData]);

  // Fetch contributors
  useEffect(() => {
    const fetchContributors = async () => {
      if (!risk?.riskID) return;
      
      setLoadingContributors(true);
      try {
        const response = await axios.get(`${API_BASE_URL}/risks/${risk.riskID}/contributors`, {
          withCredentials: true,
          headers: { "Content-Type": "application/json" }
        });
        
        if (response.data.success) {
          setContributors(response.data.data || []);
        }
      } catch (error) {
        console.error("Error loading contributors:", error);
      } finally {
        setLoadingContributors(false);
      }
    };
    
    fetchContributors();
  }, [risk?.riskID, API_BASE_URL]);

  return (
    <div className="space-y-8">
      {/* Prominent Risk Information Cards */}
      <div className="flex flex-col md:flex-row gap-3">
        {/* Inherent Risk Card */}
        <div className={`rounded-lg shadow-md overflow-hidden w-fit ${
          getRiskLevelLabel(risk.inherentRiskLabel) === 'Very High' ? 'bg-gradient-to-br from-red-500 to-red-600' :
          getRiskLevelLabel(risk.inherentRiskLabel) === 'High' ? 'bg-gradient-to-br from-orange-500 to-orange-600' :
          getRiskLevelLabel(risk.inherentRiskLabel) === 'Medium' ? 'bg-gradient-to-br from-yellow-500 to-yellow-600' :
          getRiskLevelLabel(risk.inherentRiskLabel) === 'Low' ? 'bg-gradient-to-br from-green-500 to-green-600' :
          'bg-gradient-to-br from-gray-500 to-gray-600'
        }`}>
          <div className="p-3">
            <div className="flex items-center gap-2">
              <div className="flex-shrink-0 w-8 h-8 rounded-lg bg-white/20 flex items-center justify-center">
                <AlertTriangle className="h-4 w-4 text-white" />
              </div>
              <div>
                <h3 className="text-xs font-medium text-white/80">{t('admin.risks.overview.inherent_risk', 'Inherent Risk')}</h3>
                <div className="text-lg font-bold text-white">
                  {getRiskLevelLabel(risk.inherentRiskLabel) || t('admin.risks.overview.none', 'N/A')}
                </div>
              </div>
                </div>
              </div>
            </div>

        {/* Residual Risk Card */}
        <div className={`rounded-lg shadow-md overflow-hidden w-fit ${
          getRiskLevelLabel(risk.residualRiskLabel) === 'Very High' ? 'bg-gradient-to-br from-red-500 to-red-600' :
          getRiskLevelLabel(risk.residualRiskLabel) === 'High' ? 'bg-gradient-to-br from-orange-500 to-orange-600' :
          getRiskLevelLabel(risk.residualRiskLabel) === 'Medium' ? 'bg-gradient-to-br from-yellow-500 to-yellow-600' :
          getRiskLevelLabel(risk.residualRiskLabel) === 'Low' ? 'bg-gradient-to-br from-green-500 to-green-600' :
          'bg-gradient-to-br from-gray-500 to-gray-600'
        }`}>
          <div className="p-3">
            <div className="flex items-center gap-2">
              <div className="flex-shrink-0 w-8 h-8 rounded-lg bg-white/20 flex items-center justify-center">
                <AlertTriangle className="h-4 w-4 text-white" />
              </div>
              <div>
                <h3 className="text-xs font-medium text-white/80">{t('admin.risks.overview.residual_risk', 'Residual Risk')}</h3>
                <div className="text-lg font-bold text-white">
                  {getRiskLevelLabel(risk.residualRiskLabel) || t('admin.risks.overview.none', 'N/A')}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Key Info Section */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
        <div className="bg-gray-50 px-4 py-3 border-b">
          <h3 className="text-lg font-medium text-gray-800 flex items-center">
            <Info className="h-5 w-5 mr-2 text-blue-500" />
            {t('admin.risks.overview.key_information', 'Key Information')}
          </h3>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 p-4">
          <div className="flex items-start space-x-3">
            <div className="bg-purple-50 p-2 rounded-full">
              <Tag className="h-5 w-5 text-purple-500" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">{t('admin.risks.overview.name', 'Name')}</p>
              <p className="text-base font-semibold">{renderValue(risk.name || t('admin.risks.overview.none', 'N/A'))}</p>
            </div>
          </div>
          
          <div className="flex items-start space-x-3">
            <div className="bg-emerald-50 p-2 rounded-full">
              <Hash className="h-5 w-5 text-emerald-500" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">{t('admin.risks.overview.code', 'Code')}</p>
              <p className="text-base font-semibold">{renderValue(risk.code || t('admin.risks.overview.none', 'N/A'))}</p>
            </div>
          </div>
          
          <div className="flex items-start space-x-3">
            <div className="bg-blue-50 p-2 rounded-full">
              <GitBranch className="h-5 w-5 text-blue-500" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">{t('admin.risks.overview.status', 'Status')}</p>
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getWorkflowStateColor(workflowState)}`}>
                {workflowState || t('admin.risks.overview.none', 'N/A')}
              </span>
            </div>
          </div>
          
          <div className="flex items-start space-x-3">
            <div className="bg-red-50 p-2 rounded-full">
              <AlertCircle className="h-5 w-5 text-red-500" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">{t('admin.risks.overview.major_risk', 'Major Risk')}</p>
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded text-xs font-medium ${
                risk.major ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'
              }`}>
                {risk.major ? 'Yes' : 'No'}
              </span>
            </div>
          </div>

          {/* Contributors information */}
          <div className="flex items-start space-x-3">
            <div className="bg-indigo-50 p-2 rounded-full">
              <Users className="h-5 w-5 text-indigo-500" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">{t('admin.risks.overview.contributors', 'Contributors')}</p>
              <div>
                {loadingContributors ? (
                  <p className="text-sm text-gray-400">{t('admin.risks.overview.loading_contributors', 'Loading contributors...')}</p>
                ) : contributors.length > 0 ? (
                  <div className="flex flex-col space-y-1">
                    {contributors.slice(0, 3).map(contributor => (
                      <p key={contributor.id} className="text-sm">
                        {contributor.contributor?.username || t('admin.risks.overview.unknown_user', 'Unknown User')}
                      </p>
                    ))}
                    {contributors.length > 3 && (
                      <p className="text-xs text-gray-500">+{contributors.length - 3} more</p>
                    )}
                  </div>
                ) : (
                  <p className="text-sm text-gray-500">{t('admin.risks.overview.none', 'None')}</p>
                )}
              </div>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <div className="bg-indigo-50 p-2 rounded-full">
              <AlertCircle className="h-5 w-5 text-indigo-500" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">{t('admin.risks.overview.method_of_identification', 'Method of Identification')}</p>
              <p className="text-base font-semibold">
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  risk.methodOfIdentification === 'survey' ? 'bg-blue-100 text-blue-800' :
                  risk.methodOfIdentification === 'incident_database' ? 'bg-green-100 text-green-800' :
                  risk.methodOfIdentification === 'audit_mission' ? 'bg-purple-100 text-purple-800' :
                  risk.methodOfIdentification === 'workshop' ? 'bg-orange-100 text-orange-800' :
                  'bg-gray-100 text-gray-800'
                }`}>
                  {risk.methodOfIdentification ? risk.methodOfIdentification.replace('_', ' ').toUpperCase() : '-'}
                </span>
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Risk Assessment Section */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
        <div className="bg-gray-50 px-4 py-3 border-b">
          <h3 className="text-lg font-medium text-gray-800 flex items-center">
            <AlertTriangle className="h-5 w-5 mr-2 text-indigo-500" />
            {t('admin.risks.overview.risk_assessment', 'Risk Assessment')}
          </h3>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 p-4">
          <div className="flex items-start space-x-3">
            <div className="bg-amber-50 p-2 rounded-full">
              <AlertCircle className="h-5 w-5 text-amber-500" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">{t('admin.risks.overview.impact', 'Impact')}</p>
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded text-xs font-medium ${getImpactLabel(risk.impact).color}`}>
                {getImpactLabel(risk.impact).label}
              </span>
              </div>
            </div>

          <div className="flex items-start space-x-3">
            <div className="bg-blue-50 p-2 rounded-full">
              <AlertCircle className="h-5 w-5 text-blue-500" />
              </div>
              <div>
              <p className="text-sm font-medium text-gray-500">{t('admin.risks.overview.probability', 'Probability')}</p>
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded text-xs font-medium ${getProbabilityLabel(risk.probability).color}`}>
                  {getProbabilityLabel(risk.probability).label}
              </span>
            </div>
          </div>
          
          <div className="flex items-start space-x-3">
            <div className="bg-teal-50 p-2 rounded-full">
              <Shield className="h-5 w-5 text-teal-500" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">{t('admin.risks.overview.control_level', 'Control Level (DMR)')}</p>
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded text-xs font-medium ${getControlLevelLabel(risk.DMR).color}`}>
                {getControlLevelLabel(risk.DMR).label}
              </span>
            </div>
          </div>
          
          <div className="flex items-start space-x-3">
            <div className="bg-amber-50 p-2 rounded-full">
              <AlertTriangle className="h-5 w-5 text-amber-500" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">{t('admin.risks.overview.inherent_risk', 'Inherent Risk')}</p>
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded text-xs font-medium ${getRiskLevelColor(risk.inherentRiskLabel)}`}>
                {getRiskLevelLabel(risk.inherentRiskLabel)}
              </span>
            </div>
          </div>
          
          <div className="flex items-start space-x-3">
            <div className="bg-green-50 p-2 rounded-full">
              <AlertTriangle className="h-5 w-5 text-green-500" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">{t('admin.risks.overview.residual_risk', 'Residual Risk')}</p>
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded text-xs font-medium ${getRiskLevelColor(risk.residualRiskLabel)}`}>
                {getRiskLevelLabel(risk.residualRiskLabel)}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Risk Treatment Section */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
        <div className="bg-gray-50 px-4 py-3 border-b">
          <h3 className="text-lg font-medium text-gray-800 flex items-center">
            <Shield className="h-5 w-5 mr-2 text-teal-500" />
            {t('admin.risks.overview.mitigation', 'Mitigation')}
          </h3>
        </div>
        
        <div className="p-4">
          <div className="flex mb-4">
            {/* Appetite section */}
            <div className="mr-6 w-1/3">
              <div className="flex items-start space-x-3 mb-3">
                <div className="bg-pink-50 p-2 rounded-full">
                  <Activity className="h-5 w-5 text-pink-500" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">{t('admin.risks.overview.appetite', 'Appetite')}</p>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded text-xs font-medium ${getAppetiteLabel(risk.appetite).color}`}>
                    {getAppetiteLabel(risk.appetite).label}
                  </span>
                </div>
              </div>
            </div>

            {/* Vertical separator */}
            <div className="border-l border-gray-200 mx-4"></div>
            
            {/* Treatment options */}
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-500 mb-2">{t('admin.risks.overview.treatment_options', 'Treatment Options')}</p>
              <div className="flex flex-wrap gap-2">
                  {risk.acceptance && (
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                    <CheckCircle className="h-4 w-4 mr-1" /> {t('admin.risks.overview.acceptance', 'Acceptance')}
                    </span>
                  )}
                  {risk.avoidance && (
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
                    <CheckCircle className="h-4 w-4 mr-1" /> {t('admin.risks.overview.avoidance', 'Avoidance')}
                    </span>
                  )}
                  {risk.insurance && (
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                    <CheckCircle className="h-4 w-4 mr-1" /> {t('admin.risks.overview.insurance', 'Insurance')}
                    </span>
                  )}
                  {risk.reduction && (
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                    <CheckCircle className="h-4 w-4 mr-1" /> {t('admin.risks.overview.reduction', 'Reduction')}
                    </span>
                  )}
                  {!risk.acceptance && !risk.avoidance && !risk.insurance && !risk.reduction && (
                  <span className="text-gray-500">{t('admin.risks.overview.no_treatment_options', 'No treatment options specified')}</span>
                  )}
              </div>
            </div>
          </div>
          
          {/* Horizontal separator */}
          {risk.comment && <div className="border-t border-gray-200 my-4"></div>}
          
          {risk.comment && (
            <div className="mt-4">
              <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                <ScrollText className="h-4 w-4 mr-1 text-gray-500" /> {t('admin.risks.overview.comment', 'Comment')}
              </h4>
              <p className="text-sm text-gray-600 bg-gray-50 p-3 rounded-md">{risk.comment}</p>
            </div>
          )}
        </div>
      </div>

      {/* Related Information Section */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
        <div className="bg-gray-50 px-4 py-3 border-b">
          <h3 className="text-lg font-medium text-gray-800 flex items-center">
            <Layers className="h-5 w-5 mr-2 text-orange-500" />
            {t('admin.risks.overview.related_information', 'Related Information')}
          </h3>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 p-4">
          <div className="flex items-start space-x-3">
            <div className="bg-blue-50 p-2 rounded-full">
              <Layers className="h-5 w-5 text-blue-500" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">{t('admin.risks.overview.business_process', 'Business Process')}</p>
              <p className="text-base">{renderValue(relatedData.businessProcess?.name || t('admin.risks.overview.none', 'N/A'))}</p>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <div className="bg-indigo-50 p-2 rounded-full">
              <Layers className="h-5 w-5 text-indigo-500" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">{t('admin.risks.overview.organizational_process', 'Organizational Process')}</p>
              <p className="text-base">{renderValue(relatedData.organizationalProcess?.name || t('admin.risks.overview.none', 'N/A'))}</p>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <div className="bg-purple-50 p-2 rounded-full">
              <Layers className="h-5 w-5 text-purple-500" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">{t('admin.risks.overview.operation', 'Operation')}</p>
              <p className="text-base">{renderValue(relatedData.operation?.name || t('admin.risks.overview.none', 'N/A'))}</p>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <div className="bg-pink-50 p-2 rounded-full">
              <Cpu className="h-5 w-5 text-pink-500" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">{t('admin.risks.overview.application', 'Application')}</p>
              <p className="text-base">{renderValue(relatedData.application?.name || t('admin.risks.overview.none', 'N/A'))}</p>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <div className="bg-orange-50 p-2 rounded-full">
              <Building className="h-5 w-5 text-orange-500" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">{t('admin.risks.overview.entity', 'Entity')}</p>
              <p className="text-base">{renderValue(relatedData.entity?.name || t('admin.risks.overview.none', 'N/A'))}</p>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <div className="bg-red-50 p-2 rounded-full">
              <FileType className="h-5 w-5 text-red-500" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">{t('admin.risks.overview.risk_type', 'Risk Type')}</p>
              <p className="text-base">{renderValue(relatedData.riskType?.name || t('admin.risks.overview.none', 'N/A'))}</p>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <div className="bg-cyan-50 p-2 rounded-full">
              <ShieldCheck className="h-5 w-5 text-cyan-500" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">{t('admin.risks.overview.control', 'Control')}</p>
              <p className="text-base">{renderValue(relatedData.control?.name || t('admin.risks.overview.none', 'N/A'))}</p>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <div className="bg-lime-50 p-2 rounded-full">
              <FileText className="h-5 w-5 text-lime-500" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">{t('admin.risks.overview.mitigating_action_plan', 'Mitigating Action Plan')}</p>
              <p className="text-base">{renderValue(relatedData.actionPlan?.name || t('admin.risks.overview.none', 'N/A'))}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Attachments Section */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
        <div className="bg-gray-50 px-4 py-3 border-b">
          <h3 className="text-lg font-medium text-gray-800 flex items-center">
            <Calendar className="h-5 w-5 mr-2 text-gray-500" />
            {t('admin.risks.overview.attachments', 'Attachments')}
          </h3>
        </div>
        <div className="p-4">
        <RiskAttachmentsSection risk={risk} />
        </div>
      </div>
    </div>
  );
}

export default RisksOverview;
