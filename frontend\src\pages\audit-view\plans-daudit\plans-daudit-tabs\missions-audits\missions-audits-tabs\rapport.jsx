import React, { useState, useContext } from 'react';

import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { FileText, Save, Download, Upload } from "lucide-react";
import { toast } from "sonner";
import { useMissionAuditContext } from '@/utils/context-helpers';
import { OutletContext } from '@/pages/audit-view/missions-audits/edit-missions-audits';

function RapportTab(props) {
  const contextData = useMissionAuditContext();
  const missionAudit = props.missionAudit || contextData.missionAudit;
  const [reportContent, setReportContent] = useState({
    introduction: "",
    methodologie: "",
    constatations: "",
    recommendations: "",
    conclusion: ""
  });

  // If no mission data is available yet
  if (!missionAudit || !missionAudit.id) {
    return (
      <div className="flex items-center justify-center h-48">
        <p className="text-gray-500">Chargement du rapport d'audit...</p>
      </div>
    );
  }

  const handleSave = () => {
    toast.success("Rapport sauvegardé avec succès");
  };

  const handleInputChange = (field, value) => {
    setReportContent(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <div className="space-y-6 py-4">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-[#1A202C] flex items-center">
          <FileText className="h-6 w-6 mr-3 text-[#F62D51]" />
          Rapport d'Audit
        </h2>
        <div className="flex space-x-2">
          <Button variant="outline">
            <Upload className="h-4 w-4 mr-2" />
            Importer
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Exporter
          </Button>
          <Button onClick={handleSave} className="bg-[#F62D51] hover:bg-[#F62D51]/90">
            <Save className="h-4 w-4 mr-2" />
            Sauvegarder
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Informations du Rapport</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="reportTitle">Titre du Rapport</Label>
              <Input 
                id="reportTitle" 
                placeholder="Titre du rapport d'audit"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="reportDate">Date du Rapport</Label>
              <Input 
                id="reportDate" 
                type="date"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="reportAuthor">Auteur du Rapport</Label>
              <Input 
                id="reportAuthor" 
                placeholder="Nom de l'auteur principal"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="reportStatus">Statut du Rapport</Label>
              <Select>
                <SelectTrigger id="reportStatus">
                  <SelectValue placeholder="Sélectionner un statut" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="draft">Brouillon</SelectItem>
                  <SelectItem value="review">En revue</SelectItem>
                  <SelectItem value="final">Final</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="introduction" className="w-full">
        <TabsList className="grid grid-cols-5 mb-4">
          <TabsTrigger value="introduction">Introduction</TabsTrigger>
          <TabsTrigger value="methodologie">Méthodologie</TabsTrigger>
          <TabsTrigger value="constatations">Constatations</TabsTrigger>
          <TabsTrigger value="recommendations">Recommandations</TabsTrigger>
          <TabsTrigger value="conclusion">Conclusion</TabsTrigger>
        </TabsList>
        
        <TabsContent value="introduction">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Introduction</CardTitle>
            </CardHeader>
            <CardContent>
              <Textarea 
                placeholder="Rédigez l'introduction du rapport..."
                className="min-h-[200px]"
                value={reportContent.introduction}
                onChange={(e) => handleInputChange('introduction', e.target.value)}
              />
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="methodologie">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Méthodologie</CardTitle>
            </CardHeader>
            <CardContent>
              <Textarea 
                placeholder="Décrivez la méthodologie utilisée pour cette mission d'audit..."
                className="min-h-[200px]"
                value={reportContent.methodologie}
                onChange={(e) => handleInputChange('methodologie', e.target.value)}
              />
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="constatations">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Constatations</CardTitle>
            </CardHeader>
            <CardContent>
              <Textarea 
                placeholder="Présentez les principales constatations de l'audit..."
                className="min-h-[200px]"
                value={reportContent.constatations}
                onChange={(e) => handleInputChange('constatations', e.target.value)}
              />
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="recommendations">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Recommandations</CardTitle>
            </CardHeader>
            <CardContent>
              <Textarea 
                placeholder="Formulez les recommandations suite à l'audit..."
                className="min-h-[200px]"
                value={reportContent.recommendations}
                onChange={(e) => handleInputChange('recommendations', e.target.value)}
              />
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="conclusion">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Conclusion</CardTitle>
            </CardHeader>
            <CardContent>
              <Textarea 
                placeholder="Rédigez la conclusion de ce rapport d'audit..."
                className="min-h-[200px]"
                value={reportContent.conclusion}
                onChange={(e) => handleInputChange('conclusion', e.target.value)}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default RapportTab; 