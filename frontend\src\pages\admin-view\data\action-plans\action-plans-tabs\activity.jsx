import { useOutletContext } from "react-router-dom";
import { Activity } from "lucide-react";
import { useTranslation } from "react-i18next";

function ActionPlanActivity() {
  const { actionPlan } = useOutletContext();
  const { t } = useTranslation();

  return (
    <div>
      <h2 className="text-xl font-semibold mb-6">{t('admin.action_plans.tabs.activity', 'Activity')}</h2>

      <div className="flex flex-col items-center justify-center py-12 text-center">
        <Activity className="h-16 w-16 text-gray-300 mb-4" />
        <h3 className="text-lg font-medium text-gray-700 mb-2">{t('admin.action_plans.activity.coming_soon', 'Activity Tracking Coming Soon')}</h3>
        <p className="text-gray-500 max-w-md">
          {t('admin.action_plans.activity.description', 'This section will show activity logs and history for this action plan.')}
        </p>
      </div>
    </div>
  );
}

export default ActionPlanActivity;
