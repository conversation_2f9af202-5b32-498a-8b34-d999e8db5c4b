import { Navigate, useLocation } from "react-router-dom";
import { Loader2 } from "lucide-react";

// Helper function to check for Super Admin (roleId 1)
const isUserSuperAdmin = (user) => {
  if (!user || !user.roles || !Array.isArray(user.roles) || user.roles.length === 0) {
    return false;
  }
  return user.roles.some(role => Number(role.id) === 1);
};

// Helper function to check for <PERSON><PERSON> (roleId 2-10)
const isUserAdmin = (user) => {
  if (!user || !user.roles || !Array.isArray(user.roles) || user.roles.length === 0) {
    return false;
  }
  return user.roles.some(role => {
    const roleId = Number(role.id);
    return roleId >= 2 && roleId <= 10;
  });
};

// Helper function to check for Audit Director or Auditor roles
const isUserAuditor = (user) => {
  if (!user || !user.roles || !Array.isArray(user.roles) || user.roles.length === 0) {
    return false;
  }
  return user.roles.some(role =>
    role.code === 'audit_director' || role.code === 'auditor'
  );
};

function CheckAuth({ isAuthenticated, user, children }) {
  const location = useLocation();
  const currentPath = location.pathname;

  console.log(`[CheckAuth] Path: ${currentPath}, Auth: ${isAuthenticated}, UserID: ${user ? user.id : 'null'}`);
  console.log(`[CheckAuth] User roles:`, user?.roles);

  if (isAuthenticated === null) {
    console.log(`[CheckAuth] Auth state is null, showing loader`);
    return (
      <div className="h-screen w-full flex items-center justify-center bg-white">
        <Loader2 className="h-8 w-8 animate-spin text-[#F62D51]" />
      </div>
    );
  }

  const IS_SUPER_ADMIN = isUserSuperAdmin(user);
  const IS_ADMIN = isUserAdmin(user);
  const IS_AUDITOR = isUserAuditor(user);
  console.log(`[CheckAuth] Role checks: SuperAdmin=${IS_SUPER_ADMIN}, Admin=${IS_ADMIN}, Auditor=${IS_AUDITOR}`);

  if (!isAuthenticated) {
    if (!currentPath.startsWith("/auth")) {
      console.log(`[CheckAuth] Not authenticated, redirecting to /auth/login from ${currentPath}`);
      return <Navigate to="/auth/login" state={{ from: location }} replace />;
    }
    console.log(`[CheckAuth] Not authenticated but on auth path, allowing access`);
    return <>{children}</>;
  }

  // --- User IS Authenticated ---
  if (currentPath.startsWith("/auth")) {
    if (IS_SUPER_ADMIN) {
      console.log(`[CheckAuth] Authenticated SuperAdmin on /auth, redirecting to /super-admin/welcome`);
      return <Navigate to="/super-admin/welcome" replace />;
    }
    if (IS_AUDITOR) {
      console.log(`[CheckAuth] Authenticated Auditor on /auth, redirecting to /audit/welcome`);
      return <Navigate to="/audit/welcome" replace />;
    }
    console.log(`[CheckAuth] Authenticated Admin/Other on /auth, redirecting to /admin/welcome`);
    return <Navigate to="/admin/welcome" replace />;
  }

  if (IS_SUPER_ADMIN) {
    if (!currentPath.startsWith("/super-admin")) {
      console.log(`[CheckAuth] SuperAdmin on invalid path ${currentPath}, redirecting to /super-admin/welcome`);
      return <Navigate to="/super-admin/welcome" replace />;
    }
  } else if (IS_AUDITOR) {
    if (!currentPath.startsWith("/audit")) {
      console.log(`[CheckAuth] Auditor on invalid path ${currentPath}, redirecting to /audit/welcome`);
      return <Navigate to="/audit/welcome" replace />;
    }
  } else if (IS_ADMIN) {
    if (!currentPath.startsWith("/admin")) {
      console.log(`[CheckAuth] Admin on invalid path ${currentPath}, redirecting to /admin/welcome`);
      return <Navigate to="/admin/welcome" replace />;
    }
  } else {
    console.log(`[CheckAuth] User has no recognized role, redirecting to /admin/welcome`);
    return <Navigate to="/admin/welcome" replace />;
  }

  console.log(`[CheckAuth] Access granted to ${currentPath}`);
  return <>{children}</>;
}

export default CheckAuth;
export { isUserSuperAdmin, isUserAdmin, isUserAuditor };
