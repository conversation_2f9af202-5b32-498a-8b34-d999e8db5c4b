const express = require('express');
const router = express.Router();
const { verifyToken, authorizeRoles } = require('../../middleware/auth');
const {
  getAllAuditMissions,
  getAuditMissionsByPlanId,
  createAuditMission,
  getAuditMissionById,
  updateAuditMission,
  deleteAuditMission
} = require('../../controllers/audit/audit-mission-controller');

// Apply authentication middleware to all routes
router.use(verifyToken);

// Get all audit missions - both Audit Director and Auditor can access
router.get('/', authorizeRoles(['audit_director', 'auditor']),getAllAuditMissions);

// Get audit missions by plan ID - both Audit Director and Auditor can access
router.get('/plan/:planId', authorizeRoles(['audit_director', 'auditor']),getAuditMissionsByPlanId);

// Create new audit mission - both Audit Director and Auditor can access
router.post('/', authorizeRoles(['audit_director', 'auditor']), createAuditMission);

// Get audit mission by ID - both Audit Director and Auditor can access
router.get('/:id', authorizeRoles(['audit_director', 'auditor']),getAuditMissionById);

// Update audit mission - both Audit Director and Auditor can access
router.put('/:id', authorizeRoles(['audit_director', 'auditor']), updateAuditMission);

// Delete audit mission - only Audit Director can access
router.delete('/:id', authorizeRoles(['audit_director']), deleteAuditMission);

module.exports = router;
