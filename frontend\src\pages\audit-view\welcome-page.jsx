import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { ClipboardCheck, Calendar, FileText, AlertTriangle, BarChart, Users, CheckCircle } from 'lucide-react';

function AuditWelcome() {
  const navigate = useNavigate();
  const user = useSelector((state) => state.auth.user);
  
  // Determine if user is an Audit Director or Auditor
  const isAuditDirector = user?.roles?.some(role => role.code === 'audit_director');
  const roleTitle = isAuditDirector ? 'Audit Director' : 'Auditor';

  // Mock statistics for the welcome page
  const statistics = {
    completedAudits: 12,
    plannedAudits: 8,
    inProgressAudits: 4,
    highRiskFindings: 15,
    mediumRiskFindings: 23,
    lowRiskFindings: 42,
    openRecommendations: 37,
    closedRecommendations: 64
  };

  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <div className="bg-white rounded-lg p-6 shadow-sm">
        <h1 className="text-2xl font-bold mb-2">Bienvenue, {user?.username || 'User'}</h1>
        <p className="text-gray-500">
          Vous êtes connecté en tant que <span className="font-medium text-blue-600">{roleTitle}</span>
        </p>
        <div className="mt-4 flex flex-wrap gap-3">
          <Button 
            className="bg-red-500 hover:bg-red-600"
            onClick={() => navigate('/audit/plans-daudit')}
          >
            <ClipboardCheck className="mr-2 h-4 w-4" />
            Plans d'audit
          </Button>
          <Button variant="outline">
            <Calendar className="mr-2 h-4 w-4" />
            Calendrier d'audit
          </Button>
          <Button variant="outline">
            <FileText className="mr-2 h-4 w-4" />
            Rapports
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg font-medium">Audits terminés</CardTitle>
            <CardDescription>Total des audits terminés</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <CheckCircle className="h-8 w-8 text-green-500 mr-3" />
              <div className="text-3xl font-bold">{statistics.completedAudits}</div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg font-medium">Audits en cours</CardTitle>
            <CardDescription>Audits actuellement en cours</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Calendar className="h-8 w-8 text-blue-500 mr-3" />
              <div className="text-3xl font-bold">{statistics.inProgressAudits}</div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg font-medium">Audits planifiés</CardTitle>
            <CardDescription>Audits à venir</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <ClipboardCheck className="h-8 w-8 text-yellow-500 mr-3" />
              <div className="text-3xl font-bold">{statistics.plannedAudits}</div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg font-medium">Risques élevés</CardTitle>
            <CardDescription>Constatations à risque élevé</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <AlertTriangle className="h-8 w-8 text-red-500 mr-3" />
              <div className="text-3xl font-bold">{statistics.highRiskFindings}</div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Constatations par niveau de risque</CardTitle>
            <CardDescription>Répartition des constatations d'audit</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <div className="flex items-center">
                  <div className="w-3 h-3 rounded-full bg-red-500 mr-2"></div>
                  <span>Risque élevé</span>
                </div>
                <span className="font-medium">{statistics.highRiskFindings}</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2.5">
                <div className="bg-red-500 h-2.5 rounded-full" style={{ width: `${(statistics.highRiskFindings / (statistics.highRiskFindings + statistics.mediumRiskFindings + statistics.lowRiskFindings)) * 100}%` }}></div>
              </div>
              
              <div className="flex justify-between items-center">
                <div className="flex items-center">
                  <div className="w-3 h-3 rounded-full bg-yellow-500 mr-2"></div>
                  <span>Risque moyen</span>
                </div>
                <span className="font-medium">{statistics.mediumRiskFindings}</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2.5">
                <div className="bg-yellow-500 h-2.5 rounded-full" style={{ width: `${(statistics.mediumRiskFindings / (statistics.highRiskFindings + statistics.mediumRiskFindings + statistics.lowRiskFindings)) * 100}%` }}></div>
              </div>
              
              <div className="flex justify-between items-center">
                <div className="flex items-center">
                  <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                  <span>Risque faible</span>
                </div>
                <span className="font-medium">{statistics.lowRiskFindings}</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2.5">
                <div className="bg-green-500 h-2.5 rounded-full" style={{ width: `${(statistics.lowRiskFindings / (statistics.highRiskFindings + statistics.mediumRiskFindings + statistics.lowRiskFindings)) * 100}%` }}></div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Recommandations</CardTitle>
            <CardDescription>Statut des recommandations d'audit</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-center h-48">
              <div className="relative w-40 h-40">
                {/* Donut chart representation */}
                <svg viewBox="0 0 36 36" className="w-full h-full">
                  <path
                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                    fill="none"
                    stroke="#E5E7EB"
                    strokeWidth="3"
                  />
                  <path
                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                    fill="none"
                    stroke="#22C55E"
                    strokeWidth="3"
                    strokeDasharray={`${(statistics.closedRecommendations / (statistics.openRecommendations + statistics.closedRecommendations)) * 100}, 100`}
                    strokeLinecap="round"
                  />
                  <text x="18" y="20.5" textAnchor="middle" className="text-3xl font-bold fill-current">
                    {Math.round((statistics.closedRecommendations / (statistics.openRecommendations + statistics.closedRecommendations)) * 100)}%
                  </text>
                </svg>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4 mt-4">
              <div className="flex flex-col items-center p-3 bg-gray-50 rounded-lg">
                <div className="text-lg font-bold">{statistics.openRecommendations}</div>
                <div className="text-sm text-gray-500">Ouvertes</div>
              </div>
              <div className="flex flex-col items-center p-3 bg-gray-50 rounded-lg">
                <div className="text-lg font-bold">{statistics.closedRecommendations}</div>
                <div className="text-sm text-gray-500">Fermées</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Access Footer */}
      <div className="bg-white rounded-lg p-6 shadow-sm">
        <h2 className="text-lg font-bold mb-4">Accès rapide</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Button variant="outline" className="h-auto flex flex-col items-center p-4 justify-start">
            <ClipboardCheck className="h-8 w-8 mb-2" />
            <span>Nouveau plan d'audit</span>
          </Button>
          <Button variant="outline" className="h-auto flex flex-col items-center p-4 justify-start">
            <FileText className="h-8 w-8 mb-2" />
            <span>Générer un rapport</span>
          </Button>
          <Button variant="outline" className="h-auto flex flex-col items-center p-4 justify-start">
            <BarChart className="h-8 w-8 mb-2" />
            <span>Tableau de bord</span>
          </Button>
          <Button variant="outline" className="h-auto flex flex-col items-center p-4 justify-start">
            <Users className="h-8 w-8 mb-2" />
            <span>Équipe d'audit</span>
          </Button>
        </div>
      </div>
    </div>
  );
}

export default AuditWelcome;
