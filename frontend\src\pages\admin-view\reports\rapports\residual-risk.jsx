import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { jsPDF } from "jspdf";
import autoTable from "jspdf-autotable";
import html2pdf from "html2pdf.js";
import * as XLSX from "https://cdn.jsdelivr.net/npm/xlsx@0.18.5/xlsx.mjs";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { toast } from "react-hot-toast";
import { getApiBaseUrl } from "@/utils/api-config";
import axios from 'axios';
import EmailReportModal from '@/components/reports/EmailReportModal';

const ResidualRiskRapport = () => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedRisks, setSelectedRisks] = useState([]);
  const [modalTitle, setModalTitle] = useState("");
  const [modalLoading, setModalLoading] = useState(false);
  const navigate = useNavigate();
  const API_BASE_URL = getApiBaseUrl();
  const dmrValues = [1, 4, 9, 16, 25]; // Added to map column indices to new DMR values
  const [isEmailModalOpen, setIsEmailModalOpen] = useState(false);

  const handleEmailReport = () => {
    setIsEmailModalOpen(true);
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const response = await axios.get(`${API_BASE_URL}/residualRiskRapport`, {
          withCredentials: true,
          headers: { 
            "Content-Type": "application/json",
            "x-user-id": "1" 
          },
        });
        
        if (response.status !== 200) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const result = response.data;
        if (result.success) {
          setData(result.data);
        } else {
          throw new Error(result.message || 'Failed to fetch data');
        }
      } catch (error) {
        console.error("Error fetching ResidualRiskRapport:", error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const getCellClass = (inherentRisk, dmr) => {
    const matrix = [
      ["green", "green", "green", "green", "green"],        // Very High
      ["green", "lightgreen", "lightgreen", "lightgreen", "lightgreen"], // High
      ["lightgreen", "lightgreen", "yellow", "yellow", "yellow"], // Medium
      ["lightgreen", "yellow", "yellow", "orange", "orange"], // Low
      ["yellow", "yellow", "orange", "orange", "red"],          // Very Low
    ];

    const colorClass = matrix[inherentRisk][dmr];
    switch (colorClass) {
      case "red": return { backgroundColor: '#FF0000', color: '#FFFFFF' };
      case "orange": return { backgroundColor: '#FFA500', color: '#000000' };
      case "yellow": return { backgroundColor: '#FFFF00', color: '#000000' };
      case "lightgreen": return { backgroundColor: '#7FFF00', color: '#000000' };
      case "green": return { backgroundColor: '#008000', color: '#FFFFFF' };
      default: return { backgroundColor: '#FFFFFF', color: '#000000' };
    }
  };

  const applySafeStyles = (element, dataForPDF) => {
    const elements = element.querySelectorAll('*');
    const oklchStyles = [];
    const maxLogs = 5;
    const processedCells = [];

    console.log("Data for styling:", dataForPDF);
    if (dataForPDF) {
      dataForPDF.forEach((row, idx) => console.log(`Row ${idx} (irl=${4 - idx}):`, row));
    }

    // First pass: Reset styles
    elements.forEach(el => {
      const computedStyle = window.getComputedStyle(el);
      const properties = [
        'backgroundColor', 'color', 'borderColor', 'background', 
        'borderTopColor', 'borderRightColor', 'borderBottomColor', 'borderLeftColor',
        'boxShadow', 'textShadow'
      ];

      properties.forEach(prop => {
        const value = computedStyle[prop];
        if (value.includes('oklch') && oklchStyles.length < maxLogs) {
          oklchStyles.push({ element: el.tagName, class: el.className, property: prop, value });
        }
      });

      el.removeAttribute('class');
      el.style.background = 'none';
      el.style.backgroundColor = 'transparent';
      el.style.color = '#000000';
      el.style.border = 'none';
      el.style.borderColor = 'transparent';
      el.style.boxShadow = 'none';
      el.style.textShadow = 'none';
      el.style.transition = 'none';
    });

    // Second pass: Apply specific styles
    const tbody = element.querySelector('tbody');
    if (tbody) {
      const rows = Array.from(tbody.querySelectorAll('tr'));
      rows.forEach((tr, rowIndex) => {
        console.log(`Processing row ${rowIndex}: ${tr.querySelector('td')?.textContent || 'unknown'}`);
        const cells = Array.from(tr.querySelectorAll('td'));
        cells.forEach((td, cellIndex) => {
          td.style.border = '1px solid #E5E7EB';
          td.style.padding = '12px';
          td.style.textAlign = 'center';
          if (cellIndex === 0) {
            // Inherent Risk labels
            if (td.textContent.match(/Very High|High|Medium|Low|Very Low/)) {
              td.style.backgroundColor = '#FFFFFF';
              td.style.color = '#374151';
              td.style.fontWeight = '500';
            }
          } else {
            // Matrix cells
            const colIndex = cellIndex - 1; // Skip inherent risk column
            if (rowIndex < 5 && colIndex >= 0) {
              const inherentRisk = 4 - rowIndex; // Very High (4) to Very Low (0)
              const dmr = colIndex; // Effective (0) to Non-existent (4)
              const rowData = dataForPDF && dataForPDF[rowIndex] ? dataForPDF[rowIndex] : [0,0,0,0,0];
              const count = rowData[colIndex] || 0;
              const cellStyles = getCellClass(inherentRisk, dmr);
              td.style.backgroundColor = cellStyles.backgroundColor;
              td.style.color = cellStyles.color;
              td.style.setProperty('background-color', cellStyles.backgroundColor, 'important');
              td.style.setProperty('color', cellStyles.color, 'important');
              td.innerHTML = `<div style="display: flex; justify-content: center; align-items: center;"><span style="width: 32px; height: 32px; display: flex; justify-content: center; align-items: center; border-radius: 50%; font-weight: bold;">${count}</span></div>`;
              console.log(`Styled TD: row=${rowIndex}, col=${colIndex}, irl=${4 - rowIndex}, dmr=${dmr}, count=${count}, bg=${cellStyles.backgroundColor}, html=${td.outerHTML}`);
              processedCells.push({
                row: rowIndex,
                col: colIndex,
                irl: 4 - rowIndex,
                dmr: dmr,
                bg: cellStyles.backgroundColor,
                count: count
              });
            }
          }
        });
      });
    }

    // Style headers
    element.querySelectorAll('th').forEach(th => {
      if (th.textContent.match(/Inherent Risk|Risk Control Mechanism|Effective|Improvable|Somewhat Effective|Ineffective|Non-existent/)) {
        th.style.backgroundColor = '#1A2942';
        th.style.color = '#FFFFFF';
        th.style.fontWeight = '600';
        th.style.border = '1px solid #E5E7EB';
        th.style.padding = '12px';
        th.style.textAlign = 'center';
        th.style.setProperty('background-color', '#1A2942', 'important');
        th.style.setProperty('color', '#FFFFFF', 'important');
      }
    });

    // Style table
    element.querySelectorAll('table').forEach(table => {
      table.style.borderCollapse = 'collapse';
      table.style.width = '100%';
      table.style.marginBottom = '16px';
    });

    // Style title
    element.querySelectorAll('h2').forEach(h2 => {
      if (h2.textContent.includes('Residual Risk Matrix')) {
        h2.style.color = '#1A2942';
        h2.style.fontWeight = '700';
        h2.style.fontSize = '24px';
        h2.style.marginBottom = '16px';
        h2.style.setProperty('color', '#1A2942', 'important');
      }
    });

    // Remove existing legend
    const existingLegends = element.querySelectorAll('div:has(h3)');
    existingLegends.forEach(legend => {
      if (legend.querySelector('h3')?.textContent.includes('Legend')) {
        console.log("Removing existing legend:", legend.outerHTML.substring(0, 100) + "...");
        legend.remove();
      }
    });

    // Rebuild legend
    console.log("Rebuilding legend...");
    const legendContainer = document.createElement('div');
    legendContainer.style.marginTop = '16px';
    legendContainer.style.pageBreakBefore = 'avoid';
    const legendHeader = document.createElement('h3');
    legendHeader.textContent = 'Legend';
    legendHeader.style.color = '#1F2937';
    legendHeader.style.fontWeight = '600';
    legendHeader.style.fontSize = '18px';
    legendHeader.style.marginBottom = '12px';
    
    const legendGrid = document.createElement('div');
    legendGrid.style.display = 'flex';
    legendGrid.style.flexWrap = 'wrap';
    legendGrid.style.gap = '16px';
    
    const legendEntries = [
      { text: 'Critical', color: '#FF0000' },
      { text: 'High', color: '#FFA500' },
      { text: 'Medium', color: '#FFFF00' },
      { text: 'Low', color: '#7FFF00' },
      { text: 'Very Low', color: '#008000' }
    ];
    
    legendEntries.forEach(entry => {
      const itemDiv = document.createElement('div');
      itemDiv.style.display = 'flex';
      itemDiv.style.alignItems = 'center';
      itemDiv.style.backgroundColor = '#FFFFFF';
      itemDiv.style.minWidth = '120px';
      
      const dot = document.createElement('div');
      dot.style.width = '20px';
      dot.style.height = '20px';
      dot.style.borderRadius = '50%';
      dot.style.backgroundColor = entry.color;
      dot.style.marginRight = '8px';
      
      const span = document.createElement('span');
      span.textContent = entry.text;
      span.style.color = '#374151';
      span.style.fontSize = '14px';
      
      itemDiv.appendChild(dot);
      itemDiv.appendChild(span);
      legendGrid.appendChild(itemDiv);
      console.log(`Added legend item: ${entry.text}, color: ${entry.color}`);
    });
    
    legendContainer.appendChild(legendHeader);
    legendContainer.appendChild(legendGrid);
    element.appendChild(legendContainer);

    if (oklchStyles.length > 0) {
      console.warn("Detected oklch styles (showing up to 5):", oklchStyles);
    }
    console.log("Processed cells:", processedCells);
    console.log("Legend rebuilt with items:", legendEntries.map(e => e.text));

    return oklchStyles.length === 0;
  };

  const createStyleOverride = () => {
    const style = document.createElement('style');
    style.id = 'pdf-style-override';
    style.textContent = `
      *:not([style*="background-color"]) {
        background: none !important;
      }
      *:not([style*="color"]) {
        color: #000000 !important;
      }
      * {
        border-color: transparent !important;
        box-shadow: none !important;
        text-shadow: none !important;
      }
    `;
    return style;
  };

  const downloadMatrixAsPDF = async () => {
    try {
      if (!data || !data.length) {
        toast.error("No data available to generate the PDF.");
        return;
      }

      const pdf = new jsPDF({
        orientation: 'landscape',
        unit: 'in',
        format: 'letter'
      });

      // Add title
      pdf.setFontSize(16);
      pdf.setTextColor(26, 41, 66);
      pdf.text('Residual Risk Matrix', 0.5, 0.5);

      // Define table headers and rows
      const headers = [
        ['Inherent Risk', 'Risk Control Mechanism', '', '', '', ''],
        ['', 'Effective', 'Improvable', 'Somewhat Effective', 'Ineffective', 'Non-existent']
      ];
      const rows = ['Very High', 'High', 'Medium', 'Low', 'Very Low'].map((level, levelIndex) => {
        const rowData = data[levelIndex] || [0, 0, 0, 0, 0];
        return [level, ...rowData];
      });

      // Generate table with autoTable
      autoTable(pdf, {
        head: headers,
        body: rows,
        startY: 0.7,
        theme: 'striped',
        headStyles: {
          fillColor: [26, 41, 66],
          textColor: [255, 255, 255],
          fontSize: 10,
          fontStyle: 'bold',
          halign: 'center'
        },
        styles: {
          fontSize: 9,
          cellPadding: 0.1,
          font: 'helvetica',
          textColor: [31, 41, 55],
          overflow: 'linebreak'
        },
        columnStyles: {
          0: { cellWidth: 1.5, halign: 'left' }, // Inherent Risk
          1: { cellWidth: 1.5, halign: 'center' },
          2: { cellWidth: 1.5, halign: 'center' },
          3: { cellWidth: 1.5, halign: 'center' },
          4: { cellWidth: 1.5, halign: 'center' },
          5: { cellWidth: 1.5, halign: 'center' }
        },
        alternateRowStyles: {
          fillColor: [247, 250, 252]
        },
        margin: { left: 0.5, right: 0.5 },
        didParseCell: (data) => {
          if (data.section === 'body' && data.column.index > 0) {
            const inherentRisk = 4 - data.row.index; // Very High (4) to Very Low (0)
            const dmr = data.column.index - 1; // Effective (0) to Non-existent (4)
            const cellStyles = getCellClass(inherentRisk, dmr);
            data.cell.styles.fillColor = [
              parseInt(cellStyles.backgroundColor.slice(1, 3), 16),
              parseInt(cellStyles.backgroundColor.slice(3, 5), 16),
              parseInt(cellStyles.backgroundColor.slice(5, 7), 16)
            ];
            data.cell.styles.textColor = [
              parseInt(cellStyles.color.slice(1, 3), 16),
              parseInt(cellStyles.color.slice(3, 5), 16),
              parseInt(cellStyles.color.slice(5, 7), 16)
            ];
          }
        }
      });

      // Add legend
      let yPosition = pdf.lastAutoTable.finalY + 0.5;
      pdf.setFontSize(14);
      pdf.setTextColor(31, 41, 55);
      pdf.text('Legend', 0.5, yPosition);
      yPosition += 0.3;

      const legendEntries = [
        { text: 'Critical', color: [255, 0, 0] },
        { text: 'High', color: [255, 165, 0] },
        { text: 'Medium', color: [255, 255, 0] },
        { text: 'Low', color: [127, 255, 0] },
        { text: 'Very Low', color: [0, 128, 0] }
      ];

      legendEntries.forEach((entry, index) => {
        pdf.setFillColor(...entry.color);
        pdf.circle(0.7, yPosition - 0.05, 0.1, 'F');
        pdf.setFontSize(10);
        pdf.setTextColor(31, 41, 55);
        pdf.text(entry.text, 0.9, yPosition);
        yPosition += 0.2;
      });

      // Save the PDF
      pdf.save('residual_risk_matrix.pdf');
      toast.success("Matrix PDF downloaded successfully.");
    } catch (err) {
      console.error("Error generating matrix PDF:", err);
      toast.error("Error generating PDF. Please check the console.");
    }
  };

  const downloadPDF = async () => {
    const element = document.getElementById("residual-risk-matrix");
    if (!element) {
      console.error("Main matrix element not found");
      toast.error("Failed to generate PDF: Matrix not found.");
      return;
    }

    console.log("Data for PDF:", data);

    const tempContainer = document.createElement("div");
    tempContainer.style.position = "absolute";
    tempContainer.style.left = "-9999px";
    const clonedElement = element.cloneNode(true);
    const buttons = clonedElement.querySelectorAll('button');
    buttons.forEach(btn => btn.remove());
    tempContainer.appendChild(clonedElement);
    document.body.appendChild(tempContainer);

    const stylesSafe = applySafeStyles(clonedElement, data);
    console.log("Main matrix cloned with styles (snippet):", clonedElement.outerHTML.substring(0, 1000) + "...");

    const styleOverride = createStyleOverride();
    document.head.appendChild(styleOverride);

    const opt = {
      margin: 0.5,
      filename: 'residual_risk_report.pdf',
      image: { type: 'jpeg', quality: 0.95 },
      html2canvas: { 
        scale: 3,
        useCORS: true,
        logging: true,
        backgroundColor: '#FFFFFF',
        force: true
      },
      jsPDF: { unit: 'in', format: 'letter', orientation: 'landscape' }
    };

    try {
      if (!stylesSafe) {
        console.warn("Falling back to minimal styles due to oklch detection");
        clonedElement.querySelectorAll('*').forEach(el => {
          if (!el.style.backgroundColor) el.style.backgroundColor = '#FFFFFF';
          if (!el.style.color) el.style.color = '#000000';
          el.style.background = 'none';
          el.style.border = 'none';
        });
      }
      await html2pdf().set(opt).from(clonedElement).save();
      toast.success("Main matrix PDF downloaded successfully.");
    } catch (err) {
      console.error("Error generating main matrix PDF:", err);
      toast.error(`Failed to generate main matrix PDF: ${err.message.includes('oklch') ? 'Unsupported color format' : err.message}`);
    } finally {
      document.body.removeChild(tempContainer);
      if (styleOverride && styleOverride.parentNode) document.head.removeChild(styleOverride);
    }
  };

  const downloadMatrixAsExcel = () => {
    if (!data) {
      console.error("No data available for Excel download");
      toast.error("Failed to generate Excel: No data available.");
      return;
    }

    const filename = "residual_risk_matrix.xlsx";
    const headers = ["Inherent Risk", "Effective", "Improvable", "Somewhat Effective", "Ineffective", "Non-existent"];
    const rows = ["Very High", "High", "Medium", "Low", "Very Low"];
    
    const excelData = [
      headers,
      ...rows.map((rowLabel, rowIndex) => {
        const rowData = data[rowIndex] || [0, 0, 0, 0, 0];
        return [rowLabel, ...rowData];
      })
    ];

    const worksheet = XLSX.utils.aoa_to_sheet(excelData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "Residual Risk Matrix");

    worksheet['!cols'] = [
      { wch: 15 }, // Inherent Risk
      { wch: 10 }, // Effective
      { wch: 12 }, // Improvable
      { wch: 12 }, // Somewhat Effective
      { wch: 10 }, // Ineffective
      { wch: 10 }  // Non-existent
    ];

    XLSX.writeFile(workbook, filename);
    toast.success("Main matrix Excel downloaded successfully.");
  };

  const downloadTableAsPDF = async () => {
    const element = document.getElementById("risks-table");
    if (!element) {
      console.error("Modal table element not found");
      toast.error("Failed to generate PDF: Table not found.");
      return;
    }

    const tempContainer = document.createElement("div");
    tempContainer.style.position = "absolute";
    tempContainer.style.left = "-9999px";
    const clonedElement = element.cloneNode(true);
    tempContainer.appendChild(clonedElement);
    document.body.appendChild(tempContainer);

    const styleOverride = createStyleOverride();
    document.head.appendChild(styleOverride);
    const stylesSafe = applySafeStyles(clonedElement, null);
    console.log("Modal table cloned with styles:", clonedElement.outerHTML.substring(0, 100) + "...");

    const opt = {
      margin: 0.5,
      filename: `risks_irl${selectedRisks[0]?.inherentRiskLevel || 'x'}_dmr${selectedRisks[0]?.DMR || 'x'}.pdf`,
      image: { type: 'jpeg', quality: 0.95 },
      html2canvas: { 
        scale: 3,
        useCORS: true,
        logging: true,
        backgroundColor: '#FFFFFF',
        force: true
      },
      jsPDF: { unit: 'in', format: 'letter', orientation: 'portrait' }
    };

    try {
      if (!stylesSafe) {
        console.warn("Falling back to minimal styles due to oklch detection");
        clonedElement.querySelectorAll('*').forEach(el => {
          el.style.background = 'none';
          el.style.backgroundColor = '#FFFFFF';
          el.style.color = '#000000';
          el.style.border = 'none';
        });
      }
      await html2pdf().set(opt).from(clonedElement).save();
      toast.success("Modal table PDF downloaded successfully.");
    } catch (err) {
      console.error("Error generating modal table PDF:", err);
      toast.error(`Failed to generate modal table PDF: ${err.message.includes('oklch') ? 'Unsupported color format' : err.message}`);
    } finally {
      document.body.removeChild(tempContainer);
      if (styleOverride && styleOverride.parentNode) document.head.removeChild(styleOverride);
    }
  };

  const downloadTableAsExcel = () => {
    const filename = `risks_irl${selectedRisks[0]?.inherentRiskLevel || 'x'}_dmr${selectedRisks[0]?.DMR || 'x'}.xlsx`;
    let excelData;
    
    if (selectedRisks.length === 0) {
      excelData = [{ Name: "No risks available", Description: "", Probability: "", Impact: "" }];
    } else {
      excelData = selectedRisks.map(risk => ({
        Name: risk.name,
        Description: risk.description,
        Probability: risk.probability,
        Impact: risk.impact
      }));
    }

    const worksheet = XLSX.utils.json_to_sheet(excelData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "Risks");
    
    worksheet['!cols'] = [
      { wch: 30 }, // Name
      { wch: 50 }, // Description
      { wch: 12 }, // Probability
      { wch: 10 }  // Impact
    ];

    XLSX.writeFile(workbook, filename);
    toast.success("Modal table Excel downloaded successfully.");
  };

  const handleCellClick = async (inherentRiskLevel, DMR) => {
    console.log(`Clicked cell: InherentRiskLevel=${inherentRiskLevel}, DMR=${DMR}`);
    try {
      setModalLoading(true);
      setIsModalOpen(true);

      // Map string labels to integer values
      const inherentRiskLevelMap = {
        'Very High': 5,
        'High': 4,
        'Medium': 3,
        'Low': 2,
        'Very Low': 1
      };

      const inherentRiskLevelInt = inherentRiskLevelMap[inherentRiskLevel];
      if (!inherentRiskLevelInt) {
        throw new Error(`Invalid inherentRiskLevel: ${inherentRiskLevel}`);
      }

      // Map DMR values to labels for display in modal title
      const dmrLabels = {
        1: 'Effective',
        4: 'Improvable',
        9: 'Somewhat Effective',
        16: 'Ineffective',
        25: 'Non-existent'
      };

      setModalTitle(`Risks (Inherent Risk: ${inherentRiskLevel}, RCM: ${dmrLabels[DMR] || 'Unknown'})`);

      const response = await axios.get(`${API_BASE_URL}/risk/residual/${inherentRiskLevelInt}/${DMR}`, {
        withCredentials: true,
        headers: {
          "Content-Type": "application/json",
          "x-user-id": "1"
        },
      });

      if (response.status !== 200) {
        console.warn(`Failed to fetch risks: HTTP status ${response.status}`);
        setSelectedRisks([]);
        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      const risks = response.data;
      console.log(`Fetched ${risks.length} risks:`, risks);
      setSelectedRisks(risks);
      if (risks.length === 0) {
        toast("No risks found for this inherent risk level and RCM.");
      }
    } catch (error) {
      console.error("Error fetching risks:", error);
      setSelectedRisks([]);
      toast.error("Failed to fetch risks. Please try again later.");
    } finally {
      setModalLoading(false);
    }
  };

  const RisksTable = ({ risks }) => (
    <Table id="risks-table" className="border border-gray-200 rounded-lg w-full">
      <TableHeader>
        <TableRow className="bg-[#1A2942] text-white">
          <TableHead className="text-white font-semibold">Name</TableHead>
          <TableHead className="text-white font-semibold">Description</TableHead>
          <TableHead className="text-white font-semibold">Probability</TableHead>
          <TableHead className="text-white font-semibold">Impact</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {risks.length === 0 ? (
          <TableRow>
            <TableCell colSpan={4} className="text-center text-gray-500 py-4">
              No risks available for this selection
            </TableCell>
          </TableRow>
        ) : (
          risks.map((risk) => (
            <TableRow key={risk.riskID} className="hover:bg-gray-50">
              <TableCell className="font-medium text-gray-700">{risk.name}</TableCell>
              <TableCell 
                className="max-w-[300px] truncate text-gray-600 cursor-pointer hover:underline" 
                onClick={() => navigate(`/admin/risks/edit/${risk.riskID}`)}
              >
                {risk.description}
              </TableCell>
              <TableCell className="text-gray-600">{risk.probability}</TableCell>
              <TableCell className="text-gray-600">{risk.impact}</TableCell>
            </TableRow>
          ))
        )}
      </TableBody>
    </Table>
  );

  if (loading) return (
    <div className="p-6 flex justify-center items-center">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600"></div>
    </div>
  );

  if (error) return (
    <div className="p-6 text-red-500 text-center">
      Error loading report: {error}
    </div>
  );

  if (!data) return (
    <div className="p-6 text-gray-500 text-center">
      No data available for the report
    </div>
  );

  return (
    <div className="p-6 bg-gradient-to-b from-gray-50 to-gray-100 min-h-screen">
      <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
        <div id="residual-risk-matrix" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold text-[#1A2942]">Residual Risk Matrix</h2>
          </div>

          <div className="overflow-hidden rounded-xl border border-gray-200 shadow-sm">
            <div className="overflow-x-auto">
              <table className="w-full border-collapse text-center">
                <thead>
                  <tr>
                    <th rowSpan="2" className="border p-3 bg-[#1A2942] text-white font-semibold text-sm">Inherent Risk</th>
                    <th colSpan="5" className="border p-3 bg-[#1A2942] text-white font-semibold text-sm">Risk Control Mechanism</th>
                  </tr>
                  <tr>
                    {['Effective', 'Improvable', 'Somewhat Effective', 'Ineffective', 'Non-existent'].map((header) => (
                      <th key={header} className="border p-3 bg-[#1A2942] text-white font-semibold text-sm">{header}</th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {['Very High', 'High', 'Medium', 'Low', 'Very Low'].map((level, levelIndex) => (
                    <tr key={level} className={levelIndex % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                      <td className="border p-3 font-medium text-gray-700">{level}</td>
                      {Array.isArray(data[levelIndex]) && data[levelIndex].map((count, dmrIndex) => {
                        const inherentRisk = 4 - levelIndex;
                        const cellStyles = getCellClass(inherentRisk, dmrIndex);
                        return (
                          <td
                            key={`${level}-${dmrIndex}`}
                            style={{
                              backgroundColor: cellStyles.backgroundColor,
                              color: cellStyles.color,
                              border: '1px solid #E5E7EB',
                              padding: '12px',
                              textAlign: 'center'
                            }}
                            onClick={() => handleCellClick(level, dmrValues[dmrIndex])}
                          >
                            <div className="flex items-center justify-center">
                              <span className="inline-flex items-center justify-center w-8 h-8 rounded-full font-bold">
                                {count || 0}
                              </span>
                            </div>
                          </td>
                        );
                      })}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          <div className="legend-container">
            <h3 className="text-lg font-semibold mb-3 text-gray-800">Legend</h3>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
              <div className="flex items-center bg-white p-2 rounded-lg shadow-sm">
                <div className="w-5 h-5 rounded-full bg-[#FF0000] mr-2"></div>
                <span className="text-sm">Critical</span>
              </div>
              <div className="flex items-center bg-white p-2 rounded-lg shadow-sm">
                <div className="w-5 h-5 rounded-full bg-[#FFA500] mr-2"></div>
                <span className="text-sm">High</span>
              </div>
              <div className="flex items-center bg-white p-2 rounded-lg shadow-sm">
                <div className="w-5 h-5 rounded-full bg-[#FFFF00] mr-2"></div>
                <span className="text-sm">Medium</span>
              </div>
              <div className="flex items-center bg-white p-2 rounded-lg shadow-sm">
                <div className="w-5 h-5 rounded-full bg-[#7FFF00] mr-2"></div>
                <span className="text-sm">Low</span>
              </div>
              <div className="flex items-center bg-white p-2 rounded-lg shadow-sm">
                <div className="w-5 h-5 rounded-full bg-[#008000] mr-2"></div>
                <span className="text-sm">Very Low</span>
              </div>
            </div>
          </div>
          <div className="flex gap-4">
            <Button 
              onClick={downloadMatrixAsPDF} 
              className="mt-4 bg-[#22c55e] hover:bg-[#16a34a] text-white"
            >
              Download as PDF
            </Button>
            
            <Button
              onClick={downloadMatrixAsExcel}
              className="mt-4 bg-[#22c55e] hover:bg-[#16a34a] text-white"
            >
              Download as Excel
            </Button>
            
            <Button
              onClick={handleEmailReport}
              className="mt-4 bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
              </svg>
              Send via Email
            </Button>
          </div>
        </div>
      </div>

      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="w-fit min-w-[400px] max-w-[90vw] h-fit max-h-[90vh] overflow-y-auto bg-white rounded-xl p-6">
          <DialogHeader>
            <DialogTitle className="text-2xl font-bold text-[#1A2942]">{modalTitle}</DialogTitle>
          </DialogHeader>
          <div className="mt-4">
            {modalLoading ? (
              <div className="flex justify-center items-center py-4">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-teal-600"></div>
              </div>
            ) : (
              <>
                <RisksTable risks={selectedRisks} />
                <div className="flex justify-end gap-4 mt-4">
                  <Button
                    onClick={downloadTableAsExcel}
                    disabled={modalLoading}
                    className="bg-[#22c55e] hover:bg-[#16a34a] text-white"
                  >
                    Download as Excel
                  </Button>
                  <Button
                    onClick={downloadTableAsPDF}
                    disabled={modalLoading}
                    className="bg-[#22c55e] hover:bg-[#16a34a] text-white"
                  >
                    Download as PDF
                  </Button>
                </div>
              </>
            )}
          </div>
        </DialogContent>
      </Dialog>
      <EmailReportModal
        isOpen={isEmailModalOpen}
        onClose={() => setIsEmailModalOpen(false)}
        reportType="residual-risk"
        reportTitle="Residual Risk Matrix"
        reportData={data}
      />
    </div>
  );
};

export default ResidualRiskRapport;
