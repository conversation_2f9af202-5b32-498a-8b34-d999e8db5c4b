import { useOutletContext } from "react-router-dom";
import { GitBranch } from "lucide-react"; // Icon for Workflows

function WorkflowsTab() {
  const { auditPlan } = useOutletContext();

  if (!auditPlan) {
    return (
      <div className="text-center py-10">
        <p className="text-gray-500">Chargement des informations du plan d'audit...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6 py-4">
      <div className="bg-white p-8 rounded-lg shadow text-center">
        <GitBranch className="h-12 w-12 mx-auto text-gray-300 mb-4" />
        <h2 className="text-xl font-semibold text-[#1A202C] mb-2">
          Workflows
        </h2>
        <p className="text-md text-gray-600">
          Contenu pour les workflows du plan d'audit: <span className="font-medium">{auditPlan.name}</span>.
        </p>
        <p className="text-sm text-gray-400 mt-4">
          (Ce composant est un placeholder. Le contenu spécifique sera ajouté ultérieurement.)
        </p>
      </div>
    </div>
  );
}

export default WorkflowsTab; 