import axios from 'axios';
import { getApiEndpointUrl, getAuthHeaders } from '@/utils/api-config';

// Get all constats
export const getAllConstats = async (signal) => {
  try {
    const response = await axios.get(
      getApiEndpointUrl('audit-constats'),
      { 
        headers: getAuthHeaders(),
        signal 
      }
    );
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    throw error;
  }
};

// Get constat by ID
export const getAuditConstatById = async (id, signal) => {
  try {
    const response = await axios.get(
      getApiEndpointUrl(`audit-constats/${id}`),
      { 
        headers: getAuthHeaders(),
        signal 
      }
    );
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    throw error;
  }
};

// Get constats by activity ID
export const getConstatsByActivityId = async (activityId, signal) => {
  try {
    const response = await axios.get(
      getApiEndpointUrl(`audit-constats/activity/${activityId}`),
      { 
        headers: getAuthHeaders(),
        signal 
      }
    );
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    throw error;
  }
};

// Create new constat
export const createConstat = async (constatData, signal) => {
  try {
    const response = await axios.post(
      getApiEndpointUrl('audit-constats'),
      constatData,
      { 
        headers: getAuthHeaders(),
        signal 
      }
    );
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    throw error;
  }
};

// Alias for backward compatibility
export const createAuditConstat = createConstat;

// Update constat
export const updateConstat = async (id, constatData, signal) => {
  try {
    const response = await axios.put(
      getApiEndpointUrl(`audit-constats/${id}`),
      constatData,
      { 
        headers: getAuthHeaders(),
        signal 
      }
    );
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    throw error;
  }
};

// Alias for backward compatibility
export const updateAuditConstat = updateConstat;

// Delete constat
export const deleteConstat = async (id, signal) => {
  try {
    const response = await axios.delete(
      getApiEndpointUrl(`audit-constats/${id}`),
      { 
        headers: getAuthHeaders(),
        signal 
      }
    );
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    throw error;
  }
};

// Alias for backward compatibility
export const deleteAuditConstat = deleteConstat;

// Link constat to recommendation
export const linkConstatToRecommendation = async (constatId, recommendationId, signal) => {
  try {
    const response = await axios.put(
      getApiEndpointUrl(`audit-constats/${constatId}/link-recommendation`),
      { recommendationId },
      { 
        headers: getAuthHeaders(),
        signal 
      }
    );
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    throw error;
  }
};

// Unlink constat from recommendation
export const unlinkConstatFromRecommendation = async (constatId, recommendationId, signal) => {
  try {
    const response = await axios.put(
      getApiEndpointUrl(`audit-constats/${constatId}/unlink-recommendation`),
      { recommendationId },
      { 
        headers: getAuthHeaders(),
        signal 
      }
    );
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    throw error;
  }
}; 