import { useState, useEffect, useCallback, useRef } from "react";
import axios from "axios";
import { Upload, File, Trash2, FileText, ExternalLink, Download, Loader2, Link, Plus } from "lucide-react";
import { Button } from "../../components/ui/button";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "../../components/ui/tabs";
import { toast } from "sonner";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "../../components/ui/dialog";
import { Label } from "../../components/ui/label";
import { Input } from "../../components/ui/input";
import { Textarea } from "../../components/ui/textarea";
import { getApiBaseUrl } from '../../utils/api-config';
import { useTranslation } from "react-i18next";

export function AttachmentsSection({ incident }) {
  const [businessDocuments, setBusinessDocuments] = useState([]);
  const [externalReferences, setExternalReferences] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  // We need to keep this state for the Tabs component even though it's not directly used
  const [, setActiveTab] = useState("business-documents");
  const [isDraggingBusiness, setIsDraggingBusiness] = useState(false);
  
  // State for reference modal
  const [isReferenceModalOpen, setIsReferenceModalOpen] = useState(false);
  const [newReference, setNewReference] = useState({
    url: "",
    description: ""
  });
  // API Base URL - use dynamic URL from utility function
  const API_BASE_URL = getApiBaseUrl();
  // Refs for the file inputs
  const businessDocInputRef = useRef(null);

  // Fetch attachments from the server
  const fetchAttachments = useCallback(async () => {
    if (!incident?.incidentID) return;

    try {
      setIsLoading(true);

      // Add timestamp to prevent caching
      const timestamp = new Date().getTime();

      // Fetch business documents
      const businessDocsResponse = await axios.get(`${API_BASE_URL}/uploads?incidentID=${incident.incidentID}&type=business-document&_t=${timestamp}`, {
        withCredentials: true,
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      });

      // Fetch external references
      const externalRefsResponse = await axios.get(`${API_BASE_URL}/references?incidentID=${incident.incidentID}&_t=${timestamp}`, {
        withCredentials: true,
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      });

      if (businessDocsResponse.data.success) {
        setBusinessDocuments(businessDocsResponse.data.data);
      }

      if (externalRefsResponse.data.success) {
        setExternalReferences(externalRefsResponse.data.data);
      }
    } catch (error) {
      console.error('Error fetching attachments:', error);
      toast.error(t('admin.incidents.attachments.failedToLoadAttachments', 'Failed to load attachments'), {
        description: 'Please try refreshing the page',
        duration: 5000,
      });
    } finally {
      setIsLoading(false);
    }
  }, [incident]);

  // Fetch attachments when component mounts or incident changes
  useEffect(() => {
    if (incident?.incidentID) {
      fetchAttachments();

      // Set up polling to check for new attachments every 30 seconds
      const pollingInterval = setInterval(() => {
        fetchAttachments();
      }, 30000); // 30 seconds

      // Clean up interval on component unmount
      return () => clearInterval(pollingInterval);
    }
  }, [incident?.incidentID, fetchAttachments]);

  // Handle file upload for business documents
  const handleBusinessDocumentUpload = async (e) => {
    const files = Array.from(e.target.files);
    if (files.length === 0) return;

    // Define allowed file extensions
    const allowedExtensions = [
      '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
      '.txt', '.csv', '.rtf', '.odt', '.ods', '.odp',
      '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg',
      '.zip', '.rar', '.7z', '.tar', '.gz'
    ];

    // Check file size before uploading (50MB limit)
    const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB in bytes
    const oversizedFiles = files.filter(file => file.size > MAX_FILE_SIZE);

    if (oversizedFiles.length > 0) {
      const fileNames = oversizedFiles.map(file => file.name).join(', ');
      toast.error(t('admin.incidents.attachments.filesTooLarge', { fileNames }), {
        description: `The following files exceed the 50MB limit: ${fileNames}`,
        duration: 5000,
      });
      e.target.value = ""; // Reset input
      return;
    }

    // Check file types
    const invalidFiles = files.filter(file => {
      const extension = '.' + file.name.split('.').pop().toLowerCase();
      return !allowedExtensions.includes(extension);
    });

    if (invalidFiles.length > 0) {
      const fileNames = invalidFiles.map(file => file.name).join(', ');
      toast.error(t('admin.incidents.attachments.unsupportedFileTypes', { fileNames }), {
        description: `The following files have unsupported formats: ${fileNames}`,
        duration: 5000,
      });
      e.target.value = ""; // Reset input
      return;
    }

    try {
      setIsLoading(true);
      setUploadProgress(0);

      const formData = new FormData();
      formData.append('type', 'business-document');
      formData.append('incidentID', incident.incidentID);

      files.forEach(file => {
        formData.append('files', file);
      });

      const response = await axios.post(`${API_BASE_URL}/uploads`, formData, {
        withCredentials: true,
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        onUploadProgress: (progressEvent) => {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          setUploadProgress(percentCompleted);
        }
      });

      if (response.data.success) {
        toast.success(t('admin.incidents.attachments.uploadSuccessful', { filesLength: files.length }), {
          description: `${files.length} document${files.length !== 1 ? 's' : ''} uploaded successfully`,
          duration: 3000,
        });
        fetchAttachments(); // Refresh the list
      }
    } catch (error) {
      console.error('Error uploading business documents:', error);

      // Handle specific error types
      if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
        toast.error(t('admin.incidents.attachments.uploadTimedOut', 'Upload timed out. Please try again with smaller files or a better connection.'), {
          description: 'The server took too long to respond',
          duration: 5000,
        });
      } else if (error.response?.status === 413) {
        toast.error(t('admin.incidents.attachments.fileTooLarge', 'File too large'), {
          description: 'The file exceeds the maximum size limit of 50MB',
          duration: 5000,
        });
      } else if (error.response?.data?.message?.includes('file type')) {
        toast.error(t('admin.incidents.attachments.unsupportedFileType', { message: error.response.data.message }), {
          description: error.response.data.message,
          duration: 5000,
        });
      } else {
        const errorMsg = error.response?.data?.message || 'Failed to upload documents';
        toast.error(t('admin.incidents.attachments.uploadFailed', { errorMsg }), {
          description: errorMsg,
          duration: 5000,
        });
        console.error('Upload error:', errorMsg);
      }
    } finally {
      setIsLoading(false);
      setUploadProgress(0);
      e.target.value = ""; // Reset input
    }
  };

  // Handle creation of external reference (URL)
  const handleAddExternalReference = async () => {
    // Only validate that URL is not empty
    if (!newReference.url || !newReference.url.trim()) {
      toast.error(t('admin.incidents.attachments.urlRequired', 'URL is required'));
      return;
    }

    try {
      setIsLoading(true);

      const response = await axios.post(`${API_BASE_URL}/references`, {
        incidentID: incident.incidentID,
        url: newReference.url,
        description: newReference.description,
      }, {
        withCredentials: true,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (response.data.success) {
        toast.success(t('admin.incidents.attachments.referenceAddedSuccessfully', 'Reference added successfully'));
        setIsReferenceModalOpen(false);
        setNewReference({ url: "", description: "" });
        fetchAttachments(); // Refresh the list
      }
    } catch (error) {
      console.error('Error adding external reference:', error);
      toast.error(t('admin.incidents.attachments.failedToAddReference', 'Failed to add reference'), {
        description: error.response?.data?.message || 'An error occurred',
          duration: 5000,
        });
    } finally {
      setIsLoading(false);
    }
  };

  // Download a business document
  const downloadAttachment = async (id, fileName) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/uploads/download/${id}`, {
        withCredentials: true,
        responseType: 'blob'
      });

      // Create a blob URL and trigger download
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', fileName);
      document.body.appendChild(link);
      link.click();
      link.remove();
    } catch (error) {
      console.error('Error downloading attachment:', error);
      toast.error(t('admin.incidents.attachments.downloadFailed', 'Download failed'), {
        description: 'Unable to download the file. Please try again later.',
        duration: 5000,
      });
    }
  };

  // Delete a business document
  const deleteBusinessDocument = async (id) => {
    if (!window.confirm(t('admin.incidents.attachments.confirmDeleteDocument', 'Are you sure you want to delete this document?'))) return;

    try {
      const response = await axios.delete(`${API_BASE_URL}/uploads/${id}`, {
        withCredentials: true
      });

      if (response.data.success) {
        toast.success(t('admin.incidents.attachments.documentDeleted', 'Document deleted'), {
          description: 'The document was successfully deleted',
          duration: 3000,
        });
        fetchAttachments(); // Refresh the list
      }
    } catch (error) {
      console.error('Error deleting document:', error);
      toast.error(t('admin.incidents.attachments.deleteFailed', 'Delete failed'), {
        description: 'Unable to delete the document. Please try again later.',
        duration: 5000,
      });
    }
  };

  // Delete an external reference
  const deleteExternalReference = async (id) => {
    if (!window.confirm(t('admin.incidents.attachments.confirmDeleteReference', 'Are you sure you want to delete this reference?'))) return;

    try {
      const response = await axios.delete(`${API_BASE_URL}/references/${id}`, {
        withCredentials: true
      });

      if (response.data.success) {
        toast.success(t('admin.incidents.attachments.referenceDeleted', 'Reference deleted'), {
          description: 'The reference was successfully deleted',
          duration: 3000,
        });
        fetchAttachments(); // Refresh the list
      }
    } catch (error) {
      console.error('Error deleting reference:', error);
      toast.error(t('admin.incidents.attachments.deleteFailed', 'Delete failed'), {
        description: 'Unable to delete the reference. Please try again later.',
        duration: 5000,
      });
    }
  };

  // Open an external reference in a new tab
  const openExternalReference = (url) => {
    // Ensure URL has protocol
    let formattedUrl = url;
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      formattedUrl = `https://${url}`;
    }
    window.open(formattedUrl, '_blank', 'noopener,noreferrer');
  };

  // Format file size
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Handle drag events for business documents
  const handleBusinessDragOver = (e) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleBusinessDragEnter = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingBusiness(true);
  };

  const handleBusinessDragLeave = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingBusiness(false);
  };

  const handleBusinessDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingBusiness(false);

    const files = Array.from(e.dataTransfer.files);
    if (files.length === 0) return;

    // Simulate file input change
      const dataTransfer = new DataTransfer();
      files.forEach(file => dataTransfer.items.add(file));

    // Create a fake event object
    const event = {
      target: {
        files: dataTransfer.files,
        value: '' // This will be reset in the upload function
      }
    };

    handleBusinessDocumentUpload(event);
  };

  // Handler for input changes in the reference modal
  const handleReferenceInputChange = (e) => {
    const { name, value } = e.target;
    setNewReference(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const { t } = useTranslation();

  return (
    <div>
      <Tabs defaultValue="business-documents" className="w-full" onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-2 mb-4">
          <TabsTrigger value="business-documents" className="text-center">
            <FileText className="h-4 w-4 mr-2" />
            {t('admin.incidents.attachments.businessDocuments', 'Business Documents')}
          </TabsTrigger>
          <TabsTrigger value="external-references" className="text-center">
            <ExternalLink className="h-4 w-4 mr-2" />
            {t('admin.incidents.attachments.externalReferences', 'External References')}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="business-documents" className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-md font-medium">{t('admin.incidents.attachments.businessDocuments', 'Business Documents')}</h3>
            <div>
              <input
                type="file"
                id="business-document-upload"
                multiple
                className="hidden"
                onChange={handleBusinessDocumentUpload}
                disabled={isLoading}
                accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.csv,.rtf,.odt,.ods,.odp,.jpg,.jpeg,.png,.gif,.bmp,.svg,.zip,.rar,.7z,.tar,.gz"
                ref={businessDocInputRef}
              />
              <label htmlFor="business-document-upload">
                <Button
                  type="button"
                  variant="outline"
                  className="flex items-center gap-2 cursor-pointer"
                  disabled={isLoading}
                  asChild
                >
                  <span>
                    {isLoading ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin" />
                        {t('admin.incidents.attachments.uploading', 'Uploading...')} {uploadProgress > 0 ? `${uploadProgress}%` : ''}
                      </>
                    ) : (
                      <>
                        <Upload className="h-4 w-4" />
                        {t('admin.incidents.attachments.uploadDocument', 'Upload Document')}
                      </>
                    )}
                  </span>
                </Button>
              </label>
            </div>
          </div>

          {businessDocuments.length === 0 ? (
            <div
              className={`text-center py-8 border-2 border-dashed rounded-lg transition-colors ${isDraggingBusiness ? 'border-blue-500 bg-blue-50' : 'border-gray-300'}`}
              onDragOver={handleBusinessDragOver}
              onDragEnter={handleBusinessDragEnter}
              onDragLeave={handleBusinessDragLeave}
              onDrop={handleBusinessDrop}
            >
              <File className="h-12 w-12 mx-auto text-gray-300 mb-2" />
              <p className="text-gray-500">{isDraggingBusiness ? t('admin.incidents.attachments.dropFilesHere', 'Drop files here') : t('admin.incidents.attachments.noBusinessDocumentsUploadedYet', 'No business documents uploaded yet')}</p>
              <p className="text-sm text-gray-400">{t('admin.incidents.attachments.dragDropFiles', 'Drag & drop files here or click the upload button')}</p>
              <p className="text-xs text-gray-400 mt-2">{t('admin.incidents.attachments.maxFileSize', 'Max file size: 50MB. Allowed file types: PDF, Office documents, images, archives.')}</p>
            </div>
          ) : (
            <div className="border rounded-lg overflow-hidden">
              <table className="w-full relative">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">{t('admin.incidents.attachments.name', 'Name')}</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">{t('admin.incidents.attachments.size', 'Size')}</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">{t('admin.incidents.attachments.date', 'Date')}</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">{t('admin.incidents.attachments.actions', 'Actions')}</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {businessDocuments.map((doc) => (
                    <tr key={doc.id} className="hover:bg-gray-50">
                      <td className="px-4 py-3 text-sm">
                        <div className="flex items-center">
                          <FileText className="h-4 w-4 mr-2 text-blue-500" />
                          {doc.name}
                        </div>
                      </td>
                      <td className="px-4 py-3 text-sm">{formatFileSize(doc.size)}</td>
                      <td className="px-4 py-3 text-sm">{formatDate(doc.uploadDate)}</td>
                      <td className="px-4 py-3 text-sm">
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0"
                            onClick={() => downloadAttachment(doc.id, doc.name)}
                            title={t('admin.incidents.attachments.download', 'Download')}
                          >
                            <Download className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 text-red-500 hover:text-red-700 hover:bg-red-50"
                            onClick={() => deleteBusinessDocument(doc.id)}
                            title={t('admin.incidents.attachments.delete', 'Delete')}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </TabsContent>

        <TabsContent value="external-references" className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-md font-medium">{t('admin.incidents.attachments.externalReferences', 'External References')}</h3>
            <div>
                <Button
                  type="button"
                  variant="outline"
                className="flex items-center gap-2"
                  disabled={isLoading}
                onClick={() => setIsReferenceModalOpen(true)}
              >
                <Plus className="h-4 w-4" />
                {t('admin.incidents.attachments.addReference', 'Add Reference')}
                </Button>
            </div>
          </div>

          {externalReferences.length === 0 ? (
            <div className="text-center py-8 border-2 border-dashed rounded-lg border-gray-300">
              <ExternalLink className="h-12 w-12 mx-auto text-gray-300 mb-2" />
              <p className="text-gray-500">{t('admin.incidents.attachments.noExternalReferencesAddedYet', 'No external references added yet')}</p>
              <p className="text-sm text-gray-400">{t('admin.incidents.attachments.clickAddReference', 'Click the \'Add Reference\' button to add a link')}</p>
            </div>
          ) : (
            <div className="border rounded-lg overflow-hidden">
              <table className="w-full relative">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">{t('admin.incidents.attachments.name', 'Name')}</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">{t('admin.incidents.attachments.description', 'Description')}</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">{t('admin.incidents.attachments.date', 'Date')}</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">{t('admin.incidents.attachments.actions', 'Actions')}</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {externalReferences.map((ref) => (
                    <tr key={ref.id} className="hover:bg-gray-50">
                      <td className="px-4 py-3 text-sm">
                        <div className="flex items-center">
                          <Link className="h-4 w-4 mr-2 text-green-500" />
                          <span className="text-blue-600 hover:underline cursor-pointer" onClick={() => openExternalReference(ref.url)}>
                            {ref.name || new URL(ref.url).hostname}
                          </span>
                        </div>
                      </td>
                      <td className="px-4 py-3 text-sm">{ref.description}</td>
                      <td className="px-4 py-3 text-sm">{formatDate(ref.createdAt)}</td>
                      <td className="px-4 py-3 text-sm">
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0"
                            onClick={() => openExternalReference(ref.url)}
                            title={t('admin.incidents.attachments.openLink', 'Open Link')}
                          >
                            <ExternalLink className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 text-red-500 hover:text-red-700 hover:bg-red-50"
                            onClick={() => deleteExternalReference(ref.id)}
                            title={t('admin.incidents.attachments.delete', 'Delete')}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Modal for adding a new external reference */}
      <Dialog open={isReferenceModalOpen} onOpenChange={setIsReferenceModalOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>{t('admin.incidents.attachments.addExternalReference', 'Add External Reference')}</DialogTitle>
            <DialogDescription>
              {t('admin.incidents.attachments.addLink', 'Add a link to an external resource related to this incident.')}
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="url">{t('admin.incidents.attachments.url', 'URL')} <span className="text-red-500">*</span></Label>
              <Input
                id="url"
                name="url"
                placeholder="https://www.example.com"
                value={newReference.url}
                onChange={handleReferenceInputChange}
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="description">{t('admin.incidents.attachments.description', 'Description')}</Label>
              <Textarea
                id="description"
                name="description"
                placeholder={t('admin.incidents.attachments.brieflyDescribe', 'Briefly describe this reference')}
                value={newReference.description}
                onChange={handleReferenceInputChange}
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setIsReferenceModalOpen(false)}
              type="button"
            >
              {t('admin.incidents.attachments.cancel', 'Cancel')}
            </Button>
            <Button 
              onClick={handleAddExternalReference}
              disabled={isLoading}
              type="button"
            >
              {isLoading ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : null}
              {t('admin.incidents.attachments.addReference', 'Add Reference')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
