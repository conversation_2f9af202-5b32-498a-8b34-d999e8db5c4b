import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
} from "../ui/dialog";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { Textarea } from "../ui/textarea";
import { useState } from "react";
import { useTranslation } from "react-i18next";

export function NewControlModal({ open, onClose, onSubmit }) {
  const { t } = useTranslation();
  const [newControl, setNewControl] = useState({
    name: "",
    code: "",
    comment: ""
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    e.stopPropagation();

    // Validate the form
    if (!newControl.name) {
      alert('Control name is required');
      return;
    }

    // Call the onSubmit callback
    onSubmit(newControl);

    // Reset the form
    setNewControl({
      name: "",
      code: "",
      comment: ""
    });
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>{t('admin.incidents.new_control.title', 'Add New Control')}</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">{t('admin.incidents.new_control.name', 'Name')}</label>
            <Input
              value={newControl.name}
              onChange={(e) => setNewControl({ ...newControl, name: e.target.value })}
              required
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">{t('admin.incidents.new_control.code', 'Code')}</label>
            <Input
              value={newControl.code}
              onChange={(e) => setNewControl({ ...newControl, code: e.target.value })}
              placeholder={t('admin.incidents.new_control.code_placeholder', 'Control code (optional)')}
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">{t('admin.incidents.new_control.comment', 'Comment')}</label>
            <Textarea
              value={newControl.comment}
              onChange={(e) => setNewControl({ ...newControl, comment: e.target.value })}
              placeholder={t('admin.incidents.new_control.comment_placeholder', 'Add any additional comments about this control')}
              className="min-h-[100px]"
            />
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              {t('admin.incidents.new_control.cancel', 'Cancel')}
            </Button>
            <Button type="submit" className="bg-[#F62D51] hover:bg-red-700">
              {t('admin.incidents.new_control.add_control', 'Add Control')}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
