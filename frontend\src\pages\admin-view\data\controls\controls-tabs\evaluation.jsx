import { useState, useEffect } from "react";
import { useOutletContext } from "react-router-dom";
import { useDispatch } from "react-redux";
import { updateControl } from "@/store/slices/controlSlice";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";
import { Loader2, Save, Clipboard<PERSON>heck, CheckCircle2, XCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

function ControlEvaluation() {
  const { control, refreshControl, permissions } = useOutletContext();
  const { canUpdate } = permissions;
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // State for evaluation data
  const [evaluation, setEvaluation] = useState({
    designQuality: null,
    effectivenessLevel: null
  });

  // Load initial data from control
  useEffect(() => {
    if (control) {
      setEvaluation({
        designQuality: control.designQuality || null,
        effectivenessLevel: control.effectivenessLevel || null
      });
    }
  }, [control]);

  // Handle design quality change
  const handleDesignQualityChange = (value) => {
    // If design quality is "Insatisfaisant", automatically set effectiveness to "Insatisfaisant"
    if (value === "Insatisfaisant") {
      setEvaluation({
        designQuality: value,
        effectivenessLevel: "Insatisfaisant"
      });
    } else {
      setEvaluation({
        ...evaluation,
        designQuality: value
      });
    }
  };

  // Handle effectiveness level change
  const handleEffectivenessLevelChange = (value) => {
    setEvaluation({
      ...evaluation,
      effectivenessLevel: value
    });
  };

  // Save evaluation
  const handleSaveEvaluation = async () => {
    if (!canUpdate) {
      toast.error(t('admin.controls.evaluation.permission_error', "You don't have permission to update this control"));
      return;
    }

    setIsSubmitting(true);

    try {
      const result = await dispatch(updateControl({
        id: control.controlID,
        controlData: {
          designQuality: evaluation.designQuality,
          effectivenessLevel: evaluation.effectivenessLevel
        }
      })).unwrap();

      if (result.success) {
        toast.success(t('admin.controls.evaluation.success.updated', 'Control evaluation updated successfully'));
        refreshControl();
      } else {
        toast.error(t('admin.controls.evaluation.error.update', 'Failed to update control evaluation'));
      }
    } catch (error) {
      console.error('Error updating control evaluation:', error);
      toast.error(t('admin.controls.evaluation.error.update', 'Failed to update control evaluation'));
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-[#1A202C] flex items-center">
          <ClipboardCheck className="h-6 w-6 mr-3 text-[#F62D51]" />
          {t('admin.controls.evaluation.title', 'Control Evaluation')}
        </h2>
      </div>

      <div className="border rounded-lg shadow-sm">
        <div className="w-full flex items-center p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-lg">
          <div className="flex items-center gap-2">
            <ClipboardCheck className="h-5 w-5 text-blue-600 mr-1" />
            <span className="text-lg font-medium text-blue-800">
              {t('admin.controls.evaluation.assessment', 'Control Assessment')}
            </span>
          </div>
        </div>

        <div className="p-6 bg-white space-y-8">
          {/* Design Quality Question */}
          <div className="space-y-4">
            <Label className="text-base font-medium">
              {t('admin.controls.evaluation.design_quality.question', 'What is the quality level of control design?')}
            </Label>
            <RadioGroup
              value={evaluation.designQuality}
              onValueChange={handleDesignQualityChange}
              className="flex flex-col space-y-3"
              disabled={!canUpdate}
            >
              <div className="flex items-center space-x-2 p-2 rounded-md hover:bg-gray-50">
                <RadioGroupItem value="Satisfaisant" id="design-satisfactory" />
                <Label htmlFor="design-satisfactory" className="flex items-center cursor-pointer">
                  {t('admin.controls.evaluation.design_quality.satisfactory', 'Satisfactory')}
                  <CheckCircle2 className="h-5 w-5 ml-2 text-green-500" />
                </Label>
              </div>
              <div className="flex items-center space-x-2 p-2 rounded-md hover:bg-gray-50">
                <RadioGroupItem value="Insatisfaisant" id="design-unsatisfactory" />
                <Label htmlFor="design-unsatisfactory" className="flex items-center cursor-pointer">
                  {t('admin.controls.evaluation.design_quality.unsatisfactory', 'Unsatisfactory')}
                  <XCircle className="h-5 w-5 ml-2 text-red-500" />
                </Label>
              </div>
            </RadioGroup>
          </div>

          {/* Effectiveness Level Question */}
          <div className="space-y-4">
            <Label className="text-base font-medium">
              {t('admin.controls.evaluation.effectiveness.question', 'What is the effectiveness level of the control?')}
            </Label>
            <RadioGroup
              value={evaluation.effectivenessLevel}
              onValueChange={handleEffectivenessLevelChange}
              className="flex flex-col space-y-3"
              disabled={!canUpdate || evaluation.designQuality === "Insatisfaisant"}
            >
              <div className="flex items-center space-x-2 p-2 rounded-md hover:bg-gray-50">
                <RadioGroupItem
                  value="Satisfaisant"
                  id="effectiveness-satisfactory"
                  disabled={!canUpdate || evaluation.designQuality === "Insatisfaisant"}
                />
                <Label
                  htmlFor="effectiveness-satisfactory"
                  className={`flex items-center cursor-pointer ${evaluation.designQuality === "Insatisfaisant" ? "text-gray-400" : ""}`}
                >
                  {t('admin.controls.evaluation.effectiveness.satisfactory', 'Satisfactory')}
                  <CheckCircle2 className={`h-5 w-5 ml-2 ${evaluation.designQuality === "Insatisfaisant" ? "text-gray-400" : "text-green-500"}`} />
                </Label>
              </div>
              <div className="flex items-center space-x-2 p-2 rounded-md hover:bg-gray-50">
                <RadioGroupItem
                  value="Insatisfaisant"
                  id="effectiveness-unsatisfactory"
                  disabled={!canUpdate || evaluation.designQuality === "Insatisfaisant"}
                />
                <Label
                  htmlFor="effectiveness-unsatisfactory"
                  className={`flex items-center cursor-pointer ${evaluation.designQuality === "Insatisfaisant" ? "text-gray-400" : ""}`}
                >
                  {t('admin.controls.evaluation.effectiveness.unsatisfactory', 'Unsatisfactory')}
                  <XCircle className={`h-5 w-5 ml-2 ${evaluation.designQuality === "Insatisfaisant" ? "text-gray-400" : "text-red-500"}`} />
                </Label>
              </div>
            </RadioGroup>

          </div>

          {/* Save Button */}
          {canUpdate && (
            <div className="flex justify-end mt-6">
              <Button
                onClick={handleSaveEvaluation}
                className="bg-[#F62D51] hover:bg-red-700 text-white"
                disabled={isSubmitting || !evaluation.designQuality}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {t('common.saving', 'Saving...')}
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    {t('common.buttons.save', 'Save')}
                  </>
                )}
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default ControlEvaluation;
