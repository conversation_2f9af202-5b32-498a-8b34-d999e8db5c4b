const express = require('express');
const router = express.Router();
const { verifyToken, authorizeRoles } = require('../../middleware/auth');
const {
  getAllAuditConstats,
  getConstatsByActivityId,
  createAuditConstat,
  getAuditConstatById,
  updateAuditConstat,
  deleteAuditConstat
} = require('../../controllers/audit/audit-constat-controller');

// Apply authentication middleware to all routes
router.use(verifyToken);

// Get all audit constats
router.get('/', authorizeRoles(['audit_director', 'auditor']), getAllAuditConstats);

// Get audit constats by activity ID
router.get('/activity/:activityId', authorizeRoles(['audit_director', 'auditor']), getConstatsByActivityId);

// Create new audit constat
router.post('/', authorizeRoles(['audit_director', 'auditor']), createAuditConstat);

// Get audit constat by ID
router.get('/:id', authorizeRoles(['audit_director', 'auditor']), getAuditConstatById);

// Update audit constat
router.put('/:id', authorizeRoles(['audit_director', 'auditor']), updateAuditConstat);

// Delete audit constat
router.delete('/:id', authorizeRoles(['audit_director', 'auditor']), deleteAuditConstat);

module.exports = router;