import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger, DialogFooter } from "@/components/ui/dialog";
import { Card, CardContent } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Save,
  ChevronDown,
  ChevronUp,
  Map,
  List,
  Plus,
  Edit,
  Trash2,
  PencilRuler,
  Search,
  Calendar,
  Link,
  AlertTriangle,
  ShieldCheck,
  Zap,
  ExternalLink,
  Building2,
  Briefcase,
  GitBranch,
  Loader2
} from "lucide-react";
import { toast } from "sonner";
import { useMissionAuditContext } from '@/utils/context-helpers';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import axios from 'axios';
import { getApiBaseUrl } from '@/utils/api-config';
import { useNavigate } from 'react-router-dom';
import { getAuditScopeByMissionId, updateAuditScopeRelationships } from '@/services/audit-scope-service';

function PerimetreProgrammeTab(props) {
  const contextData = useMissionAuditContext();
  const missionAudit = props.missionAudit || contextData.missionAudit;
  const API_BASE_URL = getApiBaseUrl();
  const navigate = useNavigate();

  const navigateToActivite = (activiteId) => {
    if (missionAudit && missionAudit.planId) {
      navigate(`/audit/plans-daudit/${missionAudit.planId}/missions-audits/${missionAudit.id}/activites/${activiteId}`);
    } else if (missionAudit) {
      navigate(`/audit/missions-audits/${missionAudit.id}/activites/${activiteId}`);
    }
  };

  const [perimetreItems, setPerimetreItems] = useState([]);
  const [programmeItems, setProgrammeItems] = useState([]);
  const [isLoadingActivities, setIsLoadingActivities] = useState(false);
  const [isLoadingPerimetre, setIsLoadingPerimetre] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [unlinkingItemId, setUnlinkingItemId] = useState(null);

  const [isPerimetreOpen, setIsPerimetreOpen] = useState(true);
  const [isProgrammeOpen, setIsProgrammeOpen] = useState(true);
  const [isPerimetreDialogOpen, setIsPerimetreDialogOpen] = useState(false);
  const [isProgrammeDialogOpen, setIsProgrammeDialogOpen] = useState(false);
  const [isLinkDialogOpen, setIsLinkDialogOpen] = useState(false);
  const [editingPerimetreItem, setEditingPerimetreItem] = useState(null);
  const [editingProgrammeItem, setEditingProgrammeItem] = useState(null);
  const [newPerimetreItem, setNewPerimetreItem] = useState({ nom: "", type: "Processus" });
  const [newProgrammeItem, setNewProgrammeItem] = useState({
    nom: "",
    statut: "Planifié",
    dateDebut: "",
    dateFin: "",
    activitesCount: 0
  });
  const [entities, setEntities] = useState([]);
  const [businessProcesses, setBusinessProcesses] = useState([]);
  const [organizationalProcesses, setOrganizationalProcesses] = useState([]);
  const [risks, setRisks] = useState([]);
  const [controls, setControls] = useState([]);
  const [selectedItems, setSelectedItems] = useState({
    entities: [],
    risks: [],
    businessProcesses: [],
    organizationalProcesses: [],
    controls: []
  });
  const [isLoadingLinkData, setIsLoadingLinkData] = useState(false);
  const [linkSearch, setLinkSearch] = useState("");
  const [activeTab, setActiveTab] = useState("entities");
  const [perimetreSearch, setPerimetreSearch] = useState("");
  const [programmeSearch, setProgrammeSearch] = useState("");

  // Add new state for delete confirmation
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [activityToDelete, setActivityToDelete] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (isLinkDialogOpen && perimetreItems) {
      const initialSelected = {
        entities: perimetreItems.filter(item => item.referenceType === "entity").map(item => String(item.referenceId)),
        risks: perimetreItems.filter(item => item.referenceType === "risk").map(item => String(item.referenceId)),
        businessProcesses: perimetreItems.filter(item => item.referenceType === "businessProcess").map(item => String(item.referenceId)),
        organizationalProcesses: perimetreItems.filter(item => item.referenceType === "organizationalProcess").map(item => String(item.referenceId)),
        controls: perimetreItems.filter(item => item.referenceType === "control").map(item => String(item.referenceId)),
      };
      setSelectedItems(initialSelected);
      fetchLinkData();
    }
  }, [isLinkDialogOpen, perimetreItems]);

  useEffect(() => {
    if (missionAudit?.id) {
      fetchAuditActivities();
      fetchAuditScope();
    }
  }, [missionAudit?.id]);

  const fetchLinkData = async () => {
    setIsLoadingLinkData(true);
    try {
      const [entitiesResponse, risksResponse, businessProcessesResponse, organizationalProcessesResponse, controlsResponse] = await Promise.all([
        axios.get(`${API_BASE_URL}/entities`, { withCredentials: true }).catch(() => ({ data: { success: true, data: [{ entityID: 1, name: "Entité 1" }, { entityID: 2, name: "Entité 2" }] } })),
        axios.get(`${API_BASE_URL}/risk`, { withCredentials: true }).catch(() => ({ data: { success: true, data: [{ riskID: 1, name: "Risque 1" }, { riskID: 2, name: "Risque 2" }] } })),
        axios.get(`${API_BASE_URL}/businessprocesses`, { withCredentials: true }).catch(() => ({ data: { success: true, data: [{ businessProcessID: 1, name: "Processus métier 1" }, { businessProcessID: 2, name: "Processus métier 2" }] } })),
        axios.get(`${API_BASE_URL}/organizationalprocesses`, { withCredentials: true }).catch(() => ({ data: { success: true, data: [{ organizationalProcessID: 1, name: "Processus org 1" }, { organizationalProcessID: 2, name: "Processus org 2" }] } })),
        axios.get(`${API_BASE_URL}/controls`, { withCredentials: true }).catch(() => ({ data: { success: true, data: [{ controlID: 1, name: "Contrôle 1" }, { controlID: 2, name: "Contrôle 2" }] } }))
      ]);

      console.log("Raw Entities Response:", entitiesResponse.data);
      console.log("Raw Risks Response:", risksResponse.data);
      console.log("Raw Business Processes Response:", businessProcessesResponse.data);
      console.log("Raw Organizational Processes Response:", organizationalProcessesResponse.data);
      console.log("Raw Controls Response:", controlsResponse.data);

      const uniqueEntities = removeDuplicates(entitiesResponse.data.data || [], 'entityID');
      const uniqueRisks = removeDuplicates(risksResponse.data.data || [], 'riskID');
      const uniqueBusinessProcesses = removeDuplicates(businessProcessesResponse.data.data || [], 'businessProcessID');
      const uniqueOrganizationalProcesses = removeDuplicates(organizationalProcessesResponse.data.data || [], 'organizationalProcessID');
      const uniqueControls = removeDuplicates(controlsResponse.data.data || [], 'controlID');

      console.log("Processed Entities:", uniqueEntities);
      console.log("Processed Risks:", uniqueRisks);
      console.log("Processed Business Processes:", uniqueBusinessProcesses);
      console.log("Processed Organizational Processes:", uniqueOrganizationalProcesses);
      console.log("Processed Controls:", uniqueControls);

      if (entitiesResponse.data.success) setEntities(uniqueEntities);
      if (risksResponse.data.success) setRisks(uniqueRisks);
      if (businessProcessesResponse.data.success) setBusinessProcesses(uniqueBusinessProcesses);
      if (organizationalProcessesResponse.data.success) setOrganizationalProcesses(uniqueOrganizationalProcesses);
      if (controlsResponse.data.success) setControls(uniqueControls);
    } catch (error) {
      console.error("Error fetching link data:", error);
      toast.error("Erreur lors du chargement des données de référence");
    } finally {
      setIsLoadingLinkData(false);
    }
  };

  const fetchAuditActivities = async () => {
    if (!missionAudit?.id) return;
    
    setIsLoadingActivities(true);
    try {
      const response = await axios.get(`${API_BASE_URL}/audit-activities/mission/${missionAudit.id}`, { withCredentials: true });
      if (response.data.success) {
        const activities = response.data.data.map(activity => ({
          id: activity.id,
          nom: activity.name,
          statut: activity.status || 'Planifié',
          dateDebut: activity.datedebut,
          dateFin: activity.datefin,
          activitesCount: 0, // This will be updated when we implement constats
          responsable: activity.responsable,
          chargedetravailestimee: activity.chargedetravailestimee,
          chargedetravaileffective: activity.chargedetravaileffective,
          objectif: activity.objectif,
          depense: activity.depense
        }));
        setProgrammeItems(activities);
      }
    } catch (error) {
      console.error('Error fetching audit activities:', error);
      toast.error('Erreur lors du chargement des activités');
    } finally {
      setIsLoadingActivities(false);
    }
  };

  const fetchAuditScope = async () => {
    if (!missionAudit?.id) return;
    
    setIsLoadingPerimetre(true);
    try {
      const response = await getAuditScopeByMissionId(missionAudit.id);
      if (response.success && response.data && response.data.length > 0) {
        // Get the first (and should be only) audit scope for this mission
        const auditScope = response.data[0];
        
        // Transform the scope data into the format expected by the component
        const scopeItems = [];
        
        // Add entities from many-to-many relationship
        if (auditScope.entities && Array.isArray(auditScope.entities)) {
          auditScope.entities.forEach(entity => {
            scopeItems.push({
              id: `entity-${entity.entityID}`,
              nom: entity.name,
              type: "Entité",
              referenceId: entity.entityID,
              referenceType: "entity"
            });
          });
        }

        // Add risks from many-to-many relationship
        if (auditScope.risks && Array.isArray(auditScope.risks)) {
          auditScope.risks.forEach(risk => {
            scopeItems.push({
              id: `risk-${risk.riskID}`,
              nom: risk.name,
              type: "Risque",
              referenceId: risk.riskID,
              referenceType: "risk"
            });
          });
        }

        // Add organizational processes from many-to-many relationship
        if (auditScope.processes && Array.isArray(auditScope.processes)) {
          auditScope.processes.forEach(process => {
            scopeItems.push({
              id: `organizationalProcess-${process.organizationalProcessID}`,
              nom: process.name,
              type: "Processus organisationnel",
              referenceId: process.organizationalProcessID,
              referenceType: "organizationalProcess"
            });
          });
        }

        // Add controls from many-to-many relationship
        if (auditScope.controls && Array.isArray(auditScope.controls)) {
          auditScope.controls.forEach(control => {
            scopeItems.push({
              id: `control-${control.controlID}`,
              nom: control.name,
              type: "Contrôle",
              referenceId: control.controlID,
              referenceType: "control"
            });
          });
        }

        // Add business processes from many-to-many relationship
        if (auditScope.businessProcesses && Array.isArray(auditScope.businessProcesses)) {
          auditScope.businessProcesses.forEach(process => {
            scopeItems.push({
              id: `businessProcess-${process.businessProcessID}`,
              nom: process.name,
              type: "Processus métier",
              referenceId: process.businessProcessID,
              referenceType: "businessProcess"
            });
          });
        }

        setPerimetreItems(scopeItems);
        console.log('Fetched audit scope items:', scopeItems);
      } else {
        // No scope found, start with empty array
        setPerimetreItems([]);
        console.log('No audit scope found for mission:', missionAudit.id);
      }
    } catch (error) {
      console.error('Error fetching audit scope:', error);
      toast.error('Erreur lors du chargement du périmètre');
      setPerimetreItems([]);
    } finally {
      setIsLoadingPerimetre(false);
    }
  };

  const removeDuplicates = (array, idKey) => {
    const seen = new Set();
    return array.map((item, index) => {
      const id = item[idKey] !== undefined ? String(item[idKey]) : `temp-${index}`;
      if (seen.has(id)) {
        console.warn(`Duplicate ${idKey} found: ${id}`);
        return null;
      }
      seen.add(id);
      return { ...item, [idKey]: id };
    }).filter(item => item !== null);
  };

  const filteredPerimetreItems = perimetreItems.filter(item =>
    item.nom.toLowerCase().includes(perimetreSearch.toLowerCase())
  );
  const filteredProgrammeItems = programmeItems.filter(item =>
    item.nom.toLowerCase().includes(programmeSearch.toLowerCase())
  );

  if (!missionAudit || !missionAudit.id) {
    return (
      <div className="flex items-center justify-center h-48">
        <p className="text-gray-500">Chargement du périmètre et du programme de travail...</p>
      </div>
    );
  }

  const handleSave = async () => {
    if (!missionAudit?.id) return;
    
    setIsSaving(true);
    try {
      // Transform perimetreItems into the format expected by the API
      const relationships = {
        entities: perimetreItems
          .filter(item => item.referenceType === 'entity')
          .map(item => item.referenceId),
        risks: perimetreItems
          .filter(item => item.referenceType === 'risk')
          .map(item => item.referenceId),
        organizationalProcesses: perimetreItems
          .filter(item => item.referenceType === 'organizationalProcess')
          .map(item => item.referenceId),
        controls: perimetreItems
          .filter(item => item.referenceType === 'control')
          .map(item => item.referenceId),
        businessProcesses: perimetreItems
          .filter(item => item.referenceType === 'businessProcess')
          .map(item => item.referenceId)
      };

      const response = await updateAuditScopeRelationships(missionAudit.id, relationships);
      if (response.success) {
        toast.success("Périmètre sauvegardé avec succès");
        // Refresh the scope data
        await fetchAuditScope();
      } else {
        throw new Error(response.message || 'Erreur lors de la sauvegarde');
      }
    } catch (error) {
      console.error('Error saving audit scope:', error);
      toast.error(error.message || 'Erreur lors de la sauvegarde du périmètre');
    } finally {
      setIsSaving(false);
    }
  };

  const handlePerimetreInputChange = (e) => {
    const { name, value } = e.target;
    setNewPerimetreItem(prev => ({ ...prev, [name]: value }));
  };

  const handlePerimetreSelectChange = (name, value) => {
    setNewPerimetreItem(prev => ({ ...prev, [name]: value }));
  };

  const handleAddPerimetreItem = () => {
    if (!newPerimetreItem.nom) {
      toast.error("Veuillez remplir le nom de l'élément");
      return;
    }
    if (editingPerimetreItem) {
      setPerimetreItems(prev => prev.map(item =>
        item.id === editingPerimetreItem.id ? { ...newPerimetreItem, id: item.id } : item
      ));
      toast.success("Élément du périmètre mis à jour avec succès");
    } else {
      setPerimetreItems(prev => [...prev, { ...newPerimetreItem, id: Date.now() }]);
      toast.success("Nouvel élément ajouté au périmètre");
    }
    setNewPerimetreItem({ nom: "", type: "Processus" });
    setEditingPerimetreItem(null);
    setIsPerimetreDialogOpen(false);
  };

  const handleEditPerimetreItem = (item) => {
    setEditingPerimetreItem(item);
    setNewPerimetreItem(item);
    setIsPerimetreDialogOpen(true);
  };

  const handleUnlinkPerimetreItem = async (item) => {
    if (!missionAudit?.id) return;
    
    setUnlinkingItemId(item.id);
    try {
      // Get current relationships
      const currentRelationships = {
        entities: perimetreItems
          .filter(i => i.referenceType === 'entity' && i.id !== item.id)
          .map(i => i.referenceId),
        risks: perimetreItems
          .filter(i => i.referenceType === 'risk' && i.id !== item.id)
          .map(i => i.referenceId),
        organizationalProcesses: perimetreItems
          .filter(i => i.referenceType === 'organizationalProcess' && i.id !== item.id)
          .map(i => i.referenceId),
        controls: perimetreItems
          .filter(i => i.referenceType === 'control' && i.id !== item.id)
          .map(i => i.referenceId),
        businessProcesses: perimetreItems
          .filter(i => i.referenceType === 'businessProcess' && i.id !== item.id)
          .map(i => i.referenceId)
      };

      // Update relationships in backend
      const response = await updateAuditScopeRelationships(missionAudit.id, currentRelationships);
      if (response.success) {
        // Update local state
        setPerimetreItems(prev => prev.filter(i => i.id !== item.id));
        toast.success("Élément délié avec succès");
      } else {
        throw new Error(response.message || 'Erreur lors du déliage');
      }
    } catch (error) {
      console.error('Error unlinking item:', error);
      toast.error(error.message || 'Erreur lors du déliage de l\'élément');
    } finally {
      setUnlinkingItemId(null);
    }
  };

  const handleProgrammeInputChange = (e) => {
    const { name, value } = e.target;
      setNewProgrammeItem(prev => ({ ...prev, [name]: value }));
  };

  const handleProgrammeSelectChange = (name, value) => {
    setNewProgrammeItem(prev => ({ ...prev, [name]: value }));
  };

  const handleAddProgrammeItem = async () => {
    // Prevent multiple submissions
    if (isSubmitting) {
      return;
    }

    if (!newProgrammeItem.nom || !newProgrammeItem.dateDebut || !newProgrammeItem.dateFin) {
      toast.error("Veuillez remplir au moins le nom, la date de début et la date de fin");
      return;
    }

    setIsSubmitting(true);
    try {
      const activityData = {
        name: newProgrammeItem.nom,
        status: newProgrammeItem.statut,
        datedebut: formatDateForInput(newProgrammeItem.dateDebut),
        datefin: formatDateForInput(newProgrammeItem.dateFin),
        auditMissionID: missionAudit.id,
        responsable: newProgrammeItem.responsable || null,
        chargedetravailestimee: newProgrammeItem.chargedetravailestimee || null,
        chargedetravaileffective: newProgrammeItem.chargedetravaileffective || null,
        objectif: newProgrammeItem.objectif || null,
        depense: newProgrammeItem.depense || null
      };

      let response;
    if (editingProgrammeItem) {
        response = await axios.put(
          `${API_BASE_URL}/audit-activities/${editingProgrammeItem.id}`,
          activityData,
          { withCredentials: true }
        );
    } else {
        response = await axios.post(
          `${API_BASE_URL}/audit-activities`,
          activityData,
          { withCredentials: true }
        );
      }

      if (response.data.success) {
        toast.success(editingProgrammeItem ? "Activité mise à jour avec succès" : "Nouvelle activité ajoutée avec succès");
        await fetchAuditActivities();
        setIsProgrammeDialogOpen(false);
    setNewProgrammeItem({ nom: "", statut: "Planifié", dateDebut: "", dateFin: "", activitesCount: 0 });
    setEditingProgrammeItem(null);
      }
    } catch (error) {
      console.error('Error saving audit activity:', error);
      toast.error('Erreur lors de la sauvegarde de l\'activité');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditProgrammeItem = (item) => {
    setEditingProgrammeItem(item);
    setNewProgrammeItem({ ...item });
    setIsProgrammeDialogOpen(true);
  };

  const handleDeleteProgrammeItem = (id) => {
    setActivityToDelete(id);
    setIsDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (!activityToDelete) return;
    
    try {
      const response = await axios.delete(`${API_BASE_URL}/audit-activities/${activityToDelete}`, { withCredentials: true });
      if (response.data.success) {
        toast.success("Activité supprimée avec succès");
        fetchAuditActivities();
      }
    } catch (error) {
      console.error('Error deleting audit activity:', error);
      toast.error('Erreur lors de la suppression de l\'activité');
    } finally {
      setIsDeleteDialogOpen(false);
      setActivityToDelete(null);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return "-";
    // Ensure we're working with just the date part
    const date = new Date(dateString.split('T')[0]);
    return date.toLocaleDateString('fr-FR', { day: '2-digit', month: '2-digit', year: 'numeric' });
  };

  const formatDateForDisplay = (dateString) => {
    if (!dateString) return "";
    // Ensure we're working with just the date part
    const [year, month, day] = dateString.split('T')[0].split('-');
    return `${day}/${month}/${year}`;
  };

  const formatDateForInput = (dateString) => {
    if (!dateString) return "";
    // Ensure we're working with just the date part
    return dateString.split('T')[0];
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case 'Terminé': return <Badge className="bg-green-100 text-green-800">Terminé</Badge>;
      case 'En cours': return <Badge className="bg-blue-100 text-blue-800">En cours</Badge>;
      case 'Planifié': return <Badge className="bg-amber-100 text-amber-800">Planifié</Badge>;
      default: return <Badge variant="outline">-</Badge>;
    }
  };

  const getTypeBadge = (type) => {
    switch (type) {
      case 'Processus': return <Badge className="bg-blue-100 text-blue-800">Processus</Badge>;
      case 'Système': return <Badge className="bg-green-100 text-green-800">Système</Badge>;
      case 'Organisation': return <Badge className="bg-purple-100 text-purple-800">Organisation</Badge>;
      case 'Risque': return <Badge className="bg-amber-100 text-amber-800">Risque</Badge>;
      case 'Contrôle': return <Badge className="bg-emerald-100 text-emerald-800">Contrôle</Badge>;
      case 'Processus métier': return <Badge className="bg-indigo-100 text-indigo-800">Processus métier</Badge>;
      case 'Processus organisationnel': return <Badge className="bg-violet-100 text-violet-800">Processus org.</Badge>;
      default: return <Badge variant="outline">{type}</Badge>;
    }
  };

  return (
    <div className="space-y-6 py-4">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-[#1A202C] flex items-center">
          <PencilRuler className="h-6 w-6 mr-3 text-[#F62D51]" />
          Périmètre et Programme de Travail
        </h2>
        <Button 
          onClick={handleSave} 
          className="bg-[#F62D51] hover:bg-[#F62D51]/90"
          disabled={isSaving}
        >
          {isSaving ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Sauvegarde...
            </>
          ) : (
            <>
          <Save className="h-4 w-4 mr-2" />
          Sauvegarder
            </>
          )}
        </Button>
      </div>

      <div className="border rounded-lg shadow-sm">
        <button
          type="button"
          className="w-full flex items-center p-4 bg-gradient-to-r from-purple-50 to-violet-50 rounded-t-lg"
          onClick={() => setIsPerimetreOpen(!isPerimetreOpen)}
        >
          <div className="flex items-center gap-2">
            {isPerimetreOpen ? <ChevronUp className="h-5 w-5 text-purple-600" /> : <ChevronDown className="h-5 w-5 text-purple-600" />}
            <Map className="h-5 w-5 text-purple-600 mr-1" />
            <span className="text-lg font-medium text-purple-800">Périmètre</span>
          </div>
        </button>
        {isPerimetreOpen && (
          <div className="p-5 bg-white">
            <div className="mb-4 flex justify-between items-center">
              <div className="relative w-64">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Rechercher dans le périmètre..."
                  className="pl-8"
                  value={perimetreSearch}
                  onChange={e => setPerimetreSearch(e.target.value)}
                />
              </div>
              <Button
                className="bg-[#F62D51] hover:bg-[#F62D51]/90"
                onClick={() => setIsLinkDialogOpen(true)}
              >
                <Link className="h-4 w-4 mr-2" />
                Relier
              </Button>
            </div>
            <Card className="overflow-hidden">
              <CardContent className="p-0">
                <div className="overflow-x-auto">
                  <Table className="min-w-full">
                    <TableHeader>
                      <TableRow className="bg-gray-50 hover:bg-gray-100/50">
                        <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nom</TableHead>
                        <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</TableHead>
                        <TableHead className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"></TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody className="divide-y divide-gray-200">
                      {isLoadingPerimetre ? (
                        <TableRow>
                          <TableCell colSpan={3} className="px-4 py-10 text-center">
                            <div className="flex items-center justify-center">
                              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#F62D51]"></div>
                            </div>
                          </TableCell>
                        </TableRow>
                      ) : filteredPerimetreItems.length > 0 ? (
                        filteredPerimetreItems.map(item => (
                          <TableRow key={item.id} className="hover:bg-gray-50/50">
                            <TableCell className="px-4 py-3 text-sm font-medium text-gray-900 whitespace-nowrap">{item.nom}</TableCell>
                            <TableCell className="px-4 py-3 text-sm whitespace-nowrap">{getTypeBadge(item.type)}</TableCell>
                            <TableCell className="px-4 py-3 text-sm whitespace-nowrap">
                              <div className="flex justify-end gap-2">
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  className="h-8 w-8 p-0"
                                  onClick={() => handleEditPerimetreItem(item)}
                                >
                                  <Edit className="h-4 w-4 text-blue-600" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  className="h-8 w-8 p-0"
                                  onClick={() => handleUnlinkPerimetreItem(item)}
                                  title="Délier l'élément"
                                  disabled={unlinkingItemId === item.id}
                                >
                                  {unlinkingItemId === item.id ? (
                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600"></div>
                                  ) : (
                                    <Link className="h-4 w-4 text-red-600 rotate-45" />
                                  )}
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={3} className="px-4 py-10 text-center text-sm text-gray-500">
                            Aucun élément dans le périmètre
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>

      <div className="border rounded-lg shadow-sm">
        <button
          type="button"
          className="w-full flex items-center p-4 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-t-lg"
          onClick={() => setIsProgrammeOpen(!isProgrammeOpen)}
        >
          <div className="flex items-center gap-2">
            {isProgrammeOpen ? <ChevronUp className="h-5 w-5 text-blue-600" /> : <ChevronDown className="h-5 w-5 text-blue-600" />}
            <List className="h-5 w-5 text-blue-600 mr-1" />
            <span className="text-lg font-medium text-blue-800">Programme de Travail</span>
          </div>
        </button>
        {isProgrammeOpen && (
          <div className="p-5 bg-white">
            <div className="mb-4 flex justify-between items-center">
              <div className="relative w-64">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Rechercher dans le programme..."
                  className="pl-8"
                  value={programmeSearch}
                  onChange={e => setProgrammeSearch(e.target.value)}
                />
              </div>
              <Button
                className="bg-[#F62D51] hover:bg-[#F62D51]/90"
                onClick={() => {
                  setEditingProgrammeItem(null);
                  setNewProgrammeItem({ nom: "", statut: "Planifié", dateDebut: "", dateFin: "", activitesCount: 0 });
                  setIsProgrammeDialogOpen(true);
                }}
              >
                <Plus className="h-4 w-4 mr-2" />
                Ajouter au Programme
              </Button>
            </div>
            <Card className="overflow-hidden">
              <CardContent className="p-0">
                <div className="overflow-x-auto">
                  <Table className="min-w-full">
                    <TableHeader>
                      <TableRow className="bg-gray-50 hover:bg-gray-100/50">
                        <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nom</TableHead>
                        <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</TableHead>
                        <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date de début</TableHead>
                        <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date de fin</TableHead>
                        <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Activités</TableHead>
                        <TableHead className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"></TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody className="divide-y divide-gray-200">
                      {isLoadingActivities ? (
                        <TableRow>
                          <TableCell colSpan={6} className="px-4 py-10 text-center">
                            <div className="flex items-center justify-center">
                              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#F62D51]"></div>
                            </div>
                          </TableCell>
                        </TableRow>
                      ) : filteredProgrammeItems.length > 0 ? (
                        filteredProgrammeItems.map(item => (
                          <TableRow
                            key={item.id}
                            className="hover:bg-gray-50/50 cursor-pointer"
                            onClick={() => navigateToActivite(item.id)}
                          >
                            <TableCell className="px-4 py-3 text-sm font-medium text-gray-900 whitespace-nowrap">
                              <div className="flex items-center">
                                {item.nom}
                                <ExternalLink className="h-3.5 w-3.5 ml-2 text-gray-400" />
                              </div>
                            </TableCell>
                            <TableCell className="px-4 py-3 text-sm whitespace-nowrap">{getStatusBadge(item.statut)}</TableCell>
                            <TableCell className="px-4 py-3 text-sm text-gray-500 whitespace-nowrap">{formatDate(item.dateDebut)}</TableCell>
                            <TableCell className="px-4 py-3 text-sm text-gray-500 whitespace-nowrap">{formatDate(item.dateFin)}</TableCell>
                            <TableCell className="px-4 py-3 text-sm text-gray-500 whitespace-nowrap">{item.activitesCount}</TableCell>
                            <TableCell className="px-4 py-3 text-sm whitespace-nowrap">
                              <div className="flex justify-end gap-2">
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  className="h-8 w-8 p-0"
                                  onClick={e => { e.stopPropagation(); handleEditProgrammeItem(item); }}
                                >
                                  <Edit className="h-4 w-4 text-blue-600" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  className="h-8 w-8 p-0"
                                  onClick={e => { e.stopPropagation(); handleDeleteProgrammeItem(item.id); }}
                                >
                                  <Trash2 className="h-4 w-4 text-red-600" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={6} className="px-4 py-10 text-center text-sm text-gray-500">
                            Aucune activité dans le programme
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>

      <Dialog open={isPerimetreDialogOpen} onOpenChange={setIsPerimetreDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
              <DialogHeader>
            <DialogTitle>{editingPerimetreItem ? "Modifier l'élément du périmètre" : "Ajouter au périmètre"}</DialogTitle>
                <DialogDescription>
              {editingPerimetreItem ? "Modifiez les informations de l'élément sélectionné" : "Ajoutez un nouvel élément au périmètre de l'audit"}
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
              <Label htmlFor="nom">Nom *</Label>
                  <Input
                id="nom"
                name="nom"
                value={newPerimetreItem.nom}
                onChange={handlePerimetreInputChange}
                placeholder="Nom de l'élément"
                  />
                </div>
                <div className="space-y-2">
              <Label htmlFor="type">Type</Label>
              <Select
                name="type"
                value={newPerimetreItem.type}
                onValueChange={value => handlePerimetreSelectChange("type", value)}
              >
                <SelectTrigger id="type" className="w-full">
                  <SelectValue placeholder="Sélectionner un type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Processus">Processus</SelectItem>
                  <SelectItem value="Système">Système</SelectItem>
                  <SelectItem value="Organisation">Organisation</SelectItem>
                </SelectContent>
              </Select>
                </div>
              </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
              setIsPerimetreDialogOpen(false);
              setEditingPerimetreItem(null);
                setNewPerimetreItem({ nom: "", type: "Processus" });
              }}
            >
                  Annuler
                </Button>
            <Button
              className="bg-[#F62D51] hover:bg-[#F62D51]/90"
              onClick={handleAddPerimetreItem}
            >
              {editingPerimetreItem ? "Mettre à jour" : "Ajouter"}
                </Button>
          </DialogFooter>
            </DialogContent>
          </Dialog>

      <Dialog open={isProgrammeDialogOpen} onOpenChange={(open) => {
        if (!open) {
          setIsSubmitting(false);
          setNewProgrammeItem({ nom: "", statut: "Planifié", dateDebut: "", dateFin: "", activitesCount: 0 });
          setEditingProgrammeItem(null);
        }
        setIsProgrammeDialogOpen(open);
      }}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>{editingProgrammeItem ? "Modifier l'élément du programme" : "Ajouter au programme de travail"}</DialogTitle>
            <DialogDescription>
              {editingProgrammeItem ? "Modifiez les informations de l'élément sélectionné" : "Ajoutez un nouvel élément au programme de travail"}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="nomProgramme">Nom *</Label>
              <Input
                id="nomProgramme"
                name="nom"
                value={newProgrammeItem.nom}
                onChange={handleProgrammeInputChange}
                placeholder="Nom de l'élément du programme"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="statut">Statut</Label>
              <Select
                name="statut"
                value={newProgrammeItem.statut}
                onValueChange={value => handleProgrammeSelectChange("statut", value)}
              >
                <SelectTrigger id="statut" className="w-full">
                  <SelectValue placeholder="Sélectionner un statut" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Planifié">Planifié</SelectItem>
                  <SelectItem value="En cours">En cours</SelectItem>
                  <SelectItem value="Terminé">Terminé</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="dateDebut">Date de début *</Label>
                <div className="relative">
                  <Input
                    id="dateDebut"
                    name="dateDebut"
                    type="date"
                    value={formatDateForInput(newProgrammeItem.dateDebut)}
                    onChange={handleProgrammeInputChange}
                    className="opacity-0 absolute inset-0 w-full h-full cursor-pointer z-10"
                    onClick={e => e.currentTarget.showPicker()}
                  />
                  <Input
                    placeholder="JJ/MM/AAAA"
                    value={newProgrammeItem.dateDebut ? formatDateForDisplay(newProgrammeItem.dateDebut) : ""}
                    readOnly
                    className="pl-9 pr-9 pointer-events-none"
                  />
                  <Calendar className="absolute left-2 top-2.5 h-4 w-4 text-gray-400 pointer-events-none" />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="dateFin">Date de fin *</Label>
                <div className="relative">
                  <Input
                    id="dateFin"
                    name="dateFin"
                    type="date"
                    value={formatDateForInput(newProgrammeItem.dateFin)}
                    onChange={handleProgrammeInputChange}
                    className="opacity-0 absolute inset-0 w-full h-full cursor-pointer z-10"
                    onClick={e => e.currentTarget.showPicker()}
                  />
                  <Input
                    placeholder="JJ/MM/AAAA"
                    value={newProgrammeItem.dateFin ? formatDateForDisplay(newProgrammeItem.dateFin) : ""}
                    readOnly
                    className="pl-9 pr-9 pointer-events-none"
                  />
                  <Calendar className="absolute left-2 top-2.5 h-4 w-4 text-gray-400 pointer-events-none" />
                </div>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
              setIsProgrammeDialogOpen(false);
              setEditingProgrammeItem(null);
                setNewProgrammeItem({ nom: "", statut: "Planifié", dateDebut: "", dateFin: "", activitesCount: 0 });
                setIsSubmitting(false);
              }}
              disabled={isSubmitting}
            >
              Annuler
            </Button>
            <Button
              className="bg-[#F62D51] hover:bg-[#F62D51]/90"
              onClick={handleAddProgrammeItem}
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  {editingProgrammeItem ? "Mise à jour..." : "Ajout en cours..."}
                </>
              ) : (
                editingProgrammeItem ? "Mettre à jour" : "Ajouter"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={isLinkDialogOpen} onOpenChange={setIsLinkDialogOpen}>
        <DialogContent className="sm:max-w-[1000px] max-h-[80vh] flex flex-col min-h-[500px]" aria-describedby="link-dialog-description">
          <DialogHeader>
            <DialogTitle>Sélectionnez et relier au périmètre de l'audit</DialogTitle>
            <DialogDescription id="link-dialog-description">
              Choisissez les éléments à associer au périmètre de votre mission d'audit.
            </DialogDescription>
          </DialogHeader>
          <div className="sticky top-0 z-10 bg-white pt-2 pb-2">
            <div className="flex border-b space-x-2">
              <button
                className={`px-2 py-2 font-medium flex-1 text-center whitespace-nowrap text-sm ${activeTab === 'entities' ? 'text-[#F62D51] border-b-2 border-[#F62D51]' : 'text-gray-500'}`}
                onClick={() => setActiveTab('entities')}
              >
                <div className="flex items-center justify-center"><Building2 className="h-4 w-4 mr-1" />Entités</div>
              </button>
              <button
                className={`px-2 py-2 font-medium flex-1 text-center whitespace-nowrap text-sm ${activeTab === 'risks' ? 'text-[#F62D51] border-b-2 border-[#F62D51]' : 'text-gray-500'}`}
                onClick={() => setActiveTab('risks')}
              >
                <div className="flex items-center justify-center"><AlertTriangle className="h-4 w-4 mr-1" />Risques</div>
              </button>
              <button
                className={`px-2 py-2 font-medium flex-1 text-center whitespace-nowrap text-sm ${activeTab === 'businessProcesses' ? 'text-[#F62D51] border-b-2 border-[#F62D51]' : 'text-gray-500'}`}
                onClick={() => setActiveTab('businessProcesses')}
              >
                <div className="flex items-center justify-center"><Briefcase className="h-4 w-4 mr-1" />Processus métier</div>
              </button>
              <button
                className={`px-2 py-2 font-medium flex-1 text-center whitespace-nowrap text-sm ${activeTab === 'organizationalProcesses' ? 'text-[#F62D51] border-b-2 border-[#F62D51]' : 'text-gray-500'}`}
                onClick={() => setActiveTab('organizationalProcesses')}
              >
                <div className="flex items-center justify-center"><GitBranch className="h-4 w-4 mr-1" />Processus org.</div>
              </button>
              <button
                className={`px-2 py-2 font-medium flex-1 text-center whitespace-nowrap text-sm ${activeTab === 'controls' ? 'text-[#F62D51] border-b-2 border-[#F62D51]' : 'text-gray-500'}`}
                onClick={() => setActiveTab('controls')}
              >
                <div className="flex items-center justify-center"><ShieldCheck className="h-4 w-4 mr-1" />Contrôles</div>
              </button>
            </div>
            <div className="relative w-full mt-2">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
              <Input placeholder="Rechercher..." className="pl-8" value={linkSearch} onChange={e => setLinkSearch(e.target.value)} />
            </div>
          </div>
          <div className="flex-1 min-h-0 overflow-y-auto py-4">
              {isLoadingLinkData ? (
              <div className="flex items-center justify-center h-full">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#F62D51]"></div>
                </div>
              ) : (
                <>
                {activeTab === "entities" && (
                  <div key="entities-list" className="space-y-2">
                    {entities.length === 0 ? (
                      <p className="text-gray-500 text-center py-8">Aucune entité trouvée</p>
                    ) : (
                      entities.filter(e => e.name.toLowerCase().includes(linkSearch.toLowerCase())).map((entity, index) => (
                        <div key={entity.entityID || `entity-${index}`} className="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded-md">
                          <Checkbox
                            id={`entity-${entity.entityID || index}`}
                            checked={selectedItems.entities.includes(String(entity.entityID))}
                            onCheckedChange={checked => {
                              setSelectedItems(prev => ({
                                ...prev,
                                entities: checked
                                  ? [...prev.entities, String(entity.entityID)]
                                  : prev.entities.filter(id => id !== String(entity.entityID))
                              }));
                            }}
                          />
                          <Label htmlFor={`entity-${entity.entityID || index}`} className="flex-1 cursor-pointer flex items-center">
                            <Building2 className="h-4 w-4 mr-2 text-blue-500" />
                            <span>{entity.name}</span>
                          </Label>
                        </div>
                      ))
                    )}
                  </div>
                )}
                {activeTab === "risks" && (
                  <div key="risks-list" className="space-y-2">
                      {risks.length === 0 ? (
                        <p className="text-gray-500 text-center py-8">Aucun risque trouvé</p>
                      ) : (
                      risks.filter(risk => risk.name.toLowerCase().includes(linkSearch.toLowerCase())).map((risk, index) => (
                        <div key={risk.riskID || `risk-${index}`} className="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded-md">
                              <Checkbox
                            id={`risk-${risk.riskID || index}`}
                            checked={selectedItems.risks.includes(String(risk.riskID))}
                            onCheckedChange={checked => {
                                  setSelectedItems(prev => ({
                                    ...prev,
                                    risks: checked
                                  ? [...prev.risks, String(risk.riskID)]
                                  : prev.risks.filter(id => id !== String(risk.riskID))
                                  }));
                                }}
                              />
                          <Label htmlFor={`risk-${risk.riskID || index}`} className="flex-1 cursor-pointer flex items-center">
                                <AlertTriangle className="h-4 w-4 mr-2 text-amber-500" />
                                <span>{risk.name}</span>
                                {risk.code && <span className="ml-2 text-xs text-gray-500">({risk.code})</span>}
                              </Label>
                            </div>
                          ))
                      )}
                    </div>
                  )}
                {activeTab === "businessProcesses" && (
                  <div key="business-processes-list" className="space-y-2">
                    {businessProcesses.length === 0 ? (
                      <p className="text-gray-500 text-center py-8">Aucun processus métier trouvé</p>
                    ) : (
                      businessProcesses.filter(p => p.name.toLowerCase().includes(linkSearch.toLowerCase())).map((process, index) => (
                        <div key={process.businessProcessID || `bp-${index}`} className="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded-md">
                              <Checkbox
                            id={`bp-${process.businessProcessID || index}`}
                            checked={selectedItems.businessProcesses.includes(String(process.businessProcessID))}
                            onCheckedChange={checked => {
                                  setSelectedItems(prev => ({
                                    ...prev,
                                businessProcesses: checked
                                  ? [...prev.businessProcesses, String(process.businessProcessID)]
                                  : prev.businessProcesses.filter(id => id !== String(process.businessProcessID))
                                  }));
                                }}
                              />
                          <Label htmlFor={`bp-${process.businessProcessID || index}`} className="flex-1 cursor-pointer flex items-center">
                            <Briefcase className="h-4 w-4 mr-2 text-green-500" />
                            <span>{process.name}</span>
                              </Label>
                            </div>
                          ))
                      )}
                    </div>
                  )}
                {activeTab === "organizationalProcesses" && (
                  <div key="organizational-processes-list" className="space-y-2">
                    {organizationalProcesses.length === 0 ? (
                      <p className="text-gray-500 text-center py-8">Aucun processus organisationnel trouvé</p>
                    ) : (
                      organizationalProcesses.filter(p => p.name.toLowerCase().includes(linkSearch.toLowerCase())).map((process, index) => (
                        <div key={process.organizationalProcessID || `orgp-${index}`} className="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded-md">
                              <Checkbox
                            id={`orgp-${process.organizationalProcessID || index}`}
                            checked={selectedItems.organizationalProcesses.includes(String(process.organizationalProcessID))}
                            onCheckedChange={checked => {
                                  setSelectedItems(prev => ({
                                    ...prev,
                                organizationalProcesses: checked
                                  ? [...prev.organizationalProcesses, String(process.organizationalProcessID)]
                                  : prev.organizationalProcesses.filter(id => id !== String(process.organizationalProcessID))
                                  }));
                                }}
                              />
                          <Label htmlFor={`orgp-${process.organizationalProcessID || index}`} className="flex-1 cursor-pointer flex items-center">
                            <GitBranch className="h-4 w-4 mr-2 text-violet-500" />
                            <span>{process.name}</span>
                          </Label>
                        </div>
                      ))
                    )}
                  </div>
                )}
                {activeTab === "controls" && (
                  <div key="controls-list" className="space-y-2">
                    {controls.length === 0 ? (
                      <p className="text-gray-500 text-center py-8">Aucun contrôle trouvé</p>
                    ) : (
                      controls.filter(control => control.name.toLowerCase().includes(linkSearch.toLowerCase())).map((control, index) => (
                        <div key={control.controlID || `control-${index}`} className="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded-md">
                          <Checkbox
                            id={`control-${control.controlID || index}`}
                            checked={selectedItems.controls.includes(String(control.controlID))}
                            onCheckedChange={checked => {
                              setSelectedItems(prev => ({
                                ...prev,
                                controls: checked
                                  ? [...prev.controls, String(control.controlID)]
                                  : prev.controls.filter(id => id !== String(control.controlID))
                              }));
                            }}
                          />
                          <Label htmlFor={`control-${control.controlID || index}`} className="flex-1 cursor-pointer flex items-center">
                            <ShieldCheck className="h-4 w-4 mr-2 text-blue-500" />
                            <span>{control.name}</span>
                              </Label>
                            </div>
                          ))
                      )}
                    </div>
                  )}
                </>
              )}
            </div>
          <DialogFooter className="bg-white pt-4 pb-2 border-t flex justify-end gap-2 pr-4">
            <Button variant="outline" onClick={() => {
              setIsLinkDialogOpen(false);
              setSelectedItems({ entities: [], risks: [], businessProcesses: [], organizationalProcesses: [], controls: [] });
            }}>
              Annuler
            </Button>
            <Button className="bg-[#F62D51] hover:bg-[#F62D51]/90" onClick={async () => {
              const totalSelected = selectedItems.entities.length + selectedItems.risks.length + 
                selectedItems.businessProcesses.length + selectedItems.organizationalProcesses.length + selectedItems.controls.length;
                if (totalSelected === 0) {
                  toast.error("Veuillez sélectionner au moins un élément à relier");
                  return;
                }
              const existingReferences = new Set(perimetreItems.map(item => `${item.referenceType}-${item.referenceId}`));
                const newItems = [];
              
              // Handle entities
              selectedItems.entities.forEach(entityId => {
                const key = `entity-${entityId}`;
                if (!existingReferences.has(key)) {
                  const entity = entities.find(e => e.entityID === entityId);
                  if (entity) newItems.push({ 
                    id: Date.now() + Math.random(), 
                    nom: entity.name, 
                    type: "Entité", 
                    referenceId: String(entity.entityID), 
                    referenceType: "entity" 
                  });
                }
              });

              // Handle risks
                selectedItems.risks.forEach(riskId => {
                const key = `risk-${riskId}`;
                if (!existingReferences.has(key)) {
                  const risk = risks.find(r => r.riskID === riskId);
                  if (risk) newItems.push({ 
                    id: Date.now() + Math.random(), 
                    nom: risk.name, 
                    type: "Risque", 
                    referenceId: String(risk.riskID), 
                    referenceType: "risk" 
                  });
                }
              });

              // Handle business processes
              selectedItems.businessProcesses.forEach(processId => {
                const key = `businessProcess-${processId}`;
                if (!existingReferences.has(key)) {
                  const process = businessProcesses.find(p => p.businessProcessID === processId);
                  if (process) newItems.push({ 
                    id: Date.now() + Math.random(), 
                    nom: process.name, 
                    type: "Processus métier", 
                    referenceId: String(process.businessProcessID), 
                    referenceType: "businessProcess" 
                  });
                }
              });

              // Handle organizational processes
              selectedItems.organizationalProcesses.forEach(processId => {
                const key = `organizationalProcess-${processId}`;
                if (!existingReferences.has(key)) {
                  const process = organizationalProcesses.find(p => p.organizationalProcessID === processId);
                  if (process) newItems.push({ 
                    id: Date.now() + Math.random(), 
                    nom: process.name, 
                    type: "Processus organisationnel", 
                    referenceId: String(process.organizationalProcessID), 
                    referenceType: "organizationalProcess" 
                  });
                }
              });

              // Handle controls
                selectedItems.controls.forEach(controlId => {
                const key = `control-${controlId}`;
                if (!existingReferences.has(key)) {
                  const control = controls.find(c => c.controlID === controlId);
                  if (control) newItems.push({ 
                    id: Date.now() + Math.random(), 
                    nom: control.name, 
                    type: "Contrôle", 
                    referenceId: String(control.controlID), 
                    referenceType: "control" 
                  });
                }
              });

              if (newItems.length > 0) {
                // Update local state first
                setPerimetreItems(prev => [...prev, ...newItems]);
                
                // Transform all items into the format expected by the API
                const relationships = {
                  entities: [...perimetreItems, ...newItems]
                    .filter(item => item.referenceType === 'entity')
                    .map(item => item.referenceId),
                  risks: [...perimetreItems, ...newItems]
                    .filter(item => item.referenceType === 'risk')
                    .map(item => item.referenceId),
                  organizationalProcesses: [...perimetreItems, ...newItems]
                    .filter(item => item.referenceType === 'organizationalProcess')
                    .map(item => item.referenceId),
                  controls: [...perimetreItems, ...newItems]
                    .filter(item => item.referenceType === 'control')
                    .map(item => item.referenceId),
                  businessProcesses: [...perimetreItems, ...newItems]
                    .filter(item => item.referenceType === 'businessProcess')
                    .map(item => item.referenceId)
                };

                console.log('Sending relationships to backend:', relationships);

                try {
                  // Save to backend
                  const response = await updateAuditScopeRelationships(missionAudit.id, relationships);
                  if (response.success) {
                    toast.success(`${newItems.length} élément(s) ajouté(s) au périmètre`);
                    // Close modal and reset selections only on success
                    setIsLinkDialogOpen(false);
                    setSelectedItems({ 
                      entities: [], 
                      risks: [], 
                      organizationalProcesses: [], 
                      controls: [],
                      businessProcesses: []
                    });
                  } else {
                    throw new Error(response.message || 'Erreur lors de la sauvegarde');
                  }
                } catch (error) {
                  console.error('Error saving relationships:', error);
                  toast.error(error.message || 'Erreur lors de la sauvegarde des relations');
                  // Revert local state on error
                  setPerimetreItems(prev => prev.filter(item => !newItems.some(newItem => newItem.id === item.id)));
                }
              } else {
                toast.info("Aucun nouvel élément à ajouter");
                // Close modal even if no new items to add
                setIsLinkDialogOpen(false);
                setSelectedItems({ 
                  entities: [], 
                  risks: [], 
                  organizationalProcesses: [], 
                  controls: [],
                  businessProcesses: []
                });
              }
            }}>
              Valider
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default PerimetreProgrammeTab;