import axios from 'axios';
import { toast } from 'sonner';
import { getApiBaseUrl } from './api-config';

// Create a function to get auth token
const getAuthToken = () => localStorage.getItem('authToken');

// Configure axios defaults
axios.defaults.baseURL = getApiBaseUrl();
axios.defaults.withCredentials = true;
axios.defaults.headers.common['Content-Type'] = 'application/json';

// Request interceptor for global axios
axios.interceptors.request.use(
  (config) => {
    // Get token from localStorage
    const token = getAuthToken();
    
    // If token exists, add it to the Authorization header
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
      // console.log(`[Global Axios] Adding token to request: ${config.url}`);
    } else {
      // console.log(`[Global Axios] No token available for request: ${config.url}`);
    }
    
    return config;
  },
  (error) => {
    // console.error('[Global Axios] Request error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for global axios
axios.interceptors.response.use(
  (response) => {
    // If this is a login response, save the token to localStorage
    if (response.config.url.includes('/auth/login') && response.data?.token) {
      localStorage.setItem('authToken', response.data.token);
      // console.log('[Global Axios] Token saved from login response');
    }
    return response;
  },
  (error) => {
    // Handle authentication errors
    if (error.response?.status === 401) {
      // console.log(`[Global Axios] 401 Unauthorized error for: ${error.config?.url}`);
      toast.error('Authentication failed. Please log in again.');
    }
    
    // Handle server errors
    if (error.response?.status >= 500) {
      toast.error('Server error. Please try again later.');
    }
    
    return Promise.reject(error);
  }
);

// Create a custom axios instance with the same configuration
const axiosInstance = axios.create({
  baseURL: getApiBaseUrl(),
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Apply the same interceptors to the instance
axiosInstance.interceptors.request.use(
  axios.interceptors.request.handlers[0].fulfilled,
  axios.interceptors.request.handlers[0].rejected
);

axiosInstance.interceptors.response.use(
  axios.interceptors.response.handlers[0].fulfilled,
  axios.interceptors.response.handlers[0].rejected
);

// console.log('[Axios Config] Axios configured with interceptors');

export default axiosInstance;
