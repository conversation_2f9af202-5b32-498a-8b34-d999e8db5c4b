import { useState, useEffect, useCallback } from "react";
import { useOutletContext } from "react-router-dom";
import { useSelector } from "react-redux";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogDescription } from "../../../../components/ui/dialog";
import { Button } from "../../../../components/ui/button";
import { Label } from "../../../../components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../../../components/ui/select";
import { Textarea } from "../../../../components/ui/textarea";
import { Activity, TrendingUp, ShieldAlert, BarChart4, Loader2, User, Clock, Plus, AlertTriangle, RefreshCw } from "lucide-react";
import { toast } from "sonner";
import axios from "axios";
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from "../../../../components/ui/table";
import { format } from "date-fns";
import { getApiBaseUrl } from "@/utils/api-config";
import { useTranslation } from 'react-i18next';

function RisksEvaluation() {
  const { t } = useTranslation();
  const { risk, refreshRisk } = useOutletContext();
  const { user } = useSelector(state => state.auth);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedImpact, setSelectedImpact] = useState("");
  const [selectedProbability, setSelectedProbability] = useState("");
  const [notes, setNotes] = useState("");
  const [evaluationHistory, setEvaluationHistory] = useState([]);
  const [currentRisk, setCurrentRisk] = useState(null);
  const API_BASE_URL = getApiBaseUrl();
  // Fetch the latest risk data from the server
  const fetchRiskData = useCallback(async () => {
    if (!risk?.riskID) return;

    setIsLoading(true);
    try {
      const response = await axios.get(
        `${API_BASE_URL}/risk/${risk.riskID}`,
        {
          withCredentials: true,
          headers: { "Content-Type": "application/json" },
        }
      );

      if (response.data.success) {
        setCurrentRisk(response.data.data);
        // Initialize evaluations array if it doesn't exist
        const evaluations = response.data.data.evaluations || [];
        setEvaluationHistory(evaluations);
      }
    } catch (error) {
      console.error("Error fetching risk data:", error);
      toast.error(t('admin.risks.management.evaluation.error_loading_data', "Failed to load risk evaluation data"));
    } finally {
      setIsLoading(false);
    }
  }, [risk?.riskID]);

  // Fetch risk data when component mounts or risk ID changes
  useEffect(() => {
    fetchRiskData();
  }, [fetchRiskData]);

  // Initialize form with current risk values when dialog opens
  useEffect(() => {
    if (isDialogOpen && currentRisk) {
      setSelectedImpact(currentRisk.impact || "");
      setSelectedProbability(currentRisk.probability || "");
    }
  }, [isDialogOpen, currentRisk]);

  const handleAddEvaluation = async () => {
    if (!selectedImpact || !selectedProbability) {
      toast.error(t('admin.risks.management.evaluation.validation_required', "Please select both Impact and Probability"));
      return;
    }

    if (!currentRisk?.riskID) {
      toast.error(t('admin.risks.management.evaluation.risk_not_available', "Risk data not available. Please try again."));
      return;
    }

    setIsSubmitting(true);
    try {
      const currentDate = new Date().toISOString();
      // Use username from Redux auth state if available, fallback to localStorage or "Current User"
      const username = user?.username || localStorage.getItem("username") || "Current User";

      // Create the new evaluation
      const newEvaluation = {
        date: currentDate,
        user: username,
        impact: selectedImpact,
        probability: selectedProbability,
        notes: notes || "",
      };

      // Create an updated version of the risk with the new evaluation
      const updatedRisk = {
        ...currentRisk,
        impact: selectedImpact, // Update the current risk with new values
        probability: selectedProbability,
        evaluations: [newEvaluation, ...(currentRisk.evaluations || [])],
      };

      // Calculate residual risk based on new impact and probability
      if (currentRisk.DMR) {
        let inherentRiskValue = parseInt(selectedImpact) * parseInt(selectedProbability);
        let controlLevelValue = parseInt(currentRisk.DMR);
        let residualRiskValue = inherentRiskValue - controlLevelValue;

        // Ensure residual risk is at least 1
        if (residualRiskValue < 1) residualRiskValue = 1;

        updatedRisk.residualRisk = residualRiskValue.toString();
      }

      // Make API call to update the risk
      const response = await axios.put(
        `${API_BASE_URL}/risk/${currentRisk.riskID}`,
        updatedRisk,
        {
          withCredentials: true,
          headers: { "Content-Type": "application/json" },
        }
      );

      if (response.data.success) {
        toast.success(t('admin.risks.management.evaluation.success_added', "Evaluation added successfully"));
        setIsDialogOpen(false);
        setNotes(""); // Only clear notes, keep the selected values for next evaluation

        // Update local state with the new data
        setCurrentRisk(updatedRisk);
        setEvaluationHistory(updatedRisk.evaluations || []);

        // Also refresh the parent component's risk data
        refreshRisk();
      }
    } catch (error) {
      toast.error(
        error.response?.data?.message || t('admin.risks.management.evaluation.error_adding', "Failed to add evaluation")
      );
      console.error("Error adding evaluation:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatDate = (dateString) => {
    try {
      return format(new Date(dateString), "MMM d, yyyy HH:mm");
    } catch {
      return dateString || "N/A";
    }
  };

  const getImpactLabel = (value) => {
    const impactMap = {
      "1": { label: t('admin.risks.management.evaluation.impact.very_low', "Very Low"), color: "bg-blue-100 text-blue-800" },
      "2": { label: t('admin.risks.management.evaluation.impact.low', "Low"), color: "bg-green-100 text-green-800" },
      "3": { label: t('admin.risks.management.evaluation.impact.medium', "Medium"), color: "bg-yellow-100 text-yellow-800" },
      "4": { label: t('admin.risks.management.evaluation.impact.high', "High"), color: "bg-orange-100 text-orange-800" },
      "5": { label: t('admin.risks.management.evaluation.impact.very_high', "Very High"), color: "bg-red-100 text-red-800" }
    };
    return impactMap[value] || { label: t('admin.risks.management.evaluation.impact.unknown', "Unknown"), color: "bg-gray-100 text-gray-800" };
  };

  const getProbabilityLabel = (value) => {
    const probabilityMap = {
      "1": { label: t('admin.risks.management.evaluation.probability.very_low', "Very Low"), color: "bg-blue-100 text-blue-800" },
      "2": { label: t('admin.risks.management.evaluation.probability.low', "Low"), color: "bg-green-100 text-green-800" },
      "3": { label: t('admin.risks.management.evaluation.probability.medium', "Medium"), color: "bg-yellow-100 text-yellow-800" },
      "4": { label: t('admin.risks.management.evaluation.probability.high', "High"), color: "bg-orange-100 text-orange-800" },
      "5": { label: t('admin.risks.management.evaluation.probability.very_high', "Very High"), color: "bg-red-100 text-red-800" }
    };
    return probabilityMap[value] || { label: t('admin.risks.management.evaluation.probability.unknown', "Unknown"), color: "bg-gray-100 text-gray-800" };
  };

  // Display timestamp of most recent evaluation
  const getLastEvaluationInfo = () => {
    if (evaluationHistory && evaluationHistory.length > 0) {
      const lastEval = evaluationHistory[0];
      return (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <p className="text-xs text-gray-500 flex items-center gap-1">
            <Clock className="h-3 w-3" />
            {t('admin.risks.management.evaluation.last_evaluated', 'Last evaluated {{date}} by {{user}}', {
              date: formatDate(lastEval.date),
              user: lastEval.user
            })}
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold flex items-center gap-2">
          <Activity className="h-5 w-5 text-gray-500" />
          {t('admin.risks.management.evaluation.title', "Risk Evaluation")}
        </h2>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={fetchRiskData}
            disabled={isLoading}
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            {t('admin.risks.management.evaluation.refresh', "Refresh")}
          </Button>
          <Button
            onClick={() => setIsDialogOpen(true)}
            className="bg-[#F62D51] hover:bg-red-700 flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            {t('admin.risks.management.evaluation.add_evaluation', "Add Evaluation")}
          </Button>
        </div>
      </div>

      {/* Current Evaluation */}
      <div className="border rounded-md p-4 bg-gray-50 mb-6">
        <h3 className="text-md font-medium mb-4">{t('admin.risks.management.evaluation.current_assessment', "Current Assessment")}</h3>
        {isLoading ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="flex items-center gap-3">
                <div className="bg-gray-200 p-2 rounded-md">
                  <TrendingUp className="h-5 w-5 text-gray-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">{t('admin.risks.management.evaluation.impact_label', "Impact")}</p>
                  {currentRisk?.impact ? (
                    <div className="flex items-center gap-2 mt-1">
                      <span className={`px-2.5 py-0.5 rounded-full text-xs font-medium ${getImpactLabel(currentRisk.impact).color}`}>
                        {getImpactLabel(currentRisk.impact).label}
                      </span>
                    </div>
                  ) : (
                    <p className="text-lg font-semibold">{t('admin.risks.management.evaluation.not_assessed', "Not assessed")}</p>
                  )}
                </div>
              </div>
              <div className="flex items-center gap-3">
                <div className="bg-gray-200 p-2 rounded-md">
                  <BarChart4 className="h-5 w-5 text-gray-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">{t('admin.risks.management.evaluation.probability_label', "Probability")}</p>
                  {currentRisk?.probability ? (
                    <div className="flex items-center gap-2 mt-1">
                      <span className={`px-2.5 py-0.5 rounded-full text-xs font-medium ${getProbabilityLabel(currentRisk.probability).color}`}>
                        {getProbabilityLabel(currentRisk.probability).label}
                      </span>
                    </div>
                  ) : (
                    <p className="text-lg font-semibold">{t('admin.risks.management.evaluation.not_assessed', "Not assessed")}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Show last evaluation timestamp if available */}
            {getLastEvaluationInfo()}
          </>
        )}
      </div>

      {/* Evaluation History */}
      <div>
        <h3 className="text-md font-medium mb-4 flex items-center gap-2">
          <ShieldAlert className="h-5 w-5 text-gray-500" />
          {t('admin.risks.management.evaluation.history_title', "Evaluation History")}
        </h3>

        {isLoading ? (
          <div className="flex justify-center items-center py-12 border rounded-lg">
            <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
          </div>
        ) : evaluationHistory && evaluationHistory.length > 0 ? (
          <div className="border rounded-md overflow-hidden">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t('admin.risks.management.evaluation.table.date', "Date")}</TableHead>
                  <TableHead>{t('admin.risks.management.evaluation.table.user', "User")}</TableHead>
                  <TableHead>{t('admin.risks.management.evaluation.table.impact', "Impact")}</TableHead>
                  <TableHead>{t('admin.risks.management.evaluation.table.probability', "Probability")}</TableHead>
                  <TableHead>{t('admin.risks.management.evaluation.table.notes', "Notes")}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {evaluationHistory.map((evaluation, index) => (
                  <TableRow key={index} className={index === 0 ? "bg-blue-50/30" : ""}>
                    <TableCell>{formatDate(evaluation.date)}</TableCell>
                    <TableCell>{evaluation.user}</TableCell>
                    <TableCell>
                      <span className={`px-2.5 py-0.5 rounded-full text-xs font-medium ${getImpactLabel(evaluation.impact).color}`}>
                        {getImpactLabel(evaluation.impact).label}
                      </span>
                    </TableCell>
                    <TableCell>
                      <span className={`px-2.5 py-0.5 rounded-full text-xs font-medium ${getProbabilityLabel(evaluation.probability).color}`}>
                        {getProbabilityLabel(evaluation.probability).label}
                      </span>
                    </TableCell>
                    <TableCell className="max-w-xs truncate">{evaluation.notes || "—"}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        ) : (
          <div className="bg-white rounded-lg border border-gray-200 shadow-sm flex flex-col items-center justify-center p-12 text-center">
            <div className="w-full max-w-md flex flex-col items-center justify-center space-y-6">
              <div className="p-4 rounded-full bg-gray-50">
                <Activity className="h-10 w-10 text-gray-400" />
              </div>

              <div className="space-y-2">
                <h3 className="text-lg font-medium text-gray-800">{t('admin.risks.management.evaluation.no_history_title', "No evaluation history")}</h3>
                <p className="text-gray-500 text-sm max-w-xs mx-auto">
                  {t('admin.risks.management.evaluation.no_history_description', "Add evaluations to track how this risk changes over time")}
                </p>
              </div>

              <Button
                onClick={() => setIsDialogOpen(true)}
                className="bg-[#F62D51] hover:bg-red-700 flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                {t('admin.risks.management.evaluation.add_evaluation', "Add Evaluation")}
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Dialog for adding a new evaluation */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>{t('admin.risks.management.evaluation.modal.title', "New Risk Evaluation")}</DialogTitle>
            <DialogDescription>
              {t('admin.risks.management.evaluation.modal.description', "Update the risk assessment with current information")}
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="flex items-center gap-2 text-sm text-gray-500 mb-2">
              <User className="h-4 w-4" />
              <span>{user?.username || t('admin.risks.management.evaluation.modal.current_user', "Current User")}</span>
              <Clock className="h-4 w-4 ml-4" />
              <span>{format(new Date(), "MMM d, yyyy HH:mm")}</span>
            </div>

            <div className="space-y-2">
              <Label htmlFor="impact" className="font-medium">
                {t('admin.risks.management.evaluation.modal.impact_label', "Impact")}
              </Label>
              <Select
                value={selectedImpact}
                onValueChange={setSelectedImpact}
              >
                <SelectTrigger>
                  <SelectValue placeholder={t('admin.risks.management.evaluation.modal.select_impact', "Select impact level")}>
                    {selectedImpact ? (
                      <div className="flex items-center gap-2">
                        <span className={`w-2 h-2 rounded-full ${getImpactLabel(selectedImpact).color}`}></span>
                        {getImpactLabel(selectedImpact).label}
                      </div>
                    ) : (
                      t('admin.risks.management.evaluation.modal.select_impact', "Select impact level")
                    )}
                  </SelectValue>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">
                    <div className="flex items-center gap-2">
                      <span className="w-2 h-2 rounded-full bg-blue-500"></span>
                      {getImpactLabel("1").label}
                    </div>
                  </SelectItem>
                  <SelectItem value="2">
                    <div className="flex items-center gap-2">
                      <span className="w-2 h-2 rounded-full bg-green-500"></span>
                      {getImpactLabel("2").label}
                    </div>
                  </SelectItem>
                  <SelectItem value="3">
                    <div className="flex items-center gap-2">
                      <span className="w-2 h-2 rounded-full bg-yellow-500"></span>
                      {getImpactLabel("3").label}
                    </div>
                  </SelectItem>
                  <SelectItem value="4">
                    <div className="flex items-center gap-2">
                      <span className="w-2 h-2 rounded-full bg-orange-500"></span>
                      {getImpactLabel("4").label}
                    </div>
                  </SelectItem>
                  <SelectItem value="5">
                    <div className="flex items-center gap-2">
                      <span className="w-2 h-2 rounded-full bg-red-500"></span>
                      {getImpactLabel("5").label}
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="probability" className="font-medium">
                {t('admin.risks.management.evaluation.modal.probability_label', "Probability")}
              </Label>
              <Select
                value={selectedProbability}
                onValueChange={setSelectedProbability}
              >
                <SelectTrigger>
                  <SelectValue placeholder={t('admin.risks.management.evaluation.modal.select_probability', "Select probability level")}>
                    {selectedProbability ? (
                      <div className="flex items-center gap-2">
                        <span className={`w-2 h-2 rounded-full ${getProbabilityLabel(selectedProbability).color}`}></span>
                        {getProbabilityLabel(selectedProbability).label}
                      </div>
                    ) : (
                      t('admin.risks.management.evaluation.modal.select_probability', "Select probability level")
                    )}
                  </SelectValue>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">
                    <div className="flex items-center gap-2">
                      <span className="w-2 h-2 rounded-full bg-blue-500"></span>
                      {getProbabilityLabel("1").label}
                    </div>
                  </SelectItem>
                  <SelectItem value="2">
                    <div className="flex items-center gap-2">
                      <span className="w-2 h-2 rounded-full bg-green-500"></span>
                      {getProbabilityLabel("2").label}
                    </div>
                  </SelectItem>
                  <SelectItem value="3">
                    <div className="flex items-center gap-2">
                      <span className="w-2 h-2 rounded-full bg-yellow-500"></span>
                      {getProbabilityLabel("3").label}
                    </div>
                  </SelectItem>
                  <SelectItem value="4">
                    <div className="flex items-center gap-2">
                      <span className="w-2 h-2 rounded-full bg-orange-500"></span>
                      {getProbabilityLabel("4").label}
                    </div>
                  </SelectItem>
                  <SelectItem value="5">
                    <div className="flex items-center gap-2">
                      <span className="w-2 h-2 rounded-full bg-red-500"></span>
                      {getProbabilityLabel("5").label}
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="notes" className="font-medium">
                {t('admin.risks.management.evaluation.modal.notes_label', "Notes (Optional)")}
              </Label>
              <Textarea
                id="notes"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                placeholder={t('admin.risks.management.evaluation.modal.notes_placeholder', "Add any relevant information about this evaluation")}
                rows={3}
              />
            </div>
          </div>

          <div className="flex flex-col gap-1 mt-2 mb-2">
            {(!selectedImpact || !selectedProbability) && (
              <div className="flex items-center text-amber-600 text-sm">
                <AlertTriangle className="h-4 w-4 mr-1" />
                <span>{t('admin.risks.management.evaluation.modal.validation_required', "Impact and Probability are required")}</span>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setIsDialogOpen(false);
                setNotes("");
              }}
            >
              {t('admin.risks.management.evaluation.modal.cancel', "Cancel")}
            </Button>
            <Button
              onClick={handleAddEvaluation}
              className="bg-[#F62D51] hover:bg-red-700"
              disabled={isSubmitting || !selectedImpact || !selectedProbability}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {t('admin.risks.management.evaluation.modal.saving', "Saving...")}
                </>
              ) : (
                t('admin.risks.management.evaluation.modal.save', "Save Evaluation")
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default RisksEvaluation;
