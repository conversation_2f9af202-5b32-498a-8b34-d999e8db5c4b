const db = require('./models');

// Sync database with models - force:true will drop tables if they exist
// Commented out since we're using migrations instead
// db.sequelize.sync({ alter: true })
//   .then(() => {
//     console.log('Database synchronized successfully - tables altered if needed');
//   })
//   .catch((err) => {
//     console.error('Error synchronizing database:', err);
//     // Try without alter if it fails
//     console.log('Retrying without alter option...');
//     return db.sequelize.sync();
//   })
//   .then(() => {
//     console.log('Database sync completed');
//   })
//   .catch((err) => {
//     console.error('All sync attempts failed:', err);
//   }); 