module.exports = (sequelize, DataTypes) => {
  const Control = sequelize.define('Control', {
    controlID: {
      type: DataTypes.STRING,
      primaryKey: true,
      allowNull: false,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    code: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    controlKey: {
      type: DataTypes.INTEGER, // 0 or 1 in Excel, treated as a numeric flag
      allowNull: true,
    },
    controlExecutionMethod: {
      type: DataTypes.STRING, // e.g., "Observation", "Contrôle systématique"
      allowNull: true,
    },
    objective: {
      type: DataTypes.TEXT, // Longer text possible
      allowNull: true,
    },
    executionProcedure: {
      type: DataTypes.TEXT, // e.g., "Vérifier les étapes de contrôles ci-dessous"
      allowNull: true,
    },
    operationalCost: {
      type: DataTypes.FLOAT, // e.g., 15000, 23000
      allowNull: true,
    },
    organizationalLevel: {
      type: DataTypes.STRING, // e.g., "Global"
      allowNull: true,
    },
    sampleType: {
      type: DataTypes.STRING, // e.g., "Facturé", "Contractuel"
      allowNull: true,
    },
    testingFrequency: {
      type: DataTypes.STRING, // e.g., "Trimestrielle", "Semestrielle"
      allowNull: true,
    },
    testingMethod: {
      type: DataTypes.STRING, // e.g., "Enquête"
      allowNull: true,
    },
    testingPopulationSize: {
      type: DataTypes.INTEGER, // e.g., 10, 50
      allowNull: true,
    },
    testingProcedure: {
      type: DataTypes.TEXT, // e.g., "Review if all the approved request..."
      allowNull: true,
    },
    implementingActionPlan: {
      type: DataTypes.TEXT, // e.g., "* Améliorer le contrôle des paiements"
      allowNull: true,
    },
    designQuality: {
      type: DataTypes.STRING, // "Satisfaisant" or "Insatisfaisant"
      allowNull: true,
    },
    effectivenessLevel: {
      type: DataTypes.STRING, // "Satisfaisant" or "Insatisfaisant"
      allowNull: true,
    },
    businessProcess: {
      type: DataTypes.STRING, // Foreign key to BusinessProcess
      allowNull: true,
      references: {
        model: 'BusinessProcess',
        key: 'businessProcessID',
      },
    },
    organizationalProcess: {
      type: DataTypes.STRING, // Foreign key to OrganizationalProcess
      allowNull: true,
      references: {
        model: 'OrganizationalProcess',
        key: 'organizationalProcessID',
      },
    },
    operation: {
      type: DataTypes.STRING, // Foreign key to Operation
      allowNull: true,
      references: {
        model: 'Operation',
        key: 'operationID',
      },
    },
    application: {
      type: DataTypes.STRING, // Foreign key to Application
      allowNull: true,
      references: {
        model: 'Application',
        key: 'applicationID',
      },
    },
    entity: {
      type: DataTypes.STRING, // Foreign key to Entity
      allowNull: true,
      references: {
        model: 'Entity',
        key: 'entityID',
      },
    },
    controlType: {
      type: DataTypes.STRING, // Foreign key to ControlType
      allowNull: true,
      references: {
        model: 'ControlType',
        key: 'controlTypeID',
      },
    },
    risk: {
      type: DataTypes.STRING, // Foreign key to Risk
      allowNull: true,
      references: {
        model: 'Risk',
        key: 'riskID',
      },
    },
    comment: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
  }, {
    tableName: 'Control',
    timestamps: false,
    indexes: [
      {
        unique: true,
        fields: ['controlID'],
      },
    ],
  });

  return Control;
};