import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>lipboard<PERSON>ist,
  <PERSON><PERSON><PERSON><PERSON>gle,
  ShieldCheck,
  Plus,
  Link,
  ChevronUp,
  ChevronDown,
  Edit,
  Trash2,
  Upload,
  File,
  Paperclip,
  Loader2,
  AlertCircle
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { toast } from "sonner";
import { useParams, useNavigate } from "react-router-dom";
import { getAuditConstatById, updateConstat } from '@/services/audit-constat-service';
import axios from 'axios';
import {
  getAllRecommendations,
  getRecommendationsByConstatId,
  createRecommendation,
  deleteRecommendation,
  linkRecommendationToConstat,
  updateRecommendation
} from '@/services/audit-recommendation-service';

function CaracteristiquesConstatTab() {
  const { planId, missionAuditId, activiteId, constatId } = useParams();
  const navigate = useNavigate();

  // Section toggles
  const [isCaracteristiquesOpen, setIsCaracteristiquesOpen] = useState(true);
  const [isRecommandationOpen, setIsRecommandationOpen] = useState(true);
  const [isRisqueControleOpen, setIsRisqueControleOpen] = useState(true);
  const [risqueControleTab, setRisqueControleTab] = useState("risque");
  const [isAttachmentsOpen, setIsAttachmentsOpen] = useState(true);

  // Form state
  const [formData, setFormData] = useState({
    nom: "",
    type: "Point Faible",
    impact: "3",
    activiteAudit: "",
    description: "",
    analyseCauses: "",
    recommandations: [],
    risques: [],
    piecesJointes: [],
    responsable: "",
    auditActivityID: "",
  });

  const [isLoadingConstat, setIsLoadingConstat] = useState(true);
  const [isSavingConstat, setIsSavingConstat] = useState(false);
  const [error, setError] = useState(null);

  // Mock data
  // const activitesAudit = [\n  //   { id: 1, nom: \"Activité 1\" },\n  //   { id: 2, nom: \"Activité 2\" },\n  //   { id: 3, nom: \"Activité 3\" }\n  // ];
  const impactOptions = [
    { value: "5", label: "Très fort" },
    { value: "4", label: "Fort" },
    { value: "3", label: "Moyen" },
    { value: "2", label: "Faible" },
    { value: "1", label: "Très faible" }
  ];
  const typeOptions = [
    { value: "Point Faible", label: "Point Faible" },
    { value: "Point Fort", label: "Point Fort" }
  ];

  // Replace mock recommendations state with real data state
  const [recommandations, setRecommandations] = useState([]);
  const [isLoadingRecommendations, setIsLoadingRecommendations] = useState(true);
  const [allRecommendations, setAllRecommendations] = useState([]);
  const [isLoadingAllRecommendations, setIsLoadingAllRecommendations] = useState(false);
  
  // New recommendation form state
  const [newRecommendation, setNewRecommendation] = useState({
    name: '',
    priorite: 'moyen'
  });

  // Priority options for the select
  const prioriteOptions = [
    { value: 'très fort', label: 'Très fort' },
    { value: 'fort', label: 'Fort' },
    { value: 'moyen', label: 'Moyen' },
    { value: 'faible', label: 'Faible' },
    { value: 'très faible', label: 'Très faible' }
  ];

  // Keep original lighter colors for table badges
  const prioriteColors = {
    'très fort': 'bg-red-100 text-red-800',
    'fort': 'bg-orange-100 text-orange-800',
    'moyen': 'bg-yellow-100 text-yellow-800',
    'faible': 'bg-green-100 text-green-800',
    'très faible': 'bg-blue-100 text-blue-800'
  };

  // Add new color mapping for modal indicators
  const prioriteModalColors = {
    'très fort': 'bg-red-500',
    'fort': 'bg-orange-500',
    'moyen': 'bg-yellow-400',
    'faible': 'bg-green-500',
    'très faible': 'bg-blue-500'
  };

  // Risques/Contrôles mock
  const [risques] = useState([
    { id: 1, nom: "Risque 1", impact: "Élevé", probabilite: "Moyenne", criticite: "Élevée" },
    { id: 2, nom: "Risque 2", impact: "Moyen", probabilite: "Faible", criticite: "Moyenne" }
  ]);
  const [controles] = useState([
    { id: 1, nom: "Contrôle 1", type: "Préventif", statut: "Efficace" },
    { id: 2, nom: "Contrôle 2", type: "Détectif", statut: "À améliorer" }
  ]);

  // Add color mapping for impact options (same as in risk evaluation)
  const impactColors = {
    '5': 'bg-red-500',
    '4': 'bg-orange-500',
    '3': 'bg-yellow-400',
    '2': 'bg-green-500',
    '1': 'bg-blue-500',
  };

  // Pièces jointes state (mocked)
  const [attachments, setAttachments] = useState([
    { id: 1, name: "rapport1.pdf", size: 102400, date: "2024-07-31T10:00:00Z" },
    { id: 2, name: "evidence2.png", size: 204800, date: "2024-07-30T15:30:00Z" }
  ]);
  const [isUploading, setIsUploading] = useState(false);

  // Format file size
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };
  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };
  // Handle file upload (mocked)
  const handleFileUpload = (e) => {
    const files = Array.from(e.target.files);
    if (files.length === 0) return;
    setIsUploading(true);
    setTimeout(() => {
      setAttachments(prev => [
        ...prev,
        ...files.map((file, idx) => ({
          id: Date.now() + idx,
          name: file.name,
          size: file.size,
          date: new Date().toISOString()
        }))
      ]);
      setIsUploading(false);
    }, 1000);
  };
  // Handle delete
  const handleDeleteAttachment = (id) => {
    setAttachments(prev => prev.filter(a => a.id !== id));
  };

  // Handlers
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };
  const handleSelectChange = (name, value) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Add dialog state for Nouveau/Relier actions
  const [openDialog, setOpenDialog] = useState({ type: null, tab: null });

  // Add a function to handle recommendation navigation
  const handleRecommandationClick = (recommandationId) => {
    navigate(`/audit/plans-daudit/${planId}/missions-audits/${missionAuditId}/activites/${activiteId}/constats/${constatId}/recommandations/${recommandationId}`);
  };

  // Update the impact mapping
  const impactMapping = {
    'tres fort': '5',
    'fort': '4',
    'moyen': '3',
    'faible': '2',
    'tres faible': '1'
  };

  // Add impact mapping constant
  const impactValueToEnum = {
    '1': 'tres faible',
    '2': 'faible',
    '3': 'moyen',
    '4': 'fort',
    '5': 'tres fort'
  };

  // Update the fetchConstatData function
  const fetchConstatData = async () => {
    if (!constatId) return;

    setIsLoadingConstat(true);
    try {
      const response = await getAuditConstatById(constatId);
      if (response?.success) {
        const fetchedData = response.data;
        // Use the impact mapping
        const formattedImpact = impactMapping[fetchedData.impact] || '3';

        setFormData({
          nom: fetchedData.name || '',
          type: fetchedData.type || 'Point Faible',
          impact: formattedImpact,
          description: fetchedData.description || '',
          analyseCauses: fetchedData.causes || '',
          recommandations: fetchedData.recommandations || [],
          risques: fetchedData.risques || [],
          piecesJointes: fetchedData.piecesJointes || [],
          responsable: fetchedData.responsable || '',
          auditActivityID: fetchedData.auditActivityID || '',
        });
      }
    } catch (error) {
      console.error('Error fetching constat:', error);
      setError('Erreur lors du chargement du constat');
    } finally {
      setIsLoadingConstat(false);
    }
  };

  // Fetch constat data on component mount or constatId change
  useEffect(() => {
    const abortController = new AbortController();
    const signal = abortController.signal;

    const fetchConstat = async () => {
      if (!constatId) {
        setIsLoadingConstat(false);
        setError("Constat ID is missing.");
        toast.error("Constat ID is missing for fetching details.");
        return;
      }

      setIsLoadingConstat(true);
      setError(null);

      try {
        const response = await getAuditConstatById(constatId, signal);
        console.log('Fetched constat data:', response);
        if (response.success && response.data) {
          const fetchedData = response.data;
          // Map backend impact string to frontend number
          // Use the exact string from backend for lookup
          const backendImpactString = fetchedData.impact; // Use the string directly
          const formattedImpact = backendImpactString === '5' ? '5' : backendImpactString === '4' ? '4' : backendImpactString === '3' ? '3' : backendImpactString === '2' ? '2' : '1'; // Default to Moyen if mapping fails

          setFormData({
            nom: fetchedData.name || '',
            type: fetchedData.type || 'Point Faible',
            impact: formattedImpact,
            description: fetchedData.description || '',
            analyseCauses: fetchedData.causes || '',
            recommandations: fetchedData.recommandations || [],
            risques: fetchedData.risques || [],
            piecesJointes: fetchedData.piecesJointes || [],
            responsable: fetchedData.responsable || '',
            auditActivityID: fetchedData.auditActivityID || '',
          });
        } else {
          throw new Error(response.message || 'Failed to fetch constat details');
        }
      } catch (err) {
        if (axios.isCancel(err)) {
          console.log('Constat fetch cancelled:', err.message);
        } else {
          console.error('Error fetching constat details:', err);
          const errorMessage = err.response?.data?.message || err.message || 'An error occurred while fetching constat details';
          setError(errorMessage);
          toast.error(errorMessage);
        }
      } finally {
        setIsLoadingConstat(false);
      }
    };

    fetchConstat();

    return () => {
      abortController.abort();
    };
  }, [constatId]); // Re-run effect if constatId changes

  // Fetch recommendations for the current constat
  useEffect(() => {
    const abortController = new AbortController();
    const signal = abortController.signal;

    const fetchRecommendations = async () => {
      if (!constatId) return;

      setIsLoadingRecommendations(true);
      try {
        const response = await getRecommendationsByConstatId(constatId, signal);
        if (response?.success) {
          setRecommandations(response.data);
        }
      } catch (error) {
        console.error('Error fetching recommendations:', error);
        toast.error('Erreur lors du chargement des recommandations');
      } finally {
        setIsLoadingRecommendations(false);
      }
    };

    fetchRecommendations();

    return () => {
      abortController.abort();
    };
  }, [constatId]);

  // Fetch all recommendations for the "Relier" modal
  const fetchAllRecommendations = async () => {
    setIsLoadingAllRecommendations(true);
    try {
      const response = await getAllRecommendations();
      if (response?.success) {
        // Filter out recommendations that are already linked to this constat
        const filteredRecommendations = response.data.filter(
          rec => !recommandations.some(existingRec => existingRec.id === rec.id)
        );
        setAllRecommendations(filteredRecommendations);
      }
    } catch (error) {
      console.error('Error fetching all recommendations:', error);
      toast.error('Erreur lors du chargement des recommandations');
    } finally {
      setIsLoadingAllRecommendations(false);
    }
  };

  // Handle new recommendation creation
  const handleCreateRecommendation = async () => {
    try {
      const data = {
        name: newRecommendation.name,
        priorite: newRecommendation.priorite,
        constatIds: [constatId]
      };

      const response = await createRecommendation(data);
      if (response.success) {
        setRecommandations(prev => [...prev, response.data]);
        setOpenDialog({ type: null, tab: null });
        setNewRecommendation({ name: '', priorite: 'moyen' });
        toast.success('Recommandation créée avec succès');
      }
    } catch (error) {
      console.error('Error creating recommendation:', error);
      toast.error('Erreur lors de la création de la recommandation');
    }
  };

  // Handle linking existing recommendation
  const handleLinkRecommendation = async (recommendationId) => {
    try {
      const response = await linkRecommendationToConstat(recommendationId, constatId);
      if (response.success) {
        setRecommandations(prev => [...prev, response.data]);
        setOpenDialog({ type: null, tab: null });
        toast.success('Recommandation liée avec succès');
      }
    } catch (error) {
      console.error('Error linking recommendation:', error);
      toast.error('Erreur lors de la liaison de la recommandation');
    }
  };

  // Handle recommendation deletion
  const handleDeleteRecommendation = async (id, e) => {
    e.stopPropagation();
    if (!window.confirm('Êtes-vous sûr de vouloir supprimer cette recommandation ?')) return;

    try {
      const response = await deleteRecommendation(id);
      if (response.success) {
        setRecommandations(prev => prev.filter(rec => rec.id !== id));
        toast.success('Recommandation supprimée avec succès');
      }
    } catch (error) {
      console.error('Error deleting recommendation:', error);
      toast.error('Erreur lors de la suppression de la recommandation');
    }
  };

  // Update handleSave function
  const handleSave = async () => {
    try {
      setIsSavingConstat(true);
      
      // Map the form data to match the backend model
      const constatData = {
        name: formData.nom,
        type: formData.type,
        impact: impactValueToEnum[formData.impact], // Map numeric value to enum string
        description: formData.description,
        causes: formData.analyseCauses,
        auditActivityID: formData.auditActivityID
      };

      const response = await updateConstat(constatId, constatData);
      
      if (response.success) {
        // Update the local state with the response data
        setFormData(prev => ({
          ...prev,
          nom: response.data.name,
          type: response.data.type,
          impact: formData.impact, // Keep the numeric value in the form state
          description: response.data.description,
          analyseCauses: response.data.causes,
          auditActivityID: response.data.auditActivityID
        }));
        toast.success('Caractéristiques sauvegardées avec succès');
      } else {
        throw new Error(response.message || 'Erreur lors de la sauvegarde');
      }
    } catch (error) {
      console.error('Error saving characteristics:', error);
      toast.error(error.message || 'Erreur lors de la sauvegarde des caractéristiques');
    } finally {
      setIsSavingConstat(false);
    }
  };

  // Add new state for edit modal
  const [editRecommendation, setEditRecommendation] = useState(null);

  // Add handleEditRecommendation function
  const handleEditRecommendation = async (e) => {
    e.stopPropagation();
    try {
      const response = await updateRecommendation(editRecommendation.id, {
        name: editRecommendation.name,
        priorite: editRecommendation.priorite
      });
      if (response.success) {
        setRecommandations(prev => 
          prev.map(rec => rec.id === editRecommendation.id ? response.data : rec)
        );
        setEditRecommendation(null);
        toast.success('Recommandation mise à jour avec succès');
      }
    } catch (error) {
      console.error('Error updating recommendation:', error);
      toast.error('Erreur lors de la mise à jour de la recommandation');
    }
  };

  // Update the recommendations table section
  return (
    <div className="space-y-6 py-4">
      {/* Section 1: Caractéristiques */}
      <div className="border rounded-lg shadow-sm">
        <button
          type="button"
          className="w-full flex items-center p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-lg"
          onClick={() => setIsCaracteristiquesOpen((v) => !v)}
        >
          <div className="flex items-center gap-2">
            {isCaracteristiquesOpen ? (
              <ChevronUp className="h-5 w-5 text-blue-600" />
            ) : (
              <ChevronDown className="h-5 w-5 text-blue-600" />
            )}
            <FileText className="h-5 w-5 text-blue-600 mr-1" />
            <span className="text-lg font-medium text-blue-800">Caractéristiques</span>
          </div>
        </button>
        {isCaracteristiquesOpen && (
          <div className="p-5 bg-white space-y-6">
            {/* Row 1: Nom */}
            <div className="w-full space-y-2">
              <Label htmlFor="nom">Nom *</Label>
              <Input id="nom" name="nom" value={formData.nom} onChange={handleInputChange} placeholder="Nom du constat" className="w-full" required disabled={isSavingConstat} />
            </div>
            {/* Row 2: Type de constat, Impact */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="type">Type de constat</Label>
                <Select name="type" value={formData.type} onValueChange={(value) => handleSelectChange("type", value)} disabled={isSavingConstat}>
                  <SelectTrigger id="type" className="w-full">
                    <SelectValue placeholder="Type de constat" />
                  </SelectTrigger>
                  <SelectContent>
                    {typeOptions.map((opt) => (
                      <SelectItem key={opt.value} value={opt.value}>{opt.label}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="impact">Impact</Label>
                <Select name="impact" value={formData.impact} onValueChange={(value) => handleSelectChange("impact", value)} disabled={isSavingConstat}>
                  <SelectTrigger id="impact" className="w-full">
                    <SelectValue placeholder="Impact" />
                  </SelectTrigger>
                  <SelectContent>
                    {impactOptions.map((opt) => (
                      <SelectItem key={opt.value} value={opt.value}>
                        <span className="flex items-center gap-2">
                          <span className={`inline-block w-3 h-3 rounded-full ${impactColors[opt.value]}`}></span>
                          {opt.label}
                        </span>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            {/* Row 3: Activités audit */}
            <div className="w-full space-y-2">
              <Label htmlFor="activiteAudit">Activités audit</Label>
              {formData.auditActivityID ? (
                  <Input id="activiteAudit" value={formData.auditActivityID} disabled />
               ) : (
                  <Input id="activiteAudit" value="N/A" disabled />
               )}
            </div>
            {/* Row 4: Description */}
            <div className="w-full space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea id="description" name="description" value={formData.description} onChange={handleInputChange} placeholder="Description du constat" rows={3} className="w-full" disabled={isSavingConstat} />
            </div>
            {/* Row 5: Analyse des causes */}
            <div className="w-full space-y-2">
              <Label htmlFor="analyseCauses">Analyse des causes</Label>
              <Textarea id="analyseCauses" name="analyseCauses" value={formData.analyseCauses} onChange={handleInputChange} placeholder="Analyse des causes du constat" rows={3} className="w-full" disabled={isSavingConstat} />
            </div>
            {/* Save Button */}
            <div className="flex justify-end">
              <Button className="bg-[#F62D51] hover:bg-[#F62D51]/90 flex items-center" onClick={handleSave} disabled={isSavingConstat || isLoadingConstat || error}>
                {isSavingConstat ? <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Enregistrement...</> : "Enregistrer"}
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Section 2: Recommandation */}
      <div className="border rounded-lg shadow-sm">
        <button
          type="button"
          className="w-full flex items-center p-4 bg-gradient-to-r from-amber-50 to-yellow-50 rounded-t-lg"
          onClick={() => setIsRecommandationOpen((v) => !v)}
        >
          <div className="flex items-center gap-2">
            {isRecommandationOpen ? (
              <ChevronUp className="h-5 w-5 text-yellow-600" />
            ) : (
              <ChevronDown className="h-5 w-5 text-yellow-600" />
            )}
            <ClipboardList className="h-5 w-5 text-yellow-600 mr-1" />
            <span className="text-lg font-medium text-yellow-800">Recommandation</span>
          </div>
        </button>
        {isRecommandationOpen && (
          <div className="p-5 bg-white">
            <div className="flex gap-2 mb-4 justify-end">
              <Button 
                className="bg-[#F62D51] hover:bg-[#F62D51]/90 flex items-center" 
                onClick={() => setOpenDialog({ type: 'nouveau', tab: 'recommandation' })}
              >
                <Plus className="h-4 w-4 mr-2" />Nouveau
              </Button>
              <Button 
                variant="outline" 
                className="flex items-center border-[#F62D51] text-[#F62D51]" 
                onClick={() => {
                  setOpenDialog({ type: 'relier', tab: 'recommandation' });
                  fetchAllRecommendations();
                }}
              >
                <Link className="h-4 w-4 mr-2" />Relier
              </Button>
            </div>
            <Card className="overflow-hidden">
              <CardContent className="p-0">
                <div className="overflow-x-auto">
                  <Table className="min-w-full">
                    <TableHeader>
                      <TableRow className="bg-gray-50 hover:bg-gray-100/50">
                        <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nom</TableHead>
                        <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Priorité</TableHead>
                        <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</TableHead>
                        <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Responsable</TableHead>
                        <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Taux d'avancement</TableHead>
                        <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">En retard</TableHead>
                        <TableHead className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"></TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody className="divide-y divide-gray-200">
                      {isLoadingRecommendations ? (
                        <TableRow>
                          <TableCell colSpan={7} className="px-4 py-10 text-center">
                            <div className="flex items-center justify-center">
                              <Loader2 className="h-6 w-6 animate-spin text-[#F62D51] mr-2" />
                              <span>Chargement des recommandations...</span>
                            </div>
                          </TableCell>
                        </TableRow>
                      ) : recommandations.length > 0 ? (
                        recommandations.map((item) => (
                          <TableRow 
                            key={item.id} 
                            className="hover:bg-gray-50/50 cursor-pointer"
                            onClick={() => handleRecommandationClick(item.id)}
                          >
                            <TableCell className="px-4 py-3 text-sm font-medium text-gray-900 whitespace-nowrap">
                              <div className="flex items-center">
                                <span>{item.name}</span>
                              </div>
                            </TableCell>
                            <TableCell className="px-4 py-3 text-sm whitespace-nowrap">
                              <Badge className={prioriteColors[item.priorite]}>{item.priorite}</Badge>
                            </TableCell>
                            <TableCell className="px-4 py-3 text-sm whitespace-nowrap">
                              <Badge className="bg-blue-100 text-blue-800">En cours</Badge>
                            </TableCell>
                            <TableCell className="px-4 py-3 text-sm whitespace-nowrap">
                              {item.responsable?.username || 'Non assigné'}
                            </TableCell>
                            <TableCell className="px-4 py-3 text-sm whitespace-nowrap">
                              <div className="flex items-center">
                                <div className="w-full bg-gray-200 rounded-full h-2.5 mr-2">
                                  <div className="h-2.5 rounded-full bg-blue-500" style={{ width: '0%' }}></div>
                                </div>
                                <span>0%</span>
                              </div>
                            </TableCell>
                            <TableCell className="px-4 py-3 text-sm whitespace-nowrap">
                              <Badge className="bg-green-100 text-green-800">Non</Badge>
                            </TableCell>
                            <TableCell className="px-4 py-3 text-sm whitespace-nowrap">
                              <div className="flex justify-end gap-2">
                                <Button 
                                  size="sm" 
                                  variant="ghost" 
                                  className="h-8 w-8 p-0"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    setEditRecommendation(item);
                                  }}
                                >
                                  <Edit className="h-4 w-4 text-blue-600" />
                                </Button>
                                <Button 
                                  size="sm" 
                                  variant="ghost" 
                                  className="h-8 w-8 p-0"
                                  onClick={(e) => handleDeleteRecommendation(item.id, e)}
                                >
                                  <Trash2 className="h-4 w-4 text-red-600" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={7} className="px-4 py-10 text-center text-sm text-gray-500">
                            Aucune recommandation
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>

      {/* Section 3: Risque et contrôles */}
      <div className="border rounded-lg shadow-sm">
        <button
          type="button"
          className="w-full flex items-center p-4 bg-gradient-to-r from-rose-50 to-pink-50 rounded-t-lg"
          onClick={() => setIsRisqueControleOpen((v) => !v)}
        >
          <div className="flex items-center gap-2">
            {isRisqueControleOpen ? (
              <ChevronUp className="h-5 w-5 text-rose-600" />
            ) : (
              <ChevronDown className="h-5 w-5 text-rose-600" />
            )}
            <AlertTriangle className="h-5 w-5 text-rose-600 mr-1" />
            <span className="text-lg font-medium text-rose-800">Risque et contrôles</span>
          </div>
        </button>
        {isRisqueControleOpen && (
          <div className="p-5 bg-white space-y-4">
            {/* Tabs */}
            <div className="flex border-b mb-4">
              <button
                className={`px-4 py-2 font-medium ${risqueControleTab === 'risque' ? 'text-[#F62D51] border-b-2 border-[#F62D51]' : 'text-gray-500'}`}
                onClick={() => setRisqueControleTab('risque')}
              >
                <div className="flex items-center"><AlertTriangle className="h-4 w-4 mr-2" />Risque</div>
              </button>
              <button
                className={`px-4 py-2 font-medium ${risqueControleTab === 'controle' ? 'text-[#F62D51] border-b-2 border-[#F62D51]' : 'text-gray-500'}`}
                onClick={() => setRisqueControleTab('controle')}
              >
                <div className="flex items-center"><ShieldCheck className="h-4 w-4 mr-2" />Contrôle</div>
              </button>
            </div>
            {/* Tab content */}
            {risqueControleTab === 'risque' ? (
              <>
                <div className="flex gap-2 mb-4 justify-end">
                  <Button className="bg-[#F62D51] hover:bg-[#F62D51]/90 flex items-center" onClick={() => setOpenDialog({ type: 'nouveau', tab: 'risque' })}><Plus className="h-4 w-4 mr-2" />Nouveau</Button>
                  <Button variant="outline" className="flex items-center border-[#F62D51] text-[#F62D51]" onClick={() => setOpenDialog({ type: 'relier', tab: 'risque' })}><Link className="h-4 w-4 mr-2" />Relier</Button>
                </div>
                <Card className="overflow-hidden">
                  <CardContent className="p-0">
                    <div className="overflow-x-auto">
                      <Table className="min-w-full">
                        <TableHeader>
                          <TableRow className="bg-gray-50 hover:bg-gray-100/50">
                            <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nom</TableHead>
                            <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</TableHead>
                            <TableHead className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Majeur</TableHead>
                            <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Entité</TableHead>
                            <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Dernière évaluation</TableHead>
                            <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Risque inhérent</TableHead>
                            <TableHead className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"></TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody className="divide-y divide-gray-200">
                          {risques.length > 0 ? (
                            risques.map((item) => (
                              <TableRow key={item.id} className="hover:bg-gray-50/50 cursor-pointer">
                                <TableCell className="px-4 py-3 text-sm font-medium text-gray-900 whitespace-nowrap">{item.nom}</TableCell>
                                <TableCell className="px-4 py-3 text-sm whitespace-nowrap">{item.code || 'RISK-' + item.id}</TableCell>
                                <TableCell className="px-4 py-3 text-center text-sm whitespace-nowrap">
                                  <input type="checkbox" checked={item.majeur || false} readOnly className="accent-[#F62D51]" />
                                </TableCell>
                                <TableCell className="px-4 py-3 text-sm whitespace-nowrap">{item.entite || 'Entité ' + item.id}</TableCell>
                                <TableCell className="px-4 py-3 text-sm whitespace-nowrap">{item.derniereEvaluation || '2024-07-31'}</TableCell>
                                <TableCell className="px-4 py-3 text-sm whitespace-nowrap">{item.risqueInherent || 'Élevé'}</TableCell>
                                <TableCell className="px-4 py-3 text-sm whitespace-nowrap">
                                  <div className="flex justify-end gap-2">
                                    <Button size="sm" variant="ghost" className="h-8 w-8 p-0"><Edit className="h-4 w-4 text-blue-600" /></Button>
                                    <Button size="sm" variant="ghost" className="h-8 w-8 p-0"><Trash2 className="h-4 w-4 text-red-600" /></Button>
                                  </div>
                                </TableCell>
                              </TableRow>
                            ))
                          ) : (
                            <TableRow>
                              <TableCell colSpan={6} className="px-4 py-10 text-center text-sm text-gray-500">Aucun risque</TableCell>
                            </TableRow>
                          )}
                        </TableBody>
                      </Table>
                    </div>
                  </CardContent>
                </Card>
              </>
            ) : (
              <>
                <div className="flex gap-2 mb-4 justify-end">
                  <Button className="bg-[#F62D51] hover:bg-[#F62D51]/90 flex items-center" onClick={() => setOpenDialog({ type: 'nouveau', tab: 'controle' })}><Plus className="h-4 w-4 mr-2" />Nouveau</Button>
                  <Button variant="outline" className="flex items-center border-[#F62D51] text-[#F62D51]" onClick={() => setOpenDialog({ type: 'relier', tab: 'controle' })}><Link className="h-4 w-4 mr-2" />Relier</Button>
                </div>
                <Card className="overflow-hidden">
                  <CardContent className="p-0">
                    <div className="overflow-x-auto">
                      <Table className="min-w-full">
                        <TableHeader>
                          <TableRow className="bg-gray-50 hover:bg-gray-100/50">
                            <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nom</TableHead>
                            <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</TableHead>
                            <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</TableHead>
                            <TableHead className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"></TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody className="divide-y divide-gray-200">
                          {controles.length > 0 ? (
                            controles.map((item) => (
                              <TableRow key={item.id} className="hover:bg-gray-50/50 cursor-pointer">
                                <TableCell className="px-4 py-3 text-sm font-medium text-gray-900 whitespace-nowrap">{item.nom}</TableCell>
                                <TableCell className="px-4 py-3 text-sm whitespace-nowrap">{item.type}</TableCell>
                                <TableCell className="px-4 py-3 text-sm whitespace-nowrap">{item.statut}</TableCell>
                                <TableCell className="px-4 py-3 text-sm whitespace-nowrap">
                                  <div className="flex justify-end gap-2">
                                    <Button size="sm" variant="ghost" className="h-8 w-8 p-0"><Edit className="h-4 w-4 text-blue-600" /></Button>
                                    <Button size="sm" variant="ghost" className="h-8 w-8 p-0"><Trash2 className="h-4 w-4 text-red-600" /></Button>
                                  </div>
                                </TableCell>
                              </TableRow>
                            ))
                          ) : (
                            <TableRow>
                              <TableCell colSpan={3} className="px-4 py-10 text-center text-sm text-gray-500">Aucun contrôle</TableCell>
                            </TableRow>
                          )}
                        </TableBody>
                      </Table>
                    </div>
                  </CardContent>
                </Card>
              </>
            )}
          </div>
        )}
      </div>

      {/* Section Pièces jointes */}
      <div className="border rounded-lg shadow-sm mt-6">
        <button
          type="button"
          className="w-full flex items-center p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-lg"
          onClick={() => setIsAttachmentsOpen((v) => !v)}
        >
          <div className="flex items-center gap-2">
            <Paperclip className="h-5 w-5 text-gray-600 mr-1" />
            <span className="text-lg font-medium text-gray-800">Pièces jointes</span>
          </div>
        </button>
        {isAttachmentsOpen && (
          <div className="p-5 bg-white">
            <div className="flex justify-end mb-4">
              <input
                type="file"
                id="attachment-upload"
                multiple
                className="hidden"
                onChange={handleFileUpload}
                disabled={isUploading}
              />
              <label htmlFor="attachment-upload">
                <Button
                  type="button"
                  variant="outline"
                  className="flex items-center gap-2 cursor-pointer"
                  disabled={isUploading}
                  asChild
                >
                  <span>
                    {isUploading ? (
                      <>
                        <Upload className="h-4 w-4 animate-spin" />
                        Téléchargement...
                      </>
                    ) : (
                      <>
                        <Upload className="h-4 w-4" />
                        Ajouter une pièce jointe
                      </>
                    )}
                  </span>
                </Button>
              </label>
            </div>
            {attachments.length === 0 ? (
              <div className="text-center py-8 border-2 border-dashed rounded-lg border-gray-300">
                <Paperclip className="h-12 w-12 mx-auto text-gray-300 mb-2" />
                <p className="text-gray-500">Aucune pièce jointe ajoutée</p>
                <p className="text-sm text-gray-400">Glissez-déposez des fichiers ici ou cliquez sur le bouton Ajouter une pièce jointe</p>
                <p className="text-xs text-gray-400 mt-2">Taille max: 50MB. Types autorisés: PDF, Office, images, archives.</p>
              </div>
            ) : (
              <div className="border rounded-lg overflow-hidden">
                <table className="w-full relative">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">Nom</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">Taille</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">Date</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {attachments.map((doc) => (
                      <tr key={doc.id} className="hover:bg-gray-50">
                        <td className="px-4 py-3 text-sm">
                          <div className="flex items-center">
                            <Paperclip className="h-4 w-4 mr-2 text-blue-500" />
                            {doc.name}
                          </div>
                        </td>
                        <td className="px-4 py-3 text-sm">{formatFileSize(doc.size)}</td>
                        <td className="px-4 py-3 text-sm">{formatDate(doc.date)}</td>
                        <td className="px-4 py-3 text-sm">
                          <div className="flex items-center space-x-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0 text-red-500 hover:text-red-700 hover:bg-red-50"
                              onClick={() => handleDeleteAttachment(doc.id)}
                              title="Supprimer"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Update the dialog component */}
      <Dialog open={!!openDialog.type} onOpenChange={() => {
        setOpenDialog({ type: null, tab: null });
        if (openDialog.type === 'relier') {
          fetchAllRecommendations();
        }
      }}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {openDialog.type === 'nouveau' ? 'Nouvelle recommandation' : 'Lier une recommandation'}
            </DialogTitle>
          </DialogHeader>
          {openDialog.type === 'nouveau' && openDialog.tab === 'recommandation' ? (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Nom *</Label>
                <Input
                  id="name"
                  value={newRecommendation.name}
                  onChange={(e) => setNewRecommendation(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Nom de la recommandation"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="priorite">Priorité</Label>
                <Select
                  value={newRecommendation.priorite}
                  onValueChange={(value) => setNewRecommendation(prev => ({ ...prev, priorite: value }))}
                >
                  <SelectTrigger id="priorite">
                    <SelectValue placeholder="Sélectionner une priorité" />
                  </SelectTrigger>
                  <SelectContent>
                    {prioriteOptions.map((opt) => (
                      <SelectItem key={opt.value} value={opt.value}>
                        <span className="flex items-center gap-2">
                          <span className={`inline-block w-3 h-3 rounded-full ${prioriteModalColors[opt.value]}`}></span>
                          {opt.label}
                        </span>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setOpenDialog({ type: null, tab: null })}>Annuler</Button>
                <Button 
                  className="bg-[#F62D51] hover:bg-[#F62D51]/90"
                  onClick={handleCreateRecommendation}
                  disabled={!newRecommendation.name}
                >
                  Créer
                </Button>
              </DialogFooter>
            </div>
          ) : openDialog.type === 'relier' && openDialog.tab === 'recommandation' ? (
            <div className="space-y-4">
              <div className="max-h-[400px] overflow-y-auto">
                {isLoadingAllRecommendations ? (
                  <div className="flex items-center justify-center py-4">
                    <Loader2 className="h-6 w-6 animate-spin text-[#F62D51]" />
                  </div>
                ) : allRecommendations.length > 0 ? (
                  <div className="space-y-2">
                    {allRecommendations.map((rec) => (
                      <div
                        key={rec.id}
                        className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 cursor-pointer"
                        onClick={() => handleLinkRecommendation(rec.id)}
                      >
                        <div>
                          <p className="font-medium">{rec.name}</p>
                          <Badge className={prioriteColors[rec.priorite]}>{rec.priorite}</Badge>
                        </div>
                        <Button variant="ghost" size="sm">
                          <Link className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-center text-gray-500 py-4">Aucune recommandation disponible</p>
                )}
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setOpenDialog({ type: null, tab: null })}>Fermer</Button>
              </DialogFooter>
            </div>
          ) : null}
        </DialogContent>
      </Dialog>

      {/* Add edit modal dialog */}
      <Dialog open={!!editRecommendation} onOpenChange={() => setEditRecommendation(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Modifier la recommandation</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="edit-name">Nom *</Label>
              <Input
                id="edit-name"
                value={editRecommendation?.name || ''}
                onChange={(e) => setEditRecommendation(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Nom de la recommandation"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-priorite">Priorité</Label>
              <Select
                value={editRecommendation?.priorite || 'moyen'}
                onValueChange={(value) => setEditRecommendation(prev => ({ ...prev, priorite: value }))}
              >
                <SelectTrigger id="edit-priorite">
                  <SelectValue placeholder="Sélectionner une priorité" />
                </SelectTrigger>
                <SelectContent>
                  {prioriteOptions.map((opt) => (
                    <SelectItem key={opt.value} value={opt.value}>
                      <span className="flex items-center gap-2">
                        <span className={`inline-block w-3 h-3 rounded-full ${prioriteModalColors[opt.value]}`}></span>
                        {opt.label}
                      </span>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setEditRecommendation(null)}>Annuler</Button>
              <Button 
                className="bg-[#F62D51] hover:bg-[#F62D51]/90"
                onClick={handleEditRecommendation}
                disabled={!editRecommendation?.name}
              >
                Enregistrer
              </Button>
            </DialogFooter>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default CaracteristiquesConstatTab;