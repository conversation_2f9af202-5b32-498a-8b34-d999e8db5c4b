import { useState, useEffect, useRef } from 'react';
import { AlignJustify, LogOut, Settings, User, HelpCircle, Bell, Search, X, Eye, Check, Trash2 } from "lucide-react";
import { But<PERSON> } from "../ui/button";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { logoutUser } from "@/store/auth-slice";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { formatDistanceToNow, parseISO } from 'date-fns';
import { useTranslation } from 'react-i18next';
import LanguageSwitcher from "../ui/language-switcher";

function AuditHeader({ setOpen, notifications, unreadCount, markAsRead, markAllAsRead }) {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isNotificationOpen, setIsNotificationOpen] = useState(false);
  const [visibleNotifications, setVisibleNotifications] = useState(5);
  const notificationPopoverRef = useRef(null);

  function handleLogout() {
    dispatch(logoutUser());
  }

  const handleNotificationClick = (notification) => {
    markAsRead(notification.notificationId);
    setIsNotificationOpen(false);

    if (notification.type === 'incident') {
      navigate(`/audit/incident/edit/${notification.id}`);
    } else if (notification.type === 'risk') {
      navigate(`/audit/risks/edit/${notification.id}`);
    }
  };

  const loadMoreNotifications = () => {
    setVisibleNotifications(prev => prev + 5);
  };

  // Handle clicking outside of the notification popover to close it
  useEffect(() => {
    function handleClickOutside(event) {
      if (notificationPopoverRef.current && !notificationPopoverRef.current.contains(event.target)) {
        setIsNotificationOpen(false);
      }
    }

    if (isNotificationOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isNotificationOpen]);

  return (
    <header className="h-[61px] flex items-center justify-between px-4 py-3 bg-white border-b shadow-sm">
      {/* Hamburger button with red background on mobile */}
      <Button
        onClick={() => setOpen(true)}
        className="lg:hidden sm:block bg-red-500 text-white hover:bg-red-600"
      >
        <AlignJustify />
        <span className="sr-only">Toggle Menu</span>
      </Button>
      <div className="flex flex-1 justify-end items-center gap-4">
        {/* Search Container */}
        <div className="flex items-center gap-2">
          <div className={`relative transition-all duration-300 ${isSearchOpen ? 'w-48' : 'w-0 overflow-hidden'}`}>
            <input
              type="text"
              placeholder={t('common.header.search')}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full px-3 py-1 border-b-2 border-[#242A33] focus:border-[#F62D51] outline-none transition-colors"
            />
            <button
              onClick={() => {
                setSearchQuery('');
                setIsSearchOpen(false);
              }}
              className="absolute right-2 top-1/2 -translate-y-1/2 text-[#555F6D] hover:text-[#F62D51]"
            >
              <X size={16} />
            </button>
          </div>
          <button
            onClick={() => setIsSearchOpen(!isSearchOpen)}
            className="p-2 text-[#555F6D] hover:text-[#F62D51] transition-colors"
          >
            <Search size={24} />
          </button>
        </div>

        {/* Language Switcher */}
        <LanguageSwitcher />

        {/* Notification Icon */}
        <Popover open={isNotificationOpen} onOpenChange={setIsNotificationOpen}>
          <PopoverTrigger asChild>
            <button className="relative p-2 text-[#555F6D] hover:text-[#F62D51] transition-colors">
              <Bell size={24} />
              {unreadCount > 0 && (
                <span className="absolute top-0 right-0 bg-red-500 text-white text-xs rounded-full min-w-[16px] h-4 flex items-center justify-center">
                  {unreadCount > 99 ? '99+' : unreadCount}
                </span>
              )}
            </button>
          </PopoverTrigger>
          <PopoverContent ref={notificationPopoverRef} className="w-80 bg-white shadow-lg rounded-md p-0 text-sm text-gray-700">
            <div className="p-3 border-b border-gray-200 flex justify-between items-center">
              <h3 className="font-semibold text-gray-800">{t('common.header.notifications')}</h3>
              {notifications && notifications.some(n => !n.read) && (
                <Button variant="link" size="sm" onClick={markAllAsRead} className="text-xs text-[#F62D51] hover:text-red-700 p-0 h-auto">
                  {t('common.header.mark_all_read')}
                </Button>
              )}
            </div>
            {notifications && notifications.length > 0 ? (
              <div className="max-h-[calc(100vh-200px)] overflow-y-auto">
                {notifications.slice(0, visibleNotifications).map((notification) => (
                  <div
                    key={notification.notificationId}
                    className={`p-3 border-b border-gray-100 hover:bg-gray-50 transition-colors relative ${!notification.read ? 'bg-red-50' : ''}`}
                  >
                    <div className="flex justify-between items-start gap-2">
                      <div className="flex-1">
                        <p
                          onClick={() => handleNotificationClick(notification)}
                          className={`font-medium ${!notification.read ? 'text-gray-800' : 'text-gray-600'} cursor-pointer`}
                        >
                          {notification.message}
                        </p>
                        <p className="text-xs text-gray-500 mt-1 flex items-center">
                          {notification.assignedBy} · {formatDistanceToNow(parseISO(notification.receivedAt), { addSuffix: true })}
                        </p>
                      </div>
                      <div className="flex gap-1">
                        {!notification.read && (
                          <button
                            onClick={() => markAsRead(notification.notificationId)}
                            className="p-1 text-green-500 hover:bg-green-50 rounded-full transition-colors"
                            title={t('common.header.mark_all_read')}
                          >
                            <Check size={14} />
                          </button>
                        )}
                      </div>
                    </div>
                    {!notification.read && (
                      <div className="absolute left-0 top-0 bottom-0 w-1 bg-red-500"></div>
                    )}
                  </div>
                ))}
                {notifications.length > visibleNotifications && (
                  <Button variant="link" onClick={loadMoreNotifications} className="w-full text-center py-2 text-[#F62D51] hover:text-red-700">
                    {t('common.header.show_more')} ({notifications.length - visibleNotifications} {t('common.header.more')})
                  </Button>
                )}
              </div>
            ) : (
              <p className="p-4 text-center text-gray-500">{t('common.header.no_notifications')}</p>
            )}
          </PopoverContent>
        </Popover>

        {/* Settings Icon */}
        <Popover>
          <PopoverTrigger asChild>
            <button className="p-2 text-[#555F6D] hover:text-[#F62D51] transition-colors">
              <Settings size={24} />
            </button>
          </PopoverTrigger>
          <PopoverContent className="w-48 bg-white shadow-md rounded-md p-2">
            <div className="flex flex-col">
              <button
                onClick={() => navigate('/audit/profile')}
                className="flex items-center gap-2 px-3 py-2 text-[#555F6D] hover:bg-gray-100 hover:rounded-md transition-all"
              >
                <User size={18} /> {t('common.header.check_profile')}
              </button>
              <button className="flex items-center gap-2 px-3 py-2 text-[#555F6D] hover:bg-gray-100 hover:rounded-md transition-all">
                <HelpCircle size={18} /> {t('common.header.help')}
              </button>
              <button onClick={handleLogout} className="flex items-center gap-2 px-3 py-2 text-[#F62D51] hover:bg-gray-100 hover:rounded-md transition-all">
                <LogOut size={18} /> {t('common.header.logout')}
              </button>
            </div>
          </PopoverContent>
        </Popover>
      </div>
    </header>
  );
}

export default AuditHeader;
