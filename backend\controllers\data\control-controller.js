const db = require('../../models');
const { Control, sequelize } = db;

// Get all controls
const getAllControls = async (req, res) => {
  try {
    const controls = await Control.findAll({
      include: [
        { model: sequelize.models.ControlType, as: 'controlTypeRef' },
        { model: sequelize.models.Risk, as: 'riskRef' }
      ]
    });
    res.json({
      success: true,
      data: controls
    });
  } catch (error) {
    console.error('Error fetching controls:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch controls'
    });
  }
};

// Create new control
const createControl = async (req, res) => {
  try {
    const {
      controlID,
      name,
      code,
      controlKey,
      controlExecutionMethod,
      objective,
      executionProcedure,
      operationalCost,
      organizationalLevel,
      sampleType,
      testingFrequency,
      testingMethod,
      testingPopulationSize,
      testingProcedure,
      implementingActionPlan,
      businessProcess,
      organizationalProcess,
      operation,
      application,
      entity,
      controlType,
      risk,
      comment,
      designQuality,
      effectivenessLevel
    } = req.body;

    // Validate required fields
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields',
        missingFields: ['name'].filter(field => !req.body[field])
      });
    }

    // Validate foreign keys before creating
    // IMPORTANT: Always set operation to null to avoid foreign key constraint errors
    let validatedOperation = null;
    console.log('Operation from request:', operation);

    // Always log all operations for debugging
    try {
      const allOperations = await sequelize.models.Operation.findAll({
        attributes: ['operationID', 'name']
      });
      console.log('All operations in database:', allOperations.map(op => op.get({ plain: true })));
    } catch (error) {
      console.error('Error fetching all operations:', error);
    }

    // Even if operation is provided, always set it to null
    if (operation) {
      console.log(`Operation ${operation} provided, but setting to null to avoid foreign key errors`);
    }
    validatedOperation = null;

    let validatedBusinessProcess = null;
    if (businessProcess) {
      const businessProcessExists = await sequelize.models.BusinessProcess.findOne({
        where: { businessProcessID: businessProcess }
      });
      validatedBusinessProcess = businessProcessExists ? businessProcess : null;
    }

    let validatedOrgProcess = null;
    if (organizationalProcess) {
      const orgProcessExists = await sequelize.models.OrganizationalProcess.findOne({
        where: { organizationalProcessID: organizationalProcess }
      });
      validatedOrgProcess = orgProcessExists ? organizationalProcess : null;
    }

    let validatedApplication = null;
    if (application) {
      const applicationExists = await sequelize.models.Application.findOne({
        where: { applicationID: application }
      });
      validatedApplication = applicationExists ? application : null;
    }

    let validatedEntity = null;
    if (entity) {
      const entityExists = await sequelize.models.Entity.findOne({
        where: { entityID: entity }
      });
      validatedEntity = entityExists ? entity : null;
    }

    let validatedControlType = null;
    if (controlType) {
      const controlTypeExists = await sequelize.models.ControlType.findOne({
        where: { controlTypeID: controlType }
      });
      validatedControlType = controlTypeExists ? controlType : null;
    }

    let validatedRisk = null;
    if (risk) {
      const riskExists = await sequelize.models.Risk.findOne({
        where: { riskID: risk }
      });
      validatedRisk = riskExists ? risk : null;
    }

    const control = await Control.create({
      controlID: controlID || `CTRL_${Date.now()}`,
      name,
      code: code || null,
      controlKey: controlKey || null,
      controlExecutionMethod: controlExecutionMethod || null,
      objective: objective || null,
      executionProcedure: executionProcedure || null,
      operationalCost: operationalCost || null,
      organizationalLevel: organizationalLevel || null,
      sampleType: sampleType || null,
      testingFrequency: testingFrequency || null,
      testingMethod: testingMethod || null,
      testingPopulationSize: testingPopulationSize || null,
      testingProcedure: testingProcedure || null,
      implementingActionPlan: implementingActionPlan || null,
      designQuality: designQuality || null,
      effectivenessLevel: effectivenessLevel || null,
      businessProcess: validatedBusinessProcess,
      organizationalProcess: validatedOrgProcess,
      operation: validatedOperation,
      application: validatedApplication,
      entity: validatedEntity,
      controlType: validatedControlType,
      risk: validatedRisk,
      comment: comment || null
    });

    return res.status(201).json({
      success: true,
      message: 'Control created successfully',
      data: control
    });
  } catch (error) {
    console.error('Error creating control:', error);

    // Check if it's a foreign key constraint error
    if (error.name === 'SequelizeForeignKeyConstraintError') {
      const constraintName = error.index || '';
      const fieldName = constraintName.replace('fk_', '');
      const fieldValue = error.parent?.parameters?.[0] || 'unknown';

      return res.status(400).json({
        success: false,
        message: `Foreign key constraint error: The ${fieldName} with ID ${fieldValue} does not exist.`,
        error: {
          name: error.name,
          field: fieldName,
          value: fieldValue,
          constraint: constraintName
        }
      });
    }

    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to create control',
      error: error.name
    });
  }
};

// Get control by ID
const getControlById = async (req, res) => {
  try {
    const { id } = req.params;
    const control = await Control.findOne({
      where: { controlID: id },
      include: [
        { model: sequelize.models.ControlType, as: 'controlTypeRef' },
        { model: sequelize.models.Risk, as: 'riskRef' },
        { model: sequelize.models.BusinessProcess, as: 'businessProcessRef' },
        { model: sequelize.models.OrganizationalProcess, as: 'organizationalProcessRef' },
        { model: sequelize.models.Operation, as: 'operationRef' },
        { model: sequelize.models.Application, as: 'applicationRef' },
        { model: sequelize.models.Entity, as: 'entityRef' }
      ]
    });

    if (!control) {
      return res.status(404).json({
        success: false,
        message: 'Control not found'
      });
    }

    // Convert to plain object to modify it
    const controlData = control.get({ plain: true });

    // Add reference data for easier access in the frontend
    if (controlData.businessProcessRef) {
      controlData.businessProcessName = controlData.businessProcessRef.name;
    }
    if (controlData.organizationalProcessRef) {
      controlData.organizationalProcessName = controlData.organizationalProcessRef.name;
    }
    if (controlData.operationRef) {
      controlData.operationName = controlData.operationRef.name;
    }
    if (controlData.applicationRef) {
      controlData.applicationName = controlData.applicationRef.name;
    }
    if (controlData.entityRef) {
      controlData.entityName = controlData.entityRef.name;
    }
    if (controlData.controlTypeRef) {
      controlData.controlTypeName = controlData.controlTypeRef.name;
    }
    if (controlData.riskRef) {
      controlData.riskName = controlData.riskRef.name;
    }

    // Log the control data for debugging
    console.log('Control data being sent to frontend:', controlData);

    res.json({
      success: true,
      data: controlData
    });
  } catch (error) {
    console.error('Error fetching control:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch control'
    });
  }
};

// Update control
const updateControl = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      code,
      controlKey,
      controlExecutionMethod,
      objective,
      executionProcedure,
      operationalCost,
      organizationalLevel,
      sampleType,
      testingFrequency,
      testingMethod,
      testingPopulationSize,
      testingProcedure,
      implementingActionPlan,
      businessProcess,
      organizationalProcess,
      operation,
      application,
      entity,
      controlType,
      risk,
      comment,
      designQuality,
      effectivenessLevel
    } = req.body;

    const control = await Control.findOne({
      where: { controlID: id }
    });

    if (!control) {
      return res.status(404).json({
        success: false,
        message: 'Control not found'
      });
    }

    // Validate foreign keys before updating
    let validatedOperation = null;

    // Check if operation is a valid operation ID
    if (operation !== undefined) {
      if (operation === null || operation === 'none' || operation === '') {
        // If operation is explicitly set to null/none/empty, keep it null
        validatedOperation = null;
        console.log('Operation explicitly set to null/none/empty');
      } else {
        // Check if the operation exists in the database
        try {
          console.log('Checking operation existence:', {
            operationID: operation,
            type: typeof operation
          });

          // First, let's log all operations in the database
          const allOperations = await sequelize.models.Operation.findAll({
            attributes: ['operationID']
          });
          console.log('All operations in database:', allOperations.map(op => op.operationID));

          const operationExists = await sequelize.models.Operation.findOne({
            where: { operationID: operation }
          });

          console.log('Operation lookup result:', {
            operationID: operation,
            exists: !!operationExists,
            foundOperation: operationExists ? operationExists.operationID : null
          });

          if (operationExists) {
            validatedOperation = operation;
            console.log(`Operation ${operation} exists, setting to: ${validatedOperation}`);
          } else {
            // If operation doesn't exist, keep the existing value
            validatedOperation = control.operation;
            console.log(`Operation ${operation} does not exist, keeping existing value: ${control.operation}`);

            // Return a more descriptive error message
            return res.status(400).json({
              success: false,
              message: `The operation with ID ${operation} does not exist in the database.`,
              error: {
                name: 'InvalidOperationError',
                field: 'operation',
                value: operation,
                availableOperations: allOperations.map(op => op.operationID)
              }
            });
          }
        } catch (error) {
          console.error('Error validating operation:', error);
          validatedOperation = control.operation;
        }
      }
    } else {
      // If operation is not provided in the request, keep the existing value
      validatedOperation = control.operation;
      console.log(`Operation not provided in request, keeping existing value: ${control.operation}`);
    }

    // Log the operation value before update
    console.log('Operation value before update:', {
      provided: operation,
      validated: validatedOperation,
      current: control.operation
    });

    let validatedBusinessProcess = null;
    if (businessProcess) {
      const businessProcessExists = await sequelize.models.BusinessProcess.findOne({
        where: { businessProcessID: businessProcess }
      });
      validatedBusinessProcess = businessProcessExists ? businessProcess : null;
    } else {
      validatedBusinessProcess = control.businessProcess;
    }

    let validatedOrgProcess = null;
    if (organizationalProcess) {
      const orgProcessExists = await sequelize.models.OrganizationalProcess.findOne({
        where: { organizationalProcessID: organizationalProcess }
      });
      validatedOrgProcess = orgProcessExists ? organizationalProcess : null;
    } else {
      validatedOrgProcess = control.organizationalProcess;
    }

    let validatedApplication = null;
    if (application) {
      const applicationExists = await sequelize.models.Application.findOne({
        where: { applicationID: application }
      });
      validatedApplication = applicationExists ? application : null;
    } else {
      validatedApplication = control.application;
    }

    let validatedEntity = null;
    if (entity) {
      const entityExists = await sequelize.models.Entity.findOne({
        where: { entityID: entity }
      });
      validatedEntity = entityExists ? entity : null;
    } else {
      validatedEntity = control.entity;
    }

    let validatedControlType = null;
    if (controlType) {
      const controlTypeExists = await sequelize.models.ControlType.findOne({
        where: { controlTypeID: controlType }
      });
      validatedControlType = controlTypeExists ? controlType : null;
    } else {
      validatedControlType = control.controlType;
    }

    let validatedRisk = null;
    if (risk) {
      const riskExists = await sequelize.models.Risk.findOne({
        where: { riskID: risk }
      });
      validatedRisk = riskExists ? risk : null;
    } else {
      validatedRisk = control.risk;
    }

    // Log the validated fields
    console.log('Validated fields before update:', {
      businessProcess: validatedBusinessProcess,
      organizationalProcess: validatedOrgProcess,
      operation: validatedOperation,
      application: validatedApplication,
      entity: validatedEntity,
      controlType: validatedControlType,
      risk: validatedRisk
    });

    // Update fields
    await control.update({
      name: name || control.name,
      code: code !== undefined ? code : control.code,
      controlKey: controlKey !== undefined ? controlKey : control.controlKey,
      controlExecutionMethod: controlExecutionMethod !== undefined ? controlExecutionMethod : control.controlExecutionMethod,
      objective: objective !== undefined ? objective : control.objective,
      executionProcedure: executionProcedure !== undefined ? executionProcedure : control.executionProcedure,
      operationalCost: operationalCost !== undefined ? operationalCost : control.operationalCost,
      organizationalLevel: organizationalLevel !== undefined ? organizationalLevel : control.organizationalLevel,
      sampleType: sampleType !== undefined ? sampleType : control.sampleType,
      testingFrequency: testingFrequency !== undefined ? testingFrequency : control.testingFrequency,
      testingMethod: testingMethod !== undefined ? testingMethod : control.testingMethod,
      testingPopulationSize: testingPopulationSize !== undefined ? testingPopulationSize : control.testingPopulationSize,
      testingProcedure: testingProcedure !== undefined ? testingProcedure : control.testingProcedure,
      implementingActionPlan: implementingActionPlan !== undefined ? implementingActionPlan : control.implementingActionPlan,
      designQuality: designQuality !== undefined ? designQuality : control.designQuality,
      effectivenessLevel: effectivenessLevel !== undefined ? effectivenessLevel : control.effectivenessLevel,
      businessProcess: validatedBusinessProcess,
      organizationalProcess: validatedOrgProcess,
      operation: validatedOperation,
      application: validatedApplication,
      entity: validatedEntity,
      controlType: validatedControlType,
      risk: validatedRisk,
      comment: comment !== undefined ? comment : control.comment
    });

    res.json({
      success: true,
      message: 'Control updated successfully',
      data: control
    });
  } catch (error) {
    console.error('Error updating control:', error);

    // Check if it's a foreign key constraint error
    if (error.name === 'SequelizeForeignKeyConstraintError') {
      const constraintName = error.index || '';
      const fieldName = constraintName.replace('fk_', '');
      const fieldValue = error.parent?.parameters?.[0] || 'unknown';

      return res.status(400).json({
        success: false,
        message: `Foreign key constraint error: The ${fieldName} with ID ${fieldValue} does not exist.`,
        error: {
          name: error.name,
          field: fieldName,
          value: fieldValue,
          constraint: constraintName
        }
      });
    }

    res.status(500).json({
      success: false,
      message: error.message || 'Failed to update control',
      error: error.name
    });
  }
};

// Delete control
const deleteControl = async (req, res) => {
  try {
    const { id } = req.params;
    const control = await Control.findOne({
      where: { controlID: id }
    });

    if (!control) {
      return res.status(404).json({
        success: false,
        message: 'Control not found'
      });
    }

    await control.destroy();

    res.json({
      success: true,
      message: 'Control deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting control:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete control'
    });
  }
};

module.exports = {
  getAllControls,
  createControl,
  getControlById,
  updateControl,
  deleteControl
};
