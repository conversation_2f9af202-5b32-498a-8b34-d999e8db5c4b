const db = require('../../models');
const { ActionPlan, User } = db;

// Get all action plans
const getAllActionPlans = async (req, res) => {
  try {
    // Include user information
    const actionPlans = await ActionPlan.findAll({
      include: [
        {
          model: User,
          as: 'approver',
          attributes: ['id', 'username', 'email'],
          include: [
            {
              model: db.Role,
              as: 'roles',
              through: { attributes: [] },
              attributes: ['id', 'name', 'code']
            }
          ]
        },
        {
          model: User,
          as: 'assignee',
          attributes: ['id', 'username', 'email'],
          include: [
            {
              model: db.Role,
              as: 'roles',
              through: { attributes: [] },
              attributes: ['id', 'name', 'code']
            }
          ]
        }
      ]
    });
    res.json({
      success: true,
      data: actionPlans
    });
  } catch (error) {
    console.error('Error fetching action plans:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch action plans',
      error: error.message
    });
  }
};

// Create new action plan
const createActionPlan = async (req, res) => {
  try {
    const {
      actionPlanID,
      name,
      category,
      nature,
      origin,
      priority,
      organizationalLevel,
      means,
      comment,
      plannedBeginDate,
      plannedEndDate,
      approverId,
      assigneeId,
      controls,
      incidents,
      products,
      businessLines,
      risks,
      organizationalProcesses,
      businessProcesses,
      operations,
      entities
    } = req.body;

    // Validate required fields
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields',
        missingFields: ['name'].filter(field => !req.body[field])
      });
    }

    const actionPlan = await ActionPlan.create({
      actionPlanID: actionPlanID || `AP_${Date.now()}`,
      name,
      category: category || null,
      nature: nature || null,
      origin: origin || null,
      priority: priority || null,
      organizationalLevel: organizationalLevel || null,
      means: means || null,
      comment: comment || null,
      plannedBeginDate: plannedBeginDate || null,
      plannedEndDate: plannedEndDate || null,
      approverId: approverId || null,
      assigneeId: assigneeId || null
    });

    // Handle many-to-many relationships if provided
    if (controls && controls.length > 0) {
      await actionPlan.setControls(controls);
    }

    if (incidents && incidents.length > 0) {
      await actionPlan.setIncidents(incidents);
    }

    if (products && products.length > 0) {
      await actionPlan.setProducts(products);
    }

    if (businessLines && businessLines.length > 0) {
      await actionPlan.setBusinessLines(businessLines);
    }

    if (risks && risks.length > 0) {
      await actionPlan.setRisks(risks);
    }

    if (organizationalProcesses && organizationalProcesses.length > 0) {
      await actionPlan.setOrganizationalProcesses(organizationalProcesses);
    }

    if (businessProcesses && businessProcesses.length > 0) {
      await actionPlan.setBusinessProcesses(businessProcesses);
    }

    if (operations && operations.length > 0) {
      await actionPlan.setOperations(operations);
    }

    if (entities && entities.length > 0) {
      await actionPlan.setEntities(entities);
    }

    // Return the created action plan
    return res.status(201).json({
      success: true,
      message: 'Action plan created successfully',
      data: actionPlan
    });
  } catch (error) {
    console.error('Error creating action plan:', error);
    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to create action plan'
    });
  }
};

// Get action plan by ID
const getActionPlanById = async (req, res) => {
  try {
    const { id } = req.params;
    const actionPlan = await ActionPlan.findByPk(id, {
      include: [
        {
          model: User,
          as: 'approver',
          attributes: ['id', 'username', 'email'],
          include: [
            {
              model: db.Role,
              as: 'roles',
              through: { attributes: [] },
              attributes: ['id', 'name', 'code']
            }
          ]
        },
        {
          model: User,
          as: 'assignee',
          attributes: ['id', 'username', 'email'],
          include: [
            {
              model: db.Role,
              as: 'roles',
              through: { attributes: [] },
              attributes: ['id', 'name', 'code']
            }
          ]
        }
      ]
    });

    if (!actionPlan) {
      return res.status(404).json({
        success: false,
        message: 'Action plan not found'
      });
    }

    res.json({
      success: true,
      data: actionPlan
    });
  } catch (error) {
    console.error('Error fetching action plan:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch action plan'
    });
  }
};

// Update action plan
const updateActionPlan = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      category,
      nature,
      origin,
      priority,
      organizationalLevel,
      means,
      comment,
      plannedBeginDate,
      plannedEndDate,
      approverId,
      assigneeId,
      controls,
      incidents,
      products,
      businessLines,
      risks,
      organizationalProcesses,
      businessProcesses,
      operations,
      entities
    } = req.body;

    const actionPlan = await ActionPlan.findByPk(id);

    if (!actionPlan) {
      return res.status(404).json({
        success: false,
        message: 'Action plan not found'
      });
    }

    // Update fields
    await actionPlan.update({
      name: name || actionPlan.name,
      category: category !== undefined ? category : actionPlan.category,
      nature: nature !== undefined ? nature : actionPlan.nature,
      origin: origin !== undefined ? origin : actionPlan.origin,
      priority: priority !== undefined ? priority : actionPlan.priority,
      organizationalLevel: organizationalLevel !== undefined ? organizationalLevel : actionPlan.organizationalLevel,
      means: means !== undefined ? means : actionPlan.means,
      comment: comment !== undefined ? comment : actionPlan.comment,
      plannedBeginDate: plannedBeginDate !== undefined ? plannedBeginDate : actionPlan.plannedBeginDate,
      plannedEndDate: plannedEndDate !== undefined ? plannedEndDate : actionPlan.plannedEndDate,
      approverId: approverId !== undefined ? approverId : actionPlan.approverId,
      assigneeId: assigneeId !== undefined ? assigneeId : actionPlan.assigneeId
    });

    // Handle many-to-many relationships if provided
    if (controls !== undefined) {
      await actionPlan.setControls(controls || []);
    }

    if (incidents !== undefined) {
      await actionPlan.setIncidents(incidents || []);
    }

    if (products !== undefined) {
      await actionPlan.setProducts(products || []);
    }

    if (businessLines !== undefined) {
      await actionPlan.setBusinessLines(businessLines || []);
    }

    if (risks !== undefined) {
      await actionPlan.setRisks(risks || []);
    }

    if (organizationalProcesses !== undefined) {
      await actionPlan.setOrganizationalProcesses(organizationalProcesses || []);
    }

    if (businessProcesses !== undefined) {
      await actionPlan.setBusinessProcesses(businessProcesses || []);
    }

    if (operations !== undefined) {
      await actionPlan.setOperations(operations || []);
    }

    if (entities !== undefined) {
      await actionPlan.setEntities(entities || []);
    }

    // Get the updated action plan
    const updatedActionPlan = await ActionPlan.findByPk(id);

    res.json({
      success: true,
      message: 'Action plan updated successfully',
      data: updatedActionPlan
    });
  } catch (error) {
    console.error('Error updating action plan:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update action plan'
    });
  }
};

// Delete action plan
const deleteActionPlan = async (req, res) => {
  try {
    const { id } = req.params;
    const actionPlan = await ActionPlan.findByPk(id);

    if (!actionPlan) {
      return res.status(404).json({
        success: false,
        message: 'Action plan not found'
      });
    }

    await actionPlan.destroy();

    res.json({
      success: true,
      message: 'Action plan deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting action plan:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete action plan'
    });
  }
};

module.exports = {
  getAllActionPlans,
  createActionPlan,
  getActionPlanById,
  updateActionPlan,
  deleteActionPlan
};
