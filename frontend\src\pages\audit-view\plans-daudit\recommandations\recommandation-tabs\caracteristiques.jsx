import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>lipboard<PERSON>ist,
  AlertTriangle,
  Plus,
  Link,
  ChevronUp,
  ChevronDown,
  Edit,
  Trash2,
  Upload,
  Paperclip,
  Loader2
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { useCustomOutletContext } from "../edit-recommandation";
import { updateRecommendation, linkRecommendationToConstat } from "@/services/audit-recommendation-service";
import { getAllConstats } from "@/services/audit-constat-service";
import { toast } from "sonner";
import { useNavigate, useParams } from "react-router-dom";

function RecommandationCaracteristiquesTab() {
  const navigate = useNavigate();
  const { planId, missionAuditId, activiteId } = useParams();
  const { recommandation, setRecommandation } = useCustomOutletContext();

  // Section toggles
  const [isCaracteristiquesOpen, setIsCaracteristiquesOpen] = useState(true);
  const [isConstatsOpen, setIsConstatsOpen] = useState(true);

  // Form state
  const [formData, setFormData] = useState({
    name: recommandation?.name || "",
    code: recommandation?.code || "",
    priorite: recommandation?.priorite || "moyen",
    description: recommandation?.description || "",
    details: recommandation?.details || "",
    planification: recommandation?.planification || ""
  });

  // Dialog states
  const [openLinkDialog, setOpenLinkDialog] = useState(false);
  const [availableConstats, setAvailableConstats] = useState([]);
  const [selectedConstatId, setSelectedConstatId] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  // Priority options with color mapping (for recommendation priority)
  const prioriteOptions = [
    { value: "très fort", label: "Très fort" },
    { value: "fort", label: "Fort" },
    { value: "moyen", label: "Moyen" },
    { value: "faible", label: "Faible" },
    { value: "très faible", label: "Très faible" }
  ];

  // Colors for recommendation priority circles
  const prioriteColors = {
    'très fort': 'bg-red-500 text-white',
    'fort': 'bg-orange-500 text-white',
    'moyen': 'bg-yellow-500 text-white',
    'faible': 'bg-green-500 text-white',
    'très faible': 'bg-blue-500 text-white'
  };

  // Colors for impact values (in constats table)
  const impactColors = {
    'Tres faible': 'bg-blue-100 text-blue-800',
    'Faible': 'bg-green-100 text-green-800',
    'Moyen': 'bg-yellow-100 text-yellow-800',
    'Fort': 'bg-orange-100 text-orange-800',
    'Très fort': 'bg-red-100 text-red-800'
  };

  // Helper function to capitalize first letter
  const capitalizeFirstLetter = (string) => {
    return string.charAt(0).toUpperCase() + string.slice(1).toLowerCase();
  };

  // Fetch available constats for linking
  useEffect(() => {
    const fetchAvailableConstats = async () => {
      try {
        const response = await getAllConstats();
        if (response.success) {
          // Filter out constats that are already linked to this recommendation
          const linkedConstatIds = recommandation.constats.map(c => c.id);
          const available = response.data.filter(c => !linkedConstatIds.includes(c.id));
          setAvailableConstats(available);
        }
      } catch (error) {
        console.error('Error fetching available constats:', error);
        toast.error('Erreur lors du chargement des constats disponibles');
      }
    };

    if (openLinkDialog) {
      fetchAvailableConstats();
    }
  }, [openLinkDialog, recommandation.constats]);

  // Handlers
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name, value) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSave = async () => {
    setIsLoading(true);
    try {
      const response = await updateRecommendation(recommandation.id, formData);
      if (response.success) {
        setRecommandation(response.data);
        toast.success('Recommandation mise à jour avec succès');
      } else {
        throw new Error(response.message || 'Erreur lors de la mise à jour');
      }
    } catch (error) {
      console.error('Error updating recommendation:', error);
      toast.error(error.message || 'Erreur lors de la mise à jour de la recommandation');
    } finally {
      setIsLoading(false);
    }
  };

  const handleLinkConstat = async () => {
    if (!selectedConstatId) {
      toast.error('Veuillez sélectionner un constat');
      return;
    }

    setIsLoading(true);
    try {
      const response = await linkRecommendationToConstat(recommandation.id, selectedConstatId);
      if (response.success) {
        setRecommandation(response.data);
        setOpenLinkDialog(false);
        setSelectedConstatId(null);
        toast.success('Constat lié avec succès');
      } else {
        throw new Error(response.message || 'Erreur lors de la liaison');
      }
    } catch (error) {
      console.error('Error linking constat:', error);
      toast.error(error.message || 'Erreur lors de la liaison du constat');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6 py-4">
      {/* Section 1: Caractéristiques */}
      <div className="border rounded-lg shadow-sm">
        <button
          type="button"
          className="w-full flex items-center p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-lg"
          onClick={() => setIsCaracteristiquesOpen((v) => !v)}
        >
          <div className="flex items-center gap-2">
            {isCaracteristiquesOpen ? (
              <ChevronUp className="h-5 w-5 text-blue-600" />
            ) : (
              <ChevronDown className="h-5 w-5 text-blue-600" />
            )}
            <FileText className="h-5 w-5 text-blue-600 mr-1" />
            <span className="text-lg font-medium text-blue-800">Caractéristiques</span>
          </div>
        </button>
        {isCaracteristiquesOpen && (
          <div className="p-5 bg-white space-y-6">
            {/* Row 1: Nom (3/4) and Code (1/4) */}
            <div className="grid grid-cols-4 gap-4">
              <div className="col-span-3 space-y-2">
                <Label htmlFor="name">Nom *</Label>
                <Input 
                  id="name" 
                  name="name" 
                  value={formData.name} 
                  onChange={handleInputChange} 
                  placeholder="Nom de la recommandation" 
                  className="w-full" 
                  required 
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="code">Code</Label>
                <Input 
                  id="code" 
                  name="code" 
                  value={formData.code} 
                  onChange={handleInputChange} 
                  placeholder="Code" 
                  className="w-full" 
                />
              </div>
            </div>

            {/* Row 2: Priorité */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="priorite">Priorité *</Label>
                <Select 
                  name="priorite" 
                  value={formData.priorite} 
                  onValueChange={(value) => handleSelectChange("priorite", value)}
                >
                  <SelectTrigger id="priorite" className="w-full">
                    <SelectValue placeholder="Sélectionner une priorité" />
                  </SelectTrigger>
                  <SelectContent>
                    {prioriteOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        <div className="flex items-center gap-2">
                          <div className={`w-3 h-3 rounded-full ${prioriteColors[option.value]}`} />
                          {option.label}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Row 3: Description */}
              <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea 
                id="description" 
                name="description" 
                value={formData.description} 
                onChange={handleInputChange} 
                placeholder="Description de la recommandation" 
                rows={3} 
                className="w-full" 
              />
            </div>

            {/* Row 4: Planification */}
            <div className="space-y-2">
              <Label htmlFor="planification">Planification</Label>
              <Textarea 
                id="planification" 
                name="planification" 
                value={formData.planification} 
                onChange={handleInputChange} 
                placeholder="Planification de la recommandation" 
                rows={3} 
                className="w-full" 
              />
            </div>

            {/* Row 5: Détails */}
            <div className="space-y-2">
              <Label htmlFor="details">Détails</Label>
              <Textarea 
                id="details" 
                name="details" 
                value={formData.details} 
                onChange={handleInputChange} 
                placeholder="Détails de la recommandation" 
                rows={5} 
                className="w-full" 
              />
            </div>

            {/* Save button */}
            <div className="flex justify-end">
              <Button 
                onClick={handleSave} 
                disabled={isLoading}
                className="bg-[#F62D51] hover:bg-[#F62D51]/90"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Enregistrement...
                  </>
                ) : (
                  'Enregistrer'
                )}
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Section 2: Constats */}
      <div className="border rounded-lg shadow-sm">
        <button
          type="button"
          className="w-full flex items-center p-4 bg-gradient-to-r from-amber-50 to-yellow-50 rounded-t-lg"
          onClick={() => setIsConstatsOpen((v) => !v)}
        >
          <div className="flex items-center gap-2">
            {isConstatsOpen ? (
              <ChevronUp className="h-5 w-5 text-yellow-600" />
            ) : (
              <ChevronDown className="h-5 w-5 text-yellow-600" />
            )}
            <AlertTriangle className="h-5 w-5 text-yellow-600 mr-1" />
            <span className="text-lg font-medium text-yellow-800">Constats</span>
          </div>
        </button>
        {isConstatsOpen && (
          <div className="p-5 bg-white space-y-4">
            <div className="flex gap-2 mb-4 justify-end">
              <Button 
                variant="outline" 
                className="flex items-center border-[#F62D51] text-[#F62D51]" 
                onClick={() => setOpenLinkDialog(true)}
              >
                <Link className="h-4 w-4 mr-2" />Relier
              </Button>
            </div>
            <Card className="overflow-hidden">
              <CardContent className="p-0">
                <div className="overflow-x-auto">
                  <Table className="min-w-full">
                    <TableHeader>
                      <TableRow className="bg-gray-50 hover:bg-gray-100/50">
                        <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nom</TableHead>
                        <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Impact</TableHead>
                        <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</TableHead>
                        <TableHead className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"></TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {recommandation.constats?.map((constat) => (
                        <TableRow key={constat.id} className="hover:bg-gray-50">
                          <TableCell className="px-4 py-3 text-sm font-medium text-gray-900">
                            {constat.name}
                          </TableCell>
                          <TableCell className="px-4 py-3 text-sm">
                            <Badge className={`${impactColors[capitalizeFirstLetter(constat.impact)]} border-0`}>
                              {capitalizeFirstLetter(constat.impact)}
                            </Badge>
                            </TableCell>
                          <TableCell className="px-4 py-3 text-sm">
                            {constat.type}
                            </TableCell>
                          <TableCell className="px-4 py-3 text-right">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-gray-500 hover:text-gray-700"
                              onClick={() => navigate(`/audit/plans-daudit/${planId}/missions-audits/${missionAuditId}/activites/${activiteId}/constats/${constat.id}`)}
                            >
                              Voir
                            </Button>
                            </TableCell>
                          </TableRow>
                      ))}
                      {(!recommandation.constats || recommandation.constats.length === 0) && (
                        <TableRow>
                          <TableCell colSpan={4} className="px-4 py-8 text-center text-gray-500">
                            Aucun constat lié
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>

      {/* Link Constat Dialog */}
      <Dialog open={openLinkDialog} onOpenChange={setOpenLinkDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Relier un constat</DialogTitle>
          </DialogHeader>
          <div className="py-4 space-y-4">
            <div className="space-y-2">
              <Label htmlFor="constat">Sélectionner un constat</Label>
              <Select 
                value={selectedConstatId} 
                onValueChange={setSelectedConstatId}
              >
                <SelectTrigger id="constat" className="w-full">
                  <SelectValue placeholder="Sélectionner un constat" />
                </SelectTrigger>
                <SelectContent>
                  {availableConstats.map((constat) => (
                    <SelectItem key={constat.id} value={constat.id}>
                      {constat.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setOpenLinkDialog(false)}>
              Annuler
            </Button>
            <Button 
              onClick={handleLinkConstat} 
              disabled={isLoading || !selectedConstatId}
              className="bg-[#F62D51] hover:bg-[#F62D51]/90"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Liaison...
                </>
              ) : (
                'Relier'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default RecommandationCaracteristiquesTab; 