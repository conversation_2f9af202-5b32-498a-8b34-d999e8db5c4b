import { useState, useEffect, useRef } from "react";
import { useOutletContext } from "react-router-dom";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

// Frappe Gantt loader
const loadFrappeGantt = () => {
  return new Promise((resolve, reject) => {
    if (window.Gantt) {
      resolve();
      return;
    }
    const script = document.createElement("script");
    script.src = "https://cdn.jsdelivr.net/npm/frappe-gantt@0.6.1/dist/frappe-gantt.min.js";
    script.async = false;
    script.onload = () => {
      if (window.Gantt) resolve();
      else reject(new Error("Frappe Gantt not available after script load"));
    };
    script.onerror = () => reject(new Error("Failed to load Frappe Gantt"));
    document.head.appendChild(script);
  });
};

function PlanificationTab() {
  const { auditPlan } = useOutletContext();

  // Filters
  const [periode, setPeriode] = useState("Mois");
  const [dateDebut, setDateDebut] = useState("");
  const [dateFin, setDateFin] = useState("");
  // State for selected mission details
  const [selectedMission, setSelectedMission] = useState(null);
  
  // Mock audit missions
  const [missions, setMissions] = useState([
    { id: 1, name: "Mission Audit 1", startDate: "2024-07-01", endDate: "2024-07-15" },
    { id: 2, name: "Mission Audit 2", startDate: "2024-07-10", endDate: "2024-08-05" },
    { id: 3, name: "Mission Audit 3", startDate: "2024-08-01", endDate: "2024-08-20" }
  ]);
  
  const ganttRef = useRef(null);
  const ganttInstanceRef = useRef(null);

  // Gantt rendering
  useEffect(() => {
    loadFrappeGantt().then(() => {
      if (!ganttRef.current) return;
      ganttRef.current.innerHTML = "";
      // Filter missions by date if filters are set
      let filtered = missions;
      if (dateDebut) filtered = filtered.filter(m => m.endDate >= dateDebut);
      if (dateFin) filtered = filtered.filter(m => m.startDate <= dateFin);
      // Map to Gantt tasks
      const tasks = filtered.map(m => ({
        id: m.id,
        name: "",
        start: m.startDate,
        end: m.endDate,
        progress: 100,
        dependencies: "",
        custom_class: `gantt-task-${m.id}`
      }));
      if (tasks.length === 0) return;
      ganttInstanceRef.current = new window.Gantt(ganttRef.current, tasks, {
        view_mode: periode === "Année" ? "Year" : periode === "Trimestre" ? "Month" : "Day",
        bar_height: 24,
        padding: 24,
        column_width: 36,
        language: "fr",
        show_label: false,
        on_click: (task) => {
          const mission = missions.find(m => m.id === task.id);
          setSelectedMission(mission);
        }
      });
    });
  }, [missions, periode, dateDebut, dateFin]);

  if (!auditPlan) {
    return (
      <div className="text-center py-10">
        <p className="text-gray-500">Chargement des informations du plan d'audit...</p>
      </div>
    );
  }

  const ganttStyles = `
    .gantt svg {
      background: white;
    }
    .gantt .grid-background {
      fill: white;
    }
    .gantt .bar {
      fill: #2563eb;
      stroke: #1e40af;
    }
    .gantt .bar-progress {
      fill: #60a5fa;
      stroke: #1e40af;
    }
    .gantt text {
      fill: #1e293b;
    }
    .gantt .grid-header, .gantt .grid-row {
      fill: #fff;
      stroke: #e2e8f0;
      stroke-width: 1;
    }
    .gantt .grid-header {
      stroke: #cbd5e1;
      stroke-width: 2;
    }
    .gantt .bar-label {
      display: none;
    }
    .mission-list {
      width: 200px;
      border-right: 1px solid #e2e8f0;
      padding-right: 8px;
      display: flex;
      flex-direction: column;
    }
    .mission-header {
      height: 62px;
      border-bottom: 1px solid #e2e8f0;
    }
    .mission-item {
      height: 48px;
      display: flex;
      align-items: center;
      padding-left: 8px;
      border-bottom: 1px solid #e2e8f0;
      box-sizing: border-box;
      line-height: 24px;
      cursor: pointer;
    }
    .mission-item:hover {
      background-color: #f1f5f9;
    }
    .mission-item.selected {
      background-color: #dbeafe;
    }
    .gantt-container {
      flex: 1;
      overflow-x: auto;
      min-height: 48px;
    }
    .mission-details-panel {
      background: white;
      border-radius: 0.5rem;
      padding: 1rem;
      margin-bottom: 1rem;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
      border: 1px solid #e2e8f0;
    }
  `;

  const filteredMissions = missions.filter(m => 
    (!dateDebut || m.endDate >= dateDebut) && 
    (!dateFin || m.startDate <= dateFin)
  );

  return (
    <div className="space-y-4 py-4">
      <style>{ganttStyles}</style>
      
      {/* Mission details panel at the top */}
      {selectedMission && (
        <div className="mission-details-panel">
          <h3 className="font-semibold text-lg">{selectedMission.name}</h3>
          <div className="grid grid-cols-2 gap-4 mt-2">
            <div>
              <p className="text-sm text-gray-600">Date de début:</p>
              <p className="font-medium">{selectedMission.startDate}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Date de fin:</p>
              <p className="font-medium">{selectedMission.endDate}</p>
            </div>
          </div>
        </div>
      )}

      {/* Filter row */}
      <div className="bg-white p-6 rounded-lg shadow">
        <div className="flex flex-col md:flex-row gap-4 justify-center items-center mb-6">
          <div className="flex flex-col items-start min-w-[180px]">
            <label className="text-sm font-medium mb-1">Période de calendrier</label>
            <Select value={periode} onValueChange={setPeriode}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Période" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Mois">Mois</SelectItem>
                <SelectItem value="Trimestre">Trimestre</SelectItem>
                <SelectItem value="Année">Année</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="flex flex-col items-start min-w-[180px]">
            <label className="text-sm font-medium mb-1">Date de début</label>
            <Input type="date" value={dateDebut} onChange={e => setDateDebut(e.target.value)} />
          </div>
          <div className="flex flex-col items-start min-w-[180px]">
            <label className="text-sm font-medium mb-1">Date de fin</label>
            <Input type="date" value={dateFin} onChange={e => setDateFin(e.target.value)} />
          </div>
        </div>

        {/* Two-column layout */}
        <div className="flex">
          {/* Mission names column */}
          <div className="mission-list">
            <div className="mission-header"></div>
            {filteredMissions.map(m => (
              <div 
                key={m.id} 
                className={`mission-item ${selectedMission?.id === m.id ? 'selected' : ''}`}
                onClick={() => setSelectedMission(m)}
              >
                <span className="text-sm text-gray-800 truncate">{m.name}</span>
              </div>
            ))}
          </div>
          
          {/* Gantt chart container */}
          <div ref={ganttRef} className="gantt gantt-container" />
        </div>
      </div>
    </div>
  );
}

export default PlanificationTab;