const { Sequelize } = require('sequelize');

module.exports = (sequelize) => {
  const AuditMissionRapport = {
    async getMissionReport(missionId) {
      const query = `
        SELECT 
            (SELECT "username" FROM "Users" WHERE "id" = am."chefmission") AS chefmission,
            (SELECT "username" FROM "Users" WHERE "id" = ap."directeuraudit") AS directeuraudit,
            (SELECT "username" FROM "Users" WHERE "id" = aa."responsable") AS activity_responsable,
            (SELECT "username" FROM "Users" WHERE "id" = ac."responsable") AS constat_responsable,
            (SELECT "username" FROM "Users" WHERE "id" = ar."responsableId") AS recommendation_responsable,
            am."name" AS mission_name,
            am."categorie",
            am."evaluation",
            am."objectif",
            am."pointfort",
            am."pointfaible",
            STRING_AGG(DISTINCT r."name", ', ') AS risk_names,
            STRING_AGG(DISTINCT e."name", ', ') AS entity_names,
            STRING_AGG(DISTINCT c."name", ', ') AS control_names,
            STRING_AGG(DISTINCT i."name", ', ') AS incident_names,
            STRING_AGG(DISTINCT op."name", ', ') AS organizational_process_names,
            (SELECT COALESCE(SUM("chargedetravailestimee"), 0) FROM "AuditActivities" WHERE "auditMissionID" = am."id") AS chargedetravailestimee,
            (SELECT COALESCE(SUM("chargedetravaileffective"), 0) FROM "AuditActivities" WHERE "auditMissionID" = am."id") AS chargedetravaileffective,
            (SELECT COALESCE(SUM("depense"), 0) FROM "AuditActivities" WHERE "auditMissionID" = am."id") AS depenses,
            am."principalAudite",
            ac."name" AS constat_name,
            ac."impact" AS constat_impact,
            ar."name" AS recommendation_name,
            ar."details" AS recommendation_details,
            am."datefin",
            am."datedebut"
        FROM 
            "AuditMissions" am
            LEFT JOIN "AuditPlans" ap ON am."auditplanID" = ap."id"
            LEFT JOIN "AuditActivities" aa ON aa."auditMissionID" = am."id"
            LEFT JOIN "AuditConstats" ac ON ac."auditActivityID" = aa."id"
            LEFT JOIN "AuditScopes" ascop ON ascop."auditMissionID" = am."id"
            LEFT JOIN "AuditScopeRisks" asr ON asr."auditScopeID" = ascop."id"
            LEFT JOIN "Risk" r ON r."riskID" = asr."riskID"
            LEFT JOIN "AuditScopeEntities" ase ON ase."auditScopeID" = ascop."id"
            LEFT JOIN "Entity" e ON e."entityID" = ase."entityID"
            LEFT JOIN "AuditScopeControls" asctrl ON asctrl."auditScopeID" = ascop."id"
            LEFT JOIN "Control" c ON c."controlID" = asctrl."controlID"
            LEFT JOIN "AuditScopeIncidents" asi ON asi."auditScopeID" = ascop."id"
            LEFT JOIN "Incident" i ON i."incidentID" = asi."incidentID"
            LEFT JOIN "AuditScopeProcesses" asp ON asp."auditScopeID" = ascop."id"
            LEFT JOIN "OrganizationalProcess" op ON op."organizationalProcessID" = asp."organizationalProcessID"
            LEFT JOIN "ConstatRecommendation" cr ON cr."constatId" = ac."id"
            LEFT JOIN "AuditRecommendations" ar ON ar."id" = cr."recommendationId"
        WHERE 
            am."id" = :missionId
        GROUP BY 
            am."chefmission",
            ap."directeuraudit",
            aa."responsable",
            ac."responsable",
            am."name",
            am."categorie",
            am."evaluation",
            am."objectif",
            am."pointfort",
            am."pointfaible",
            ar."responsableId",
            am."principalAudite",
            ac."name",
            ac."impact",
            ar."name",
            ar."details",
            am."datefin",
            am."datedebut",
            am."id";
      `;
      try {
        const results = await sequelize.query(query, {
          replacements: { missionId },
          type: Sequelize.QueryTypes.SELECT,
        });
        return results;
      } catch (error) {
        throw new Error(`Error fetching mission report: ${error.message}`);
      }
    },
  };

  return AuditMissionRapport;
};