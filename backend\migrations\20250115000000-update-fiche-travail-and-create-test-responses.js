'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    // 1. Create FicheTestResponses table
    await queryInterface.createTable('FicheTestResponses', {
      id: {
        type: Sequelize.STRING(50),
        primaryKey: true,
        allowNull: false
      },
      ficheDeTravailID: {
        type: Sequelize.STRING(50),
        allowNull: false,
        references: {
          model: 'FicheDeTravail',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      questionID: {
        type: Sequelize.STRING(50),
        allowNull: false,
        references: {
          model: 'Questions',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      sampleNumber: {
        type: Sequelize.INTEGER,
        allowNull: false
      },
      answer: {
        type: Sequelize.JSONB,
        allowNull: true
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // 2. Add indexes for FicheTestResponses
    await queryInterface.addIndex('FicheTestResponses', ['ficheDeTravailID']);
    await queryInterface.addIndex('FicheTestResponses', ['questionID']);
    await queryInterface.addIndex('FicheTestResponses', ['sampleNumber']);
    await queryInterface.addIndex('FicheTestResponses', ['ficheDeTravailID', 'questionID', 'sampleNumber'], {
      unique: true,
      name: 'unique_fiche_question_sample'
    });

    // 3. Update FicheDeTravail table - change tailleEchantillon from STRING to INTEGER
    // First, add a temporary column
    await queryInterface.addColumn('FicheDeTravail', 'tailleEchantillonNew', {
      type: Sequelize.INTEGER,
      allowNull: true
    });

    // 4. Convert existing string values to integers
    await queryInterface.sequelize.query(`
      UPDATE "FicheDeTravail" 
      SET "tailleEchantillonNew" = CASE 
        WHEN "tailleEchantillon" ~ '^[0-9]+$' THEN CAST("tailleEchantillon" AS INTEGER)
        ELSE NULL
      END
      WHERE "tailleEchantillon" IS NOT NULL
    `);

    // 5. Drop the old column and rename the new one
    await queryInterface.removeColumn('FicheDeTravail', 'tailleEchantillon');
    await queryInterface.renameColumn('FicheDeTravail', 'tailleEchantillonNew', 'tailleEchantillon');

    // 6. Add validation constraint for tailleEchantillon
    await queryInterface.addConstraint('FicheDeTravail', {
      fields: ['tailleEchantillon'],
      type: 'check',
      name: 'tailleEchantillon_range',
      where: {
        tailleEchantillon: {
          [Sequelize.Op.or]: [
            { [Sequelize.Op.is]: null },
            { [Sequelize.Op.between]: [1, 99] }
          ]
        }
      }
    });
  },

  async down (queryInterface, Sequelize) {
    // 1. Drop FicheTestResponses table
    await queryInterface.dropTable('FicheTestResponses');

    // 2. Revert FicheDeTravail changes
    // Add temporary column
    await queryInterface.addColumn('FicheDeTravail', 'tailleEchantillonOld', {
      type: Sequelize.STRING(50),
      allowNull: true
    });

    // Convert back to string
    await queryInterface.sequelize.query(`
      UPDATE "FicheDeTravail" 
      SET "tailleEchantillonOld" = CAST("tailleEchantillon" AS VARCHAR)
      WHERE "tailleEchantillon" IS NOT NULL
    `);

    // Drop constraint
    await queryInterface.removeConstraint('FicheDeTravail', 'tailleEchantillon_range');

    // Drop new column and rename old one
    await queryInterface.removeColumn('FicheDeTravail', 'tailleEchantillon');
    await queryInterface.renameColumn('FicheDeTravail', 'tailleEchantillonOld', 'tailleEchantillon');
  }
}; 