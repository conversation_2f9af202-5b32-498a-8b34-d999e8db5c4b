// backend/routes/users/role-routes.js
const express = require('express');
const router = express.Router();
const { verifyToken, authorizeRoles, loadUserRoles } = require('../../middleware/auth');
const {
  getAllRoles,
  getRoleById,
  createRole,
  updateRole,
  deleteRole,
  getUserRoles,
  assignRolesToUser,
  removeRoleFromUser,
  getUsersByRole
} = require('../../controllers/users/role-controller');

// Apply authentication middleware to all routes
router.use(verifyToken);
router.use(loadUserRoles);

// Role management routes - only GRC Administrator can access
router.get('/', authorizeRoles(['grc_admin']), getAllRoles);
router.post('/', authorizeRoles(['grc_admin']), createRole);
router.get('/:id', authorizeRoles(['grc_admin']), getRoleById);
router.put('/:id', authorizeRoles(['grc_admin']), updateRole);
router.delete('/:id', authorizeRoles(['grc_admin']), deleteRole);

// User-role management routes
router.get('/user/:userId', authorizeRoles(['grc_admin', 'grc_manager']), getUserRoles);
router.post('/user/:userId', authorizeRoles(['grc_admin']), assignRolesToUser);
router.delete('/user/:userId/:roleId', authorizeRoles(['grc_admin']), removeRoleFromUser);

// Get users by role
router.get('/:roleId/users', authorizeRoles(['grc_admin']), getUsersByRole);

module.exports = router;
