import { useState } from "react";
import { Ch<PERSON><PERSON>Down, ChevronUp, Filter, X } from "lucide-react";
import { But<PERSON> } from "./button";
import { Separator } from "./separator";
import { Badge } from "./badge";
import { cn } from "@/lib/utils";

const FilterPanel = ({
  filters,
  activeFilters,
  onClearFilters,
  className,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  // Count active filters - updated to handle "all" value and date ranges
  const activeFilterCount = activeFilters ? Object.entries(activeFilters).filter(
    // eslint-disable-next-line no-unused-vars
    ([key, value]) => {
      // Handle date range objects
      if (value && typeof value === 'object' && !Array.isArray(value)) {
        // Check if it's a date range object with start/end properties
        if ('start' in value || 'end' in value) {
          return value.start !== null || value.end !== null;
        }
      }

      // Handle regular values
      if (value === null || value === undefined) return false;
      if (value === "all") return false;
      if (value === "") return false;
      if (Array.isArray(value)) return value.length > 0;
      return true;
    }
  ).length : 0;

  // Removed console.log for production

  return (
    <div className={cn("bg-gray-50 rounded-lg shadow-sm border border-gray-200", className)}>
      {/* Header */}
      <div
        className="flex items-center justify-between p-4 cursor-pointer"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4 text-gray-500" />
          <h3 className="font-medium text-gray-700">Filters</h3>
          {activeFilterCount > 0 && (
            <Badge variant="secondary" className="bg-blue-100 text-blue-800 hover:bg-blue-200">
              {activeFilterCount}
            </Badge>
          )}
        </div>
        <div className="flex items-center gap-2">
          {activeFilterCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              className="text-red-500 hover:text-red-700 hover:bg-red-50 transition-colors"
              onClick={(e) => {
                e.stopPropagation();
                onClearFilters();
              }}
            >
              <X className="h-3 w-3 mr-1" />
              Clear
            </Button>
          )}
          {isExpanded ? (
            <ChevronUp className="h-4 w-4 text-gray-500" />
          ) : (
            <ChevronDown className="h-4 w-4 text-gray-500" />
          )}
        </div>
      </div>

      {/* Filter Content */}
      {isExpanded && (
        <div className="p-4 pt-0">
          <Separator className="mb-4" />
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filters.map((filter) => (
              <div key={filter.id} className="space-y-2">
                <label className="text-sm font-medium text-gray-700">
                  {filter.label}
                </label>
                {filter.component}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default FilterPanel;

