const express = require('express');
const router = express.Router();
const { verifyToken, authorizeRoles, authenticateToken } = require('../../middleware/auth');
const incidentController = require('../../controllers/incidents/incident-controller');
const workflowController = require('../../controllers/incidents/workflow-controller');
const activityController = require('../../controllers/incidents/activity-controller');
const { authorizeIncidentContributor } = require('../../middleware/contributor-auth');

// Apply authentication middleware to all routes
router.use(verifyToken);

// Get all incidents
router.get('/', incidentController.getAllIncidents);

// Create new incident
router.post('/', incidentController.createIncident);

// Get incident by ID
router.get('/:id', incidentController.getIncidentById);

// Update incident
router.put('/:id', incidentController.updateIncident);

// Delete incident - restrict to admin roles only
router.delete('/:id', authorizeRoles(['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager']), incidentController.deleteIncident);

// Workflow routes
// Main workflow endpoint - this was missing and causing 404 errors
router.get('/:incidentId/workflow',
  authenticateToken,
  workflowController.getWorkflowState
);

router.get('/:incidentId/workflow/state',
  authenticateToken,
  workflowController.getWorkflowState
);

router.get('/:incidentId/workflow/transitions',
  authenticateToken,
  workflowController.getAvailableTransitions
);

router.post('/:incidentId/workflow/initialize',
  authenticateToken,
  workflowController.initializeWorkflow
);

router.get('/:incidentId/workflow/history',
  authenticateToken,
  workflowController.getWorkflowHistory
);

router.post('/:incidentId/workflow/transition',
  authenticateToken,
  authorizeIncidentContributor(),
  workflowController.transitionState
);

router.get('/workflow/states', workflowController.getAllPossibleStates);

// Activity routes
router.get('/:incidentId/activities', activityController.getIncidentActivities);

// Test endpoint for activity controller
router.post('/:incidentId/test-activity',
  authenticateToken,
  async (req, res) => {
    try {
      const success = await activityController.logActivity({
        userId: req.user.id,
        action: 'TEST',
        entityType: 'Incident',
        entityId: req.params.incidentId,
        details: 'Testing activity logging'
      });

      return res.status(200).json({
        success: true,
        message: 'Test activity logged successfully',
        result: success
      });
    } catch (error) {
      console.error('Error in test activity endpoint:', error);
      return res.status(500).json({
        success: false,
        message: 'Test activity failed',
        error: error.message
      });
    }
  }
);

module.exports = router;
