import { useOutletContext } from "react-router-dom";
import { CalendarIcon, Clock, Tag, Users, Building, MapPin, FileText, User } from "lucide-react";
import { format } from "date-fns";
import { useTranslation } from "react-i18next";

function ActionPlanOverview() {
  const { actionPlan } = useOutletContext();
  const { t } = useTranslation();

  // Format date if it exists
  const formatDate = (dateString) => {
    if (!dateString) return t('admin.action_plans.overview.not_set', 'Not set');
    try {
      return format(new Date(dateString), "yyyy-MM-dd");
    } catch (error) {
      return dateString;
    }
  };

  return (
    <div>
      <h2 className="text-xl font-semibold mb-6">{t('admin.action_plans.overview.title', 'Action Plan Overview')}</h2>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div>
          <h3 className="text-lg font-medium mb-4">{t('admin.action_plans.overview.details', 'Details')}</h3>
          <div className="space-y-4">
            <div className="flex items-start">
              <Tag className="h-5 w-5 mr-3 text-gray-500 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-500">{t('admin.action_plans.overview.category', 'Category')}</p>
                <p className="text-base">{actionPlan.category || t('admin.action_plans.overview.not_specified', 'Not specified')}</p>
              </div>
            </div>

            <div className="flex items-start">
              <FileText className="h-5 w-5 mr-3 text-gray-500 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-500">{t('admin.action_plans.overview.nature', 'Nature')}</p>
                <p className="text-base">{actionPlan.nature || t('admin.action_plans.overview.not_specified', 'Not specified')}</p>
              </div>
            </div>

            <div className="flex items-start">
              <Building className="h-5 w-5 mr-3 text-gray-500 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-500">{t('admin.action_plans.overview.origin', 'Origin')}</p>
                <p className="text-base">{actionPlan.origin || t('admin.action_plans.overview.not_specified', 'Not specified')}</p>
              </div>
            </div>

            <div className="flex items-start">
              <MapPin className="h-5 w-5 mr-3 text-gray-500 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-500">{t('admin.action_plans.overview.organizational_level', 'Organizational Level')}</p>
                <p className="text-base">{actionPlan.organizationalLevel || t('admin.action_plans.overview.not_specified', 'Not specified')}</p>
              </div>
            </div>

            <div className="flex items-start">
              <User className="h-5 w-5 mr-3 text-gray-500 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-500">{t('admin.action_plans.overview.approver', 'Approver')}</p>
                <p className="text-base">
                  {actionPlan.approver ?
                    `${actionPlan.approver.username} (${actionPlan.approver.role})` :
                    t('admin.action_plans.overview.not_assigned', 'Not assigned')}
                </p>
              </div>
            </div>

            <div className="flex items-start">
              <User className="h-5 w-5 mr-3 text-gray-500 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-500">{t('admin.action_plans.overview.assignee', 'Assignee')}</p>
                <p className="text-base">
                  {actionPlan.assignee ?
                    `${actionPlan.assignee.username} (${actionPlan.assignee.role})` :
                    t('admin.action_plans.overview.not_assigned', 'Not assigned')}
                </p>
              </div>
            </div>

            <div className="flex items-start">
              <Users className="h-5 w-5 mr-3 text-gray-500 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-500">{t('admin.action_plans.overview.means', 'Means')}</p>
                <p className="text-base">{actionPlan.means || t('admin.action_plans.overview.not_specified', 'Not specified')}</p>
              </div>
            </div>
          </div>
        </div>

        <div>
          <h3 className="text-lg font-medium mb-4">{t('admin.action_plans.overview.timeline', 'Timeline')}</h3>
          <div className="space-y-4">
            <div className="flex items-start">
              <CalendarIcon className="h-5 w-5 mr-3 text-gray-500 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-500">{t('admin.action_plans.overview.planned_begin_date', 'Planned Begin Date')}</p>
                <p className="text-base">{formatDate(actionPlan.plannedBeginDate)}</p>
              </div>
            </div>

            <div className="flex items-start">
              <CalendarIcon className="h-5 w-5 mr-3 text-gray-500 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-500">{t('admin.action_plans.overview.planned_end_date', 'Planned End Date')}</p>
                <p className="text-base">{formatDate(actionPlan.plannedEndDate)}</p>
              </div>
            </div>

            <div className="flex items-start">
              <Clock className="h-5 w-5 mr-3 text-gray-500 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-500">{t('admin.action_plans.overview.duration', 'Duration')}</p>
                <p className="text-base">
                  {actionPlan.plannedBeginDate && actionPlan.plannedEndDate ?
                    `${Math.ceil((new Date(actionPlan.plannedEndDate) - new Date(actionPlan.plannedBeginDate)) / (1000 * 60 * 60 * 24))} ${t('admin.action_plans.overview.days', 'days')}` :
                    t('admin.action_plans.overview.not_available', 'Not available')}
                </p>
              </div>
            </div>
          </div>

          <h3 className="text-lg font-medium mt-8 mb-4">{t('admin.action_plans.overview.description', 'Description')}</h3>
          <div className="bg-gray-50 p-4 rounded-lg">
            <p className="text-base whitespace-pre-wrap">
              {actionPlan.comment || t('admin.action_plans.overview.no_description', 'No description provided.')}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ActionPlanOverview;
