const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const { Attachment } = require('../../models');

// Create uploads directory if it doesn't exist
const uploadsDir = path.join(__dirname, '../../uploads');
const businessDocsDir = path.join(uploadsDir, 'business-documents');
const externalRefsDir = path.join(uploadsDir, 'external-references');

// Directories will be created on-demand in the uploadFile function

// Upload a file
const uploadFile = async (req, res) => {
  try {
    // console.log('Upload request received:', {
    //   files: req.files ? Object.keys(req.files).length : 0,
    //   body: req.body
    // });

    // Ensure upload directories exist
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true });
      // console.log('Created uploads directory');
    }

    if (!fs.existsSync(businessDocsDir)) {
      fs.mkdirSync(businessDocsDir, { recursive: true });
      // console.log('Created business-documents directory');
    }

    if (!fs.existsSync(externalRefsDir)) {
      fs.mkdirSync(externalRefsDir, { recursive: true });
      // console.log('Created external-references directory');
    }

    if (!req.files || Object.keys(req.files).length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No files were uploaded'
      });
    }

    const { type, incidentID } = req.body;

    if (!type || !['business-document', 'external-reference'].includes(type)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid file type. Must be "business-document" or "external-reference"'
      });
    }

    if (!incidentID) {
      return res.status(400).json({
        success: false,
        message: 'Incident ID is required'
      });
    }

    // console.log(`Processing upload for incident ${incidentID}, type: ${type}`);

    const uploadedFiles = Array.isArray(req.files.files) ? req.files.files : [req.files.files];
    const savedFiles = [];

    // Define allowed file extensions and MIME types
    const allowedExtensions = [
      '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
      '.txt', '.csv', '.rtf', '.odt', '.ods', '.odp',
      '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg',
      '.zip', '.rar', '.7z', '.tar', '.gz'
    ];

    const blockedExtensions = [
      '.exe', '.bat', '.cmd', '.msi', '.dll', '.bin',
      '.sh', '.com', '.scr', '.vbs', '.js', '.jar', '.py'
    ];

    for (const file of uploadedFiles) {
      const fileExtension = path.extname(file.name).toLowerCase();

      // Check if file extension is explicitly blocked
      if (blockedExtensions.includes(fileExtension)) {
        return res.status(400).json({
          success: false,
          message: `File type ${fileExtension} is not allowed for security reasons`
        });
      }

      // Check if file extension is allowed
      if (!allowedExtensions.includes(fileExtension)) {
        return res.status(400).json({
          success: false,
          message: `File type ${fileExtension} is not supported. Allowed types: ${allowedExtensions.join(', ')}`
        });
      }

      const fileName = `${uuidv4()}${fileExtension}`;
      const uploadDir = type === 'business-document' ? businessDocsDir : externalRefsDir;
      const filePath = path.join(uploadDir, fileName);

      // Move the file to the uploads directory
      await file.mv(filePath);

      // Generate a unique ID for the attachment
      const attachmentID = `ATT_${Date.now()}_${Math.floor(Math.random() * 1000)}`;

      // Save file metadata to database
      const attachment = await Attachment.create({
        attachmentID,
        fileName: file.name,
        fileSize: file.size,
        fileType: file.mimetype,
        filePath: fileName,
        uploadDate: new Date(),
        type,
        incidentID
      });

      // console.log(`File saved: ${file.name}, ID: ${attachmentID}, Path: ${fileName}`);

      savedFiles.push({
        id: attachment.attachmentID,
        name: file.name,
        size: file.size,
        type: file.mimetype,
        uploadDate: attachment.uploadDate
      });
    }

    return res.status(201).json({
      success: true,
      message: `${savedFiles.length} file(s) uploaded successfully`,
      data: savedFiles
    });
  } catch (error) {
    console.error('Error uploading file:', error);
    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to upload file'
    });
  }
};

// Get all attachments for an incident
const getAttachments = async (req, res) => {
  try {
    const { incidentID, type } = req.query;

    if (!incidentID) {
      return res.status(400).json({
        success: false,
        message: 'Incident ID is required'
      });
    }

    // Set cache control headers to prevent caching
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');

    const query = { incidentID };

    if (type && ['business-document', 'external-reference'].includes(type)) {
      query.type = type;
    }

    // Force refresh from database
    const attachments = await Attachment.findAll({
      where: query,
      order: [['uploadDate', 'DESC']],
      raw: true // Get plain objects instead of Sequelize instances for better performance
    });

    return res.json({
      success: true,
      data: attachments.map(attachment => ({
        id: attachment.attachmentID,
        name: attachment.fileName,
        size: attachment.fileSize,
        type: attachment.fileType,
        uploadDate: attachment.uploadDate
      }))
    });
  } catch (error) {
    console.error('Error fetching attachments:', error);
    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to fetch attachments'
    });
  }
};

// Delete an attachment
const deleteAttachment = async (req, res) => {
  try {
    const { id } = req.params;

    const attachment = await Attachment.findByPk(id);

    if (!attachment) {
      return res.status(404).json({
        success: false,
        message: 'Attachment not found'
      });
    }

    // Delete the file from the filesystem
    const uploadDir = attachment.type === 'business-document' ? businessDocsDir : externalRefsDir;
    const filePath = path.join(uploadDir, attachment.filePath);

    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }

    // Delete the record from the database
    await attachment.destroy();

    return res.json({
      success: true,
      message: 'Attachment deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting attachment:', error);
    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to delete attachment'
    });
  }
};

// Download an attachment
const downloadAttachment = async (req, res) => {
  try {
    const { id } = req.params;

    const attachment = await Attachment.findByPk(id);

    if (!attachment) {
      return res.status(404).json({
        success: false,
        message: 'Attachment not found'
      });
    }

    const uploadDir = attachment.type === 'business-document' ? businessDocsDir : externalRefsDir;
    const filePath = path.join(uploadDir, attachment.filePath);

    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        success: false,
        message: 'File not found on server'
      });
    }

    res.download(filePath, attachment.fileName);
  } catch (error) {
    console.error('Error downloading attachment:', error);
    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to download attachment'
    });
  }
};

module.exports = {
  uploadFile,
  getAttachments,
  deleteAttachment,
  downloadAttachment
};
