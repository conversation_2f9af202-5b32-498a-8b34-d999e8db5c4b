import React, { useState, useMemo, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Checkbox } from "@/components/ui/checkbox";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import TablePagination from "@/components/ui/table-pagination";
import PageHeader from '@/components/ui/page-header';
import { Plus, Search, ArrowUpDown, Edit, Trash2, Filter as FilterIcon, XCircle, Loader2, AlertCircle, ChevronUp } from "lucide-react";
import { toast } from 'sonner';
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import { Textarea } from "@/components/ui/textarea";
import auditIcon from '@/assets/audit.png';
import FilterPanel from "@/components/ui/filter-panel";
import LoadingProgress from "@/components/ui/loading-progress";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { getAllAuditPlans, createAuditPlan, deleteAuditPlan } from '@/services/audit-plan-service';
import axios from 'axios';

// Helper function to get status label and color
const getStatusLabel = (status) => {
  switch (status) {
    case 'Planned':
      return { label: 'Planifié', color: 'bg-blue-100 text-blue-800' };
    case 'In Progress':
      return { label: 'En cours', color: 'bg-yellow-100 text-yellow-800' };
    case 'Completed':
      return { label: 'Terminé', color: 'bg-green-100 text-green-800' };
    default:
      return { label: status, color: 'bg-gray-100 text-gray-800' };
  }
};

function PlansDauditList() {
  const navigate = useNavigate();
  const [auditPlans, setAuditPlans] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedPlans, setSelectedPlans] = useState([]);
  const [sortConfig, setSortConfig] = useState({ key: 'datedebut', direction: 'desc' });
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [filters, setFilters] = useState({
    status: 'all',
    year: 'all',
  });
  const [newPlan, setNewPlan] = useState({
    name: "",
    calendrier: new Date().getFullYear().toString()
  });

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Loading state
  const [loading, setLoading] = useState(true);
  const [showLoadingIndicator, setShowLoadingIndicator] = useState(false);

  // Preload icon
  const [iconLoaded, setIconLoaded] = useState(false);

  // Fetch audit plans on component mount
  useEffect(() => {
    fetchAuditPlans();
  }, []);

  // Preload the icon when component mounts
  useEffect(() => {
    if (auditIcon) {
      const img = new Image();
      img.src = auditIcon;
      img.onload = () => setIconLoaded(true);
    }
  }, []);

  // Fetch audit plans from API
  const fetchAuditPlans = async () => {
    try {
      setLoading(true);
      const response = await getAllAuditPlans();
      if (response.success) {
        setAuditPlans(response.data);
      } else {
        // Handle API-specific errors returned in the response payload
        toast.error(response.message || 'Failed to fetch audit plans');
      }
    } catch (error) {
      // Check if the error is an Axios timeout error (ECONNABORTED)
      if (axios.isAxiosError(error) && error.code === 'ECONNABORTED') {
        // Handle timeout specifically - show toast but do not log this specific error
        toast.error('Request timed out while fetching audit plans. Please try again.');
      } else {
        // Handle other errors - log and show a generic toast
        console.error('An unexpected error occurred while fetching audit plans:', error);
        toast.error('An unexpected error occurred while fetching audit plans.');
      }
    } finally {
      setLoading(false);
    }
  };

  // Unique values for filters
  const uniqueYears = useMemo(() => {
    const years = new Set(auditPlans.map(plan => plan.calendrier));
    return ['all', ...Array.from(years).sort((a, b) => b - a)];
  }, [auditPlans]);

  // Handle filter change
  const handleFilterChange = (filterName, value) => {
    setFilters(prev => ({ ...prev, [filterName]: value }));
    setCurrentPage(1);
  };

  // Clear all filters
  const clearFilters = () => {
    setFilters({ status: 'all', year: 'all' });
    setCurrentPage(1);
  };

  const handleSort = (key) => {
    setSortConfig(prevConfig => ({
      key,
      direction: prevConfig.key === key && prevConfig.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const handleSelectAll = (checked) => {
    if (checked) {
      setSelectedPlans(currentPlans.map(plan => plan.id));
    } else {
      setSelectedPlans([]);
    }
  };

  const handleSelectPlan = (id) => {
    setSelectedPlans(prev =>
      prev.includes(id) ? prev.filter(item => item !== id) : [...prev, id]
    );
  };

  const handleRowClick = (id) => {
    navigate(`/audit/plans-daudit/${id}`);
  };

  // Handle create plan form input changes
  const handleCreateInputChange = (e) => {
    const { name, value } = e.target;
    setNewPlan(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle create plan form select changes
  const handleCreateSelectChange = (name, value) => {
    setNewPlan(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle create plan form submission
  const handleCreatePlan = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      if (!newPlan.name || !newPlan.calendrier) {
        throw new Error("Veuillez remplir tous les champs obligatoires: Nom et Calendrier (Année).");
      }

      const response = await createAuditPlan({
        name: newPlan.name,
        calendrier: parseInt(newPlan.calendrier),
        status: "Planned"
      });

      if (response.success) {
        toast.success("Nouveau plan d'audit créé avec succès!");
        // Add the new plan to the list without full refresh
        setAuditPlans(prevPlans => [...prevPlans, response.data]);
      // Reset form and close modal
      setNewPlan({
        name: "",
          calendrier: new Date().getFullYear().toString()
      });
      setIsCreateModalOpen(false);
      } else {
        throw new Error(response.message || "Failed to create audit plan");
      }
    } catch (error) {
      console.error('Error creating plan:', error);
      toast.error(error?.message || "Échec de la création du plan");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteSelected = async () => {
    if (selectedPlans.length === 0) {
      toast.error("Aucun plan sélectionné");
      return;
    }

    if (window.confirm(`Êtes-vous sûr de vouloir supprimer ${selectedPlans.length} plan(s) sélectionné(s) ?`)) {
      try {
        // Delete plans one by one since bulk delete endpoint doesn't exist
        const deletePromises = selectedPlans.map(id => deleteAuditPlan(id));
        await Promise.all(deletePromises);

        toast.success(`${selectedPlans.length} plan(s) d'audit supprimé(s) avec succès.`);
        // Update the list without full refresh
        setAuditPlans(prevPlans => prevPlans.filter(plan => !selectedPlans.includes(plan.id)));
        // Clear selection
        setSelectedPlans([]);
      } catch (error) {
        console.error('Error in bulk delete operation:', error);
        toast.error(error?.message || 'Une erreur inattendue est survenue lors de la suppression');
      }
    }
  };

  // Filter plans based on searchQuery and filters
  const filteredPlans = useMemo(() => {
    return auditPlans.filter(plan => {
      // Apply search query filter
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        const matchesSearch = plan.name.toLowerCase().includes(query);
        if (!matchesSearch) return false;
      }

      // Status filter
      if (filters.status && filters.status !== 'all') {
        if (plan.status !== filters.status) return false;
      }

      // Year filter
      if (filters.year && filters.year !== 'all') {
        if (plan.calendrier !== parseInt(filters.year)) return false;
      }

      return true;
    });
  }, [auditPlans, searchQuery, filters]);

  // Sort plans
  const sortedPlans = useMemo(() => {
    return [...filteredPlans].sort((a, b) => {
      if (!sortConfig.key) return 0;

      let aValue = a[sortConfig.key];
      let bValue = b[sortConfig.key];

      if (sortConfig.key === 'datedebut' || sortConfig.key === 'datefin') {
        aValue = new Date(aValue);
        bValue = new Date(bValue);
      }

      if (aValue < bValue) return sortConfig.direction === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortConfig.direction === 'asc' ? 1 : -1;
      return 0;
    });
  }, [filteredPlans, sortConfig]);

  // Paginate
  const totalItems = sortedPlans.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const currentPlans = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return sortedPlans.slice(startIndex, startIndex + itemsPerPage);
  }, [sortedPlans, currentPage, itemsPerPage]);

  const handlePageChange = (page) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  const breadcrumbItems = [
    { label: "Audit", href: "/audit/welcome" },
    { label: "Plans d'Audit" },
  ];

  // Loading screen
  if (loading) {
    return (
      <div className="p-6 flex justify-center items-center h-screen">
        <div className="flex flex-col items-center">
          <Loader2 className="h-12 w-12 animate-spin text-[#F62D51]" />
          <span className="mt-4 text-gray-600">Chargement des plans d'audit...</span>
        </div>
      </div>
    );
  }

  // Columns definition
  const columns = [
    { key: 'name', label: 'Nom du Plan', sortable: true },
    { key: 'status', label: 'Statut', sortable: true },
    { key: 'calendrier', label: 'Année', sortable: true },
    { key: 'datedebut', label: 'Date de début', sortable: true },
    { key: 'datefin', label: 'Date de fin', sortable: true },
    { key: 'avancement', label: 'Avancement', sortable: true }
  ];

  return (
    <div className="p-6">
      {/* Loading indicator */}
      {showLoadingIndicator && (
        <LoadingProgress
          isLoading={loading}
          duration={1500}
          message="Chargement des plans d'audit..."
          onComplete={() => setShowLoadingIndicator(false)}
        />
      )}

      <PageHeader
        title="Gestion des Plans d'Audit"
        description="Gérez et suivez tous les plans d'audit planifiés, en cours ou terminés."
        section="Audit"
        icon={AlertCircle}
        breadcrumbItems={breadcrumbItems}
        searchPlaceholder="Rechercher des plans d'audit..."
        searchValue={searchQuery}
        onSearchChange={(e) => setSearchQuery(e.target.value)}
      />

      {/* Filter Panel */}
      <div className="mb-6 filter-panel-container">
        <FilterPanel
          filters={[
            {
              id: "status",
              label: "Statut",
              component: (
                <Select
                  value={filters.status}
                  onValueChange={(value) => handleFilterChange("status", value)}
                >
                  <SelectTrigger className="h-9">
                    <SelectValue placeholder="Sélectionner statut" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tous</SelectItem>
                    <SelectItem value="Planned">Planifié</SelectItem>
                    <SelectItem value="In Progress">En cours</SelectItem>
                    <SelectItem value="Completed">Terminé</SelectItem>
                  </SelectContent>
                </Select>
              ),
            },
            {
              id: "year",
              label: "Année",
              component: (
                <Select
                  value={filters.year}
                  onValueChange={(value) => handleFilterChange("year", value)}
                >
                  <SelectTrigger className="h-9">
                    <SelectValue placeholder="Sélectionner année" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Toutes</SelectItem>
                    {uniqueYears.filter(y => y !== 'all').map(year => (
                      <SelectItem key={year} value={year}>{year}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              ),
            },
          ]}
          onClearFilters={clearFilters}
        />
      </div>

      {/* Action buttons */}
      <div className="flex justify-end items-center mb-6 gap-2">
        {selectedPlans.length > 0 && (
          <Button
            variant="outline"
            size="sm"
            onClick={handleDeleteSelected}
            className="border-red-500 text-red-500 hover:bg-red-50 flex items-center gap-2"
          >
            <Trash2 className="h-4 w-4" />
                Supprimer ({selectedPlans.length})
          </Button>
        )}

        <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
          <DialogTrigger asChild>
            <Button
              className="bg-[#F62D51]/90 hover:bg-[#F62D51] text-white shadow-md flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              Nouveau Plan
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Créer un Nouveau Plan d'Audit</DialogTitle>
              <DialogDescription>
                Remplissez les informations de base ci-dessous pour créer un nouveau plan d'audit. Les deux champs sont obligatoires.
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleCreatePlan} className="space-y-4 mt-4">
              <div className="space-y-2">
                <Label htmlFor="name">Nom *</Label>
                <Input
                  id="name"
                  name="name"
                  value={newPlan.name}
                  onChange={handleCreateInputChange}
                  placeholder="Entrez le nom du plan"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="calendrier">Calendrier (Année) *</Label>
                <Select
                  name="calendrier"
                  value={newPlan.calendrier}
                  onValueChange={(value) => handleCreateSelectChange('calendrier', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionner l'année" />
                  </SelectTrigger>
                  <SelectContent>
                    {Array.from({ length: 21 }, (_, i) => new Date().getFullYear() - 10 + i).map(year => (
                      <SelectItem key={year} value={year.toString()}>{year}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="flex justify-end gap-2 mt-6">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsCreateModalOpen(false)}
                  disabled={isSubmitting}
                >
                  Annuler
                </Button>
                <Button
                  type="submit"
                  className="bg-[#F62D51] hover:bg-[#F62D51]/90 text-white"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Création...
                    </>
                  ) : (
                    'Créer'
                  )}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden flex-1 mb-16">
        <div className="overflow-x-auto" style={{ maxWidth: '100%', overflowX: 'auto' }}>
          {currentPlans.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12 px-4">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                <AlertCircle className="h-8 w-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Aucun plan d'audit trouvé</h3>
              <p className="text-gray-500 text-center max-w-sm">
                {searchQuery || filters.status !== 'all' || filters.year !== 'all' 
                  ? "Aucun résultat ne correspond à vos critères de recherche. Essayez de modifier vos filtres."
                  : "Commencez par créer votre premier plan d'audit en cliquant sur le bouton 'Nouveau Plan' ci-dessus."}
              </p>
            </div>
          ) : (
          <table className="w-full" style={{ minWidth: '1200px' }}>
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-sm font-medium text-[#555F6D]">
                  <Checkbox
                    checked={selectedPlans.length === currentPlans.length && currentPlans.length > 0}
                    onCheckedChange={handleSelectAll}
                    aria-label="Select all"
                  />
                </th>
                  {columns.map((column) => (
                    <th
                      key={column.key}
                      className="px-6 py-3 text-left text-sm font-medium text-[#555F6D] cursor-pointer hover:bg-gray-100"
                      onClick={() => column.sortable && handleSort(column.key)}
                    >
                      <div className="flex items-center gap-1">
                        {column.label}
                        {sortConfig.key === column.key && (
                          <ArrowUpDown className="w-4 h-4" />
                        )}
                      </div>
                    </th>
                  ))}
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
                {currentPlans.map((plan) => (
                <tr
                  key={plan.id}
                    className="hover:bg-gray-50 cursor-pointer"
                  onClick={() => handleRowClick(plan.id)}
                >
                  <td className="px-6 py-4">
                    <Checkbox
                      checked={selectedPlans.includes(plan.id)}
                      onCheckedChange={() => handleSelectPlan(plan.id)}
                      onClick={(e) => e.stopPropagation()}
                    />
                  </td>
                  <td className="px-6 py-4 text-sm font-bold text-[#242A33] whitespace-nowrap">
                    <span className="flex items-center gap-2">
                      {auditIcon && iconLoaded ? (
                        <img
                          src={auditIcon}
                          alt="audit plan"
                          className="w-5 h-5 object-contain"
                          onError={(e) => {
                            e.target.src = 'https://placehold.co/20x20/F62D51/FFF?text=A';
                            e.target.onerror = null;
                          }}
                        />
                      ) : (
                        <div className="w-5 h-5 bg-[#F62D51] rounded-full flex items-center justify-center text-white text-xs">A</div>
                      )}
                      {plan.name}
                    </span>
                  </td>
                  <td className="px-6 py-4 text-sm whitespace-nowrap">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusLabel(plan.status).color}`}>
                      {getStatusLabel(plan.status).label}
                    </span>
                  </td>
                    <td className="px-6 py-4 text-sm text-gray-600 whitespace-nowrap">
                      {plan.calendrier}
                  </td>
                    <td className="px-6 py-4 text-sm text-gray-600 whitespace-nowrap">
                      {plan.datedebut ? new Date(plan.datedebut).toLocaleDateString() : '-'}
                  </td>
                    <td className="px-6 py-4 text-sm text-gray-600 whitespace-nowrap">
                      {plan.datefin ? new Date(plan.datefin).toLocaleDateString() : '-'}
                  </td>
                    <td className="px-6 py-4 text-sm text-gray-600 whitespace-nowrap">
                      {plan.avancement || '0%'}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          )}
      </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="px-6 py-4 border-t">
      <TablePagination
        currentPage={currentPage}
        totalPages={totalPages}
              onPageChange={handlePageChange}
              itemsPerPage={itemsPerPage}
        totalItems={totalItems}
              onItemsPerPageChange={setItemsPerPage}
            />
          </div>
        )}
      </div>
    </div>
  );
}

export default PlansDauditList;
