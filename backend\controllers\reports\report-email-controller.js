const nodemailer = require('nodemailer');
const { User } = require('../../models');
const puppeteer = require('puppeteer');
const config = require('../../config/config');

/**
 * Get all users for recipient selection
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getEmailRecipients = async (req, res) => {
  try {
    // Get all users with their id, name, and email
    const users = await User.findAll({
      attributes: ['id', 'name', 'username', 'email'],
      order: [['name', 'ASC']]
    });
    
    return res.status(200).json({
      success: true,
      data: users
    });
  } catch (error) {
    console.error('Error fetching email recipients:', error);
    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to fetch email recipients'
    });
  }
};

/**
 * Send a report via email
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.sendReportEmail = async (req, res) => {
  try {
    const { 
      reportType, 
      reportTitle, 
      recipientEmail, 
      recipientUserId,
      reportData, 
      reportFormat = 'pdf',
      message = '' 
    } = req.body;
    
    // Validate required fields
    if (!reportType || (!recipientEmail && !recipientUserId)) {
      return res.status(400).json({
        success: false,
        message: 'Report type and recipient (email or userId) are required'
      });
    }
    
    // Get sender information
    const senderId = req.user.userId || req.user.id;
    const sender = await User.findByPk(senderId);
    
    if (!sender) {
      return res.status(404).json({
        success: false,
        message: 'Sender user not found'
      });
    }
    
    // Get recipient email if userId is provided
    let finalRecipientEmail = recipientEmail;
    let recipientName = 'User';
    
    if (recipientUserId) {
      const recipient = await User.findByPk(recipientUserId);
      if (!recipient || !recipient.email) {
        return res.status(404).json({
          success: false,
          message: 'Recipient user not found or has no email'
        });
      }
      finalRecipientEmail = recipient.email;
      recipientName = recipient.name || recipient.username || recipient.email;
    }
    
    // Create email transporter
    const transporter = nodemailer.createTransport({
      host: config.email.host || 'smtp.gmail.com',
      port: config.email.port || 587,
      secure: config.email.secure || false,
      auth: {
        user: config.email.user,
        pass: config.email.password
      }
    });
    
    // Prepare attachments array
    const attachments = [];
    
    if (reportFormat === 'pdf' && reportData) {
      try {
        // Launch headless browser
        const browser = await puppeteer.launch({
          headless: true,
          args: ['--no-sandbox', '--disable-setuid-sandbox']
        });
        
        const page = await browser.newPage();
        
        // Create HTML content from report data
        let htmlContent = `
          <html>
            <head>
              <title>${reportTitle || reportType}</title>
              <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                h1 { color: #333366; }
                table { border-collapse: collapse; width: 100%; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; }
              </style>
            </head>
            <body>
              <h1>${reportTitle || reportType}</h1>
              <p>Generated on: ${new Date().toLocaleString()}</p>
              <div id="report-content">
        `;
        
        // Add report-specific content
        if (typeof reportData === 'object') {
          if (reportType.includes('risk')) {
            // Risk matrix report
            htmlContent += `<table>
              <tr>
                <th>Risk ID</th>
                <th>Risk Name</th>
                <th>Likelihood</th>
                <th>Impact</th>
                <th>Risk Level</th>
              </tr>`;
            
            // Add rows for each risk
            if (Array.isArray(reportData)) {
              reportData.forEach(risk => {
                htmlContent += `<tr>
                  <td>${risk.id || '-'}</td>
                  <td>${risk.name || '-'}</td>
                  <td>${risk.likelihood || '-'}</td>
                  <td>${risk.impact || '-'}</td>
                  <td>${risk.level || '-'}</td>
                </tr>`;
              });
            }
            
            htmlContent += `</table>`;
          } else if (reportType.includes('incident')) {
            // Incident report
            htmlContent += `<table>
              <tr>
                <th>Incident ID</th>
                <th>Title</th>
                <th>Type</th>
                <th>Status</th>
                <th>Date</th>
              </tr>`;
            
            // Add rows for each incident
            if (Array.isArray(reportData)) {
              reportData.forEach(incident => {
                htmlContent += `<tr>
                  <td>${incident.id || '-'}</td>
                  <td>${incident.title || '-'}</td>
                  <td>${incident.type || '-'}</td>
                  <td>${incident.status || '-'}</td>
                  <td>${incident.date || '-'}</td>
                </tr>`;
              });
            }
            
            htmlContent += `</table>`;
          } else if (reportType.includes('loss')) {
            // Loss report
            htmlContent += `<table>
              <tr>
                <th>Period</th>
                <th>Gross Loss</th>
                <th>Net Loss</th>
              </tr>`;
            
            // Add rows for each period
            if (reportData.labels && reportData.datasets) {
              reportData.labels.forEach((label, index) => {
                htmlContent += `<tr>
                  <td>${label || '-'}</td>
                  <td>${reportData.datasets[0]?.data[index] || '-'}</td>
                  <td>${reportData.datasets[1]?.data[index] || '-'}</td>
                </tr>`;
              });
            }
            
            htmlContent += `</table>`;
          }
        } else {
          // Generic content for other report types
          htmlContent += `<p>Report data is not available in a structured format.</p>`;
        }
        
        htmlContent += `
              </div>
            </body>
          </html>
        `;
        
        await page.setContent(htmlContent);
        
        // Generate PDF
        const pdfBuffer = await page.pdf({
          format: 'A4',
          printBackground: true,
          margin: { top: '20px', right: '20px', bottom: '20px', left: '20px' }
        });
        
        await browser.close();
        
        // Add PDF as attachment
        attachments.push({
          filename: `${reportTitle || reportType}_report.pdf`,
          content: pdfBuffer,
          contentType: 'application/pdf'
        });
      } catch (pdfError) {
        console.error('Error generating PDF:', pdfError);
        // Continue without PDF if there's an error
      }
    }
    
    // Prepare email content
    const mailOptions = {
      from: `"${sender.name || sender.username || 'GRC System'}" <${config.email.user}>`,
      to: finalRecipientEmail,
      subject: `GRC Report: ${reportTitle || reportType}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333366;">GRC Report: ${reportTitle || reportType}</h2>
          <p>Hello ${recipientName},</p>
          <p>${sender.name || sender.username || 'A user'} has shared a GRC report with you.</p>
          ${message ? `<p><strong>Message:</strong> ${message}</p>` : ''}
          <p>The report is attached to this email.</p>
          <p>Thank you,<br>GRC System</p>
        </div>
      `,
      attachments
    };
    
    // Send email
    const info = await transporter.sendMail(mailOptions);
    
    return res.status(200).json({
      success: true,
      message: 'Report email sent successfully',
      messageId: info.messageId
    });
  } catch (error) {
    console.error('Error sending report email:', error);
    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to send report email'
    });
  }
};

