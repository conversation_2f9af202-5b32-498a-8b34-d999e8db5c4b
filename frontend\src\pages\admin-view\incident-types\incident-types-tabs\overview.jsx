import { useOutletContext } from "react-router-dom";
import { Tag, Calendar, FileText, AlertOctagon, Users } from "lucide-react";
import React, { useEffect, useState } from "react";
import axios from "axios";
import { getApiBaseUrl } from "../../../../utils/api-config";

function IncidentTypesOverview() {
  const { incidentType } = useOutletContext();
  const [contributors, setContributors] = useState([]);
  const [loadingContributors, setLoadingContributors] = useState(false);
  const API_BASE_URL = getApiBaseUrl();

  // Helper function to format dates
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString("en-GB", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Fetch contributors when incident type is loaded
  useEffect(() => {
    const fetchContributors = async () => {
      if (!incidentType?.incidentTypeID) return;
      
      setLoadingContributors(true);
      try {
        const response = await axios.get(`${API_BASE_URL}/incident-types/${incidentType.incidentTypeID}/contributors`, {
          withCredentials: true,
          headers: { "Content-Type": "application/json" }
        });
        
        if (response.data.success) {
          setContributors(response.data.data || []);
        }
      } catch (error) {
        console.error("Error loading contributors:", error);
      } finally {
        setLoadingContributors(false);
      }
    };
    
    if (incidentType?.incidentTypeID) {
      fetchContributors();
    }
  }, [incidentType?.incidentTypeID]);

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 gap-6">
        <div>
          <h3 className="text-lg font-semibold text-[#242A33] mb-4">Incident Type Details</h3>
          <div className="space-y-6">
            <div className="flex items-start gap-4">
              <div className="bg-gray-50 p-2 rounded-lg">
                <Tag className="h-5 w-5 text-[#555F6D]" />
              </div>
              <div>
                <p className="text-sm text-[#555F6D]">Name</p>
                <p className="font-medium">{incidentType.name || "N/A"}</p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <div className="bg-gray-50 p-2 rounded-lg">
                <FileText className="h-5 w-5 text-[#555F6D]" />
              </div>
              <div>
                <p className="text-sm text-[#555F6D]">Description</p>
                <p className="font-medium">{incidentType.description || "N/A"}</p>
              </div>
            </div>

            {incidentType.createdAt && (
              <div className="flex items-start gap-4">
                <div className="bg-gray-50 p-2 rounded-lg">
                  <Calendar className="h-5 w-5 text-[#555F6D]" />
                </div>
                <div>
                  <p className="text-sm text-[#555F6D]">Created At</p>
                  <p className="font-medium">{formatDate(incidentType.createdAt)}</p>
                </div>
              </div>
            )}

            {incidentType.updatedAt && (
              <div className="flex items-start gap-4">
                <div className="bg-gray-50 p-2 rounded-lg">
                  <Calendar className="h-5 w-5 text-[#555F6D]" />
                </div>
                <div>
                  <p className="text-sm text-[#555F6D]">Last Updated</p>
                  <p className="font-medium">{formatDate(incidentType.updatedAt)}</p>
                </div>
              </div>
            )}

            {/* New Contributors Section */}
            <div>
              <p className="text-sm font-medium text-gray-500 flex items-center">
                <Users className="h-4 w-4 mr-1 text-indigo-500" />
                Contributors
              </p>
              <div>
                {loadingContributors ? (
                  <p className="text-sm text-gray-400">Loading contributors...</p>
                ) : contributors.length > 0 ? (
                  <div className="flex flex-col space-y-1">
                    {contributors.slice(0, 3).map(contributor => (
                      <p key={contributor.id} className="text-sm">
                        {contributor.contributor?.username || 'Unknown User'}
                      </p>
                    ))}
                    {contributors.length > 3 && (
                      <p className="text-xs text-gray-500">+{contributors.length - 3} more</p>
                    )}
                  </div>
                ) : (
                  <p className="text-sm text-gray-500">None</p>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default IncidentTypesOverview;
