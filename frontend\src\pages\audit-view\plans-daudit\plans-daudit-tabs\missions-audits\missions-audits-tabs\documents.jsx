import React, { useState, useContext } from 'react';

import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Plus, FileText, Save, Upload, Download, File, Trash2, Calendar, Eye, Search } from "lucide-react";
import { toast } from "sonner";
import { useMissionAuditContext } from '@/utils/context-helpers';
import { OutletContext } from '@/pages/audit-view/missions-audits/edit-missions-audits';

function DocumentsTab(props) {
  const contextData = useMissionAuditContext();
  const missionAudit = props.missionAudit || contextData.missionAudit;
  const [documents, setDocuments] = useState([]);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [categoryFilter, setCategoryFilter] = useState("all");
  
  const [newDocument, setNewDocument] = useState({
    title: "",
    description: "",
    category: "Rapport",
    uploadDate: new Date().toISOString().split('T')[0],
    author: "",
    fileSize: "",
    fileName: "",
    status: "Draft",
    version: "1.0"
  });

  // If no mission data is available yet
  if (!missionAudit || !missionAudit.id) {
    return (
      <div className="flex items-center justify-center h-48">
        <p className="text-gray-500">Chargement des documents...</p>
      </div>
    );
  }

  const handleSave = () => {
    toast.success("Documents sauvegardés avec succès");
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewDocument(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name, value) => {
    setNewDocument(prev => ({ ...prev, [name]: value }));
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setNewDocument(prev => ({
        ...prev,
        fileName: file.name,
        fileSize: (file.size / 1024).toFixed(0) + " KB"
      }));
    }
  };

  const handleAddDocument = () => {
    if (!newDocument.title || !newDocument.fileName) {
      toast.error("Veuillez remplir au moins le titre et sélectionner un fichier");
      return;
    }

    // Add new document
    setDocuments(prev => [...prev, { ...newDocument, id: Date.now() }]);
    toast.success("Nouveau document ajouté avec succès");

    // Reset form and close dialog
    setNewDocument({
      title: "",
      description: "",
      category: "Rapport",
      uploadDate: new Date().toISOString().split('T')[0],
      author: "",
      fileSize: "",
      fileName: "",
      status: "Draft",
      version: "1.0"
    });
    setIsDialogOpen(false);
  };

  const handleDeleteDocument = (id) => {
    setDocuments(prev => prev.filter(doc => doc.id !== id));
    toast.success("Document supprimé avec succès");
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case 'Final':
        return <Badge className="bg-green-100 text-green-800">Final</Badge>;
      case 'In Review':
        return <Badge className="bg-yellow-100 text-yellow-800">En revue</Badge>;
      case 'Draft':
        return <Badge className="bg-blue-100 text-blue-800">Brouillon</Badge>;
      default:
        return <Badge variant="outline">-</Badge>;
    }
  };

  const filteredDocuments = documents.filter(doc => {
    const matchesSearch = doc.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         doc.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         doc.author.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = categoryFilter === "all" || doc.category === categoryFilter;
    return matchesSearch && matchesCategory;
  });

  const documentCategories = [
    { id: "Rapport", label: "Rapports" },
    { id: "Planification", label: "Planification" },
    { id: "Evidence", label: "Éléments Probants" },
    { id: "Presentation", label: "Présentations" },
    { id: "Other", label: "Autres" }
  ];

  return (
    <div className="space-y-6 py-4">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-[#1A202C] flex items-center">
          <FileText className="h-6 w-6 mr-3 text-[#F62D51]" />
          Documents
        </h2>
        <div className="flex space-x-2">
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button className="bg-[#F62D51] hover:bg-[#F62D51]/90">
                <Plus className="h-4 w-4 mr-2" />
                Ajouter un Document
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-xl">
              <DialogHeader>
                <DialogTitle>Ajouter un Nouveau Document</DialogTitle>
                <DialogDescription>
                  Importez un document et complétez les informations ci-dessous.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="file">Fichier *</Label>
                  <div className="flex items-center gap-2">
                    <Button type="button" variant="outline" className="w-full justify-start" onClick={() => document.getElementById('file-upload').click()}>
                      <Upload className="h-4 w-4 mr-2" />
                      {newDocument.fileName || "Cliquez pour sélectionner un fichier"}
                    </Button>
                    <Input 
                      id="file-upload" 
                      type="file" 
                      className="hidden" 
                      onChange={handleFileChange}
                    />
                  </div>
                  {newDocument.fileSize && (
                    <p className="text-xs text-gray-500 mt-1">Taille: {newDocument.fileSize}</p>
                  )}
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="title">Titre *</Label>
                  <Input 
                    id="title" 
                    name="title"
                    value={newDocument.title}
                    onChange={handleInputChange}
                    placeholder="Titre du document"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Input 
                    id="description" 
                    name="description"
                    value={newDocument.description}
                    onChange={handleInputChange}
                    placeholder="Description brève du document"
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="category">Catégorie</Label>
                    <Select 
                      name="category"
                      value={newDocument.category} 
                      onValueChange={(value) => handleSelectChange("category", value)}
                    >
                      <SelectTrigger id="category">
                        <SelectValue placeholder="Sélectionner une catégorie" />
                      </SelectTrigger>
                      <SelectContent>
                        {documentCategories.map(category => (
                          <SelectItem key={category.id} value={category.id}>{category.label}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="status">Statut</Label>
                    <Select 
                      name="status"
                      value={newDocument.status} 
                      onValueChange={(value) => handleSelectChange("status", value)}
                    >
                      <SelectTrigger id="status">
                        <SelectValue placeholder="Sélectionner un statut" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Draft">Brouillon</SelectItem>
                        <SelectItem value="In Review">En revue</SelectItem>
                        <SelectItem value="Final">Final</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="author">Auteur</Label>
                    <Input 
                      id="author" 
                      name="author"
                      value={newDocument.author}
                      onChange={handleInputChange}
                      placeholder="Nom de l'auteur"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="version">Version</Label>
                    <Input 
                      id="version" 
                      name="version"
                      value={newDocument.version}
                      onChange={handleInputChange}
                      placeholder="ex: 1.0, 2.1, etc."
                    />
                  </div>
                </div>
              </div>
              
              <div className="flex justify-end gap-2 mt-4">
                <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Annuler
                </Button>
                <Button className="bg-[#F62D51] hover:bg-[#F62D51]/90" onClick={handleAddDocument}>
                  Ajouter
                </Button>
              </div>
            </DialogContent>
          </Dialog>
          
          <Button onClick={handleSave} variant="outline" className="border-[#F62D51] text-[#F62D51]">
            <Save className="h-4 w-4 mr-2" />
            Sauvegarder
          </Button>
        </div>
      </div>

      {/* Search and Filter */}
      <div className="flex flex-col md:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
          <Input 
            placeholder="Rechercher un document..." 
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="w-full md:w-64">
          <Select value={categoryFilter} onValueChange={setCategoryFilter}>
            <SelectTrigger>
              <SelectValue placeholder="Filtrer par catégorie" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Toutes les catégories</SelectItem>
              {documentCategories.map(category => (
                <SelectItem key={category.id} value={category.id}>{category.label}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Bibliothèque de Documents</CardTitle>
        </CardHeader>
        <CardContent>
          {documents.length === 0 ? (
            <div className="text-center py-8 border rounded-md">
              <File className="h-12 w-12 mx-auto text-gray-300 mb-3" />
              <p className="text-gray-500">Aucun document disponible pour cette mission d'audit.</p>
              <p className="text-sm text-gray-400">Cliquez sur "Ajouter un Document" pour télécharger vos premiers documents.</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Document</TableHead>
                  <TableHead>Catégorie</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Statut</TableHead>
                  <TableHead>Taille</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredDocuments.map((doc) => (
                  <TableRow key={doc.id}>
                    <TableCell>
                      <div className="flex items-start space-x-3">
                        <File className="h-5 w-5 text-blue-500 mt-0.5" />
                        <div>
                          <div className="font-medium">{doc.title}</div>
                          <div className="text-xs text-gray-500">{doc.fileName}</div>
                          {doc.author && <div className="text-xs text-gray-500">Par: {doc.author}</div>}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>{doc.category}</TableCell>
                    <TableCell>
                      <div className="flex items-center text-sm">
                        <Calendar className="h-3.5 w-3.5 mr-1 text-gray-500" />
                        {new Date(doc.uploadDate).toLocaleDateString('fr-FR')}
                      </div>
                      <div className="text-xs text-gray-500 mt-1">v{doc.version}</div>
                    </TableCell>
                    <TableCell>{getStatusBadge(doc.status)}</TableCell>
                    <TableCell>{doc.fileSize}</TableCell>
                    <TableCell>
                      <div className="flex justify-end space-x-2">
                        <Button 
                          variant="ghost" 
                          size="icon" 
                          className="h-8 w-8 text-gray-500"
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="icon" 
                          className="h-8 w-8 text-blue-500"
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="icon" 
                          className="h-8 w-8 text-red-500"
                          onClick={() => handleDeleteDocument(doc.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

export default DocumentsTab; 