import React, { useState, useEffect, useRef } from 'react';
import { Doughn<PERSON> } from 'react-chartjs-2';
import axios from 'axios';
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from 'chart.js';
import { jsPDF } from "jspdf";
import * as XLSX from "https://cdn.jsdelivr.net/npm/xlsx@0.18.5/xlsx.mjs";
import { Button } from "@/components/ui/button";
import { getApiBaseUrl } from "@/utils/api-config";
import EmailReportModal from '@/components/reports/EmailReportModal';
// Register Chart.js components
ChartJS.register(ArcElement, Tooltip, Legend);

const IncidentsByType = (props) => {
  // Add state for email modal
  const [isEmailModalOpen, setIsEmailModalOpen] = useState(false);
  
  // Add function to handle email button click
  const handleEmailReport = () => {
    setIsEmailModalOpen(true);
  };
  const [chartData, setChartData] = useState(null);
  const [counts, setCounts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const chartRef = useRef(null); // Reference to the chart instance
  const API_BASE_URL = getApiBaseUrl();
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const response = await axios.get(`${API_BASE_URL}/incidentsByType`, {
          withCredentials: true,
          headers: { 'x-user-id': '1' },
        });
        const data = response.data.data;

        const labels = data.map(item => item.type_name || `Type ${item.incidentTypeID}`);
        const countsData = data.map(item => item.incident_count);
        const backgroundColors = data.map((_, index) => {
          const colors = ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40'];
          return colors[index % colors.length];
        });

        setChartData({
          labels,
          datasets: [
            {
              label: "Number of Incidents",
              data: countsData,
              backgroundColor: backgroundColors,
              borderColor: backgroundColors,
              borderWidth: 1,
            },
          ],
        });

        setCounts(data);
      } catch (err) {
        console.error('Error fetching incidents by type:', err);
        setError('Failed to load data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const options = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: "Number of Incidents by Incident Type",
      },
    },
  };

  const downloadPDF = async () => {
    try {
      if (!chartRef.current) {
        console.error("Chart reference is not available.");
        alert("Error: Unable to generate PDF. The chart is not ready.");
        return;
      }

      // Get the chart as a base64 image
      const chartImage = chartRef.current.toBase64Image();
      console.log("Chart image generated successfully.");

      // Create a new jsPDF instance
      const pdf = new jsPDF({
        orientation: 'landscape',
        unit: 'in',
        format: 'letter',
      });

      // Define page dimensions
      const pageWidth = pdf.internal.pageSize.getWidth(); // 11 inches
      const pageHeight = pdf.internal.pageSize.getHeight(); // 8.5 inches
      const margin = 0.5;
      const maxWidth = pageWidth - 2 * margin; // 10 inches
      const maxHeight = pageHeight - 2 * margin - 2; // Reserve 2 inches for Details section

      // Calculate image dimensions while maintaining aspect ratio
      const imgProps = pdf.getImageProperties(chartImage);
      let pdfWidth = maxWidth;
      let pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;

      // Scale down if the height exceeds the maximum height
      if (pdfHeight > maxHeight) {
        pdfHeight = maxHeight;
        pdfWidth = (imgProps.width * pdfHeight) / imgProps.height;
      }

      // Add the chart image to the PDF
      pdf.addImage(chartImage, 'JPEG', margin, margin, pdfWidth, pdfHeight);

      // Add the Details section as text
      let yPosition = pdfHeight + margin + 0.5; // Start below the chart
      pdf.setFontSize(16);
      pdf.setTextColor(45, 55, 72); // RGB equivalent of #2D3748
      pdf.text('Details', margin, yPosition);
      yPosition += 0.5;

      // Total Incidents
      pdf.setFontSize(12);
      pdf.setTextColor(113, 128, 150); // RGB equivalent of #718096
      pdf.text("Total Number of Incidents:", margin, yPosition);
      pdf.setTextColor(26, 41, 66); // RGB equivalent of #1A2942
      pdf.setFont('helvetica', 'bold');
      pdf.text(`${totalIncidents}`, margin + 3, yPosition);
      yPosition += 0.3;

      // Most Frequent Type
      pdf.setFont('helvetica', 'normal');
      pdf.setTextColor(113, 128, 150); // RGB equivalent of #718096
      pdf.text('Most Frequent Type:', margin, yPosition);
      pdf.setTextColor(26, 41, 66); // RGB equivalent of #1A2942
      pdf.setFont('helvetica', 'bold');
      pdf.text(`${mostFrequentType}`, margin + 3, yPosition);
      yPosition += 0.5;

      // List of Incident Types and Counts
      pdf.setFont('helvetica', 'normal');
      pdf.setTextColor(55, 65, 81); // RGB equivalent of #374151
      pdf.setFontSize(11);
      counts.forEach((item, index) => {
        if (yPosition > pageHeight - margin) {
          pdf.addPage();
          yPosition = margin;
        }
        pdf.text(
          `${item.type_name || `Type ${item.incidentTypeID}`}: ${item.incident_count} incident(s)`,
          margin + 0.2,
          yPosition
        );
        yPosition += 0.3;
      });

      // Save the PDF
      pdf.save('incidents_by_type.pdf');
      console.log("PDF generated and downloaded successfully.");
    } catch (err) {
      console.error("Error generating PDF:", err);
      alert("Error generating PDF. Please check the console for details.");
    }
  };

  const downloadExcel = () => {
    if (!chartData) return;
    const excelData = chartData.labels.map((label, index) => ({
      Type: label,
      Incidents: chartData.datasets[0].data[index],
    }));
    const worksheet = XLSX.utils.json_to_sheet(excelData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "IncidentsByType");
    XLSX.writeFile(workbook, "incidents_by_type.xlsx");
  };

  const totalIncidents = counts.reduce((sum, item) => sum + item.incident_count, 0);
  const mostFrequentType = counts.length > 0
    ? counts.reduce((max, item) => item.incident_count > max.incident_count ? item : max, counts[0]).type_name || `Type ${counts[0]?.incidentTypeID}`
    : "N/A";

  if (loading) return <div className="p-6 text-center">Loading...</div>;
  if (error) return <div className="p-6 text-center text-red-500">{error}</div>;

  return (
    <div className="p-6 bg-white rounded-lg shadow border border-gray-200">
      <div id="incidents-by-type-chart">
        <div className="w-full max-w-md mx-auto">
          <Doughnut ref={chartRef} data={chartData} options={options} />
        </div>
        <div className="mt-4">
          <h3 className="text-lg font-semibold">Details of Incidents by Type:</h3>
          <ul className="list-disc pl-5">
            {counts.map((item, index) => (
              <li key={index}>
                {item.type_name || `Type ${item.incidentTypeID}`}: {item.incident_count} incident(s)
              </li>
            ))}
          </ul>
        </div>
      </div>
      <div className="flex gap-4 mt-4">
        <Button onClick={downloadPDF} className="bg-[#22c55e] hover:bg-[#16a34a] text-white">
          Download PDF
        </Button>
        <Button onClick={downloadExcel} className="bg-[#22c55e] hover:bg-[#16a34a] text-white">
          Download Excel
        </Button>
        <Button
          onClick={handleEmailReport}
          className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
            <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
          </svg>
          Send via Email
        </Button>
      </div>
      <div className="mt-6 bg-gray-50 rounded-xl p-4 border border-gray-200">
        <h3 className="text-lg font-semibold mb-3 text-gray-800">Details</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
            <p className="text-sm text-gray-500 mb-1">Total Number of Incidents</p>
            <p className="text-xl font-bold text-[#1A2942]">{totalIncidents}</p>
          </div>
          <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
            <p className="text-sm text-gray-500 mb-1">Most Frequent Type</p>
            <p className="text-xl font-bold text-[#1A2942]">{mostFrequentType}</p>
          </div>
        </div>
      </div>
      <EmailReportModal
        isOpen={isEmailModalOpen}
        onClose={() => setIsEmailModalOpen(false)}
        reportType="incidents-by-type"
        reportTitle="Incidents by Incident Type"
        reportData={chartData}
      />
    </div>
  );
};

export default IncidentsByType;
