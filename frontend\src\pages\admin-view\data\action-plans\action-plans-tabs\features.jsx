import { useState, useEffect } from "react";
import { useOutletContext, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { Loader2, User } from "lucide-react";
import { updateActionPlan } from "@/store/slices/actionPlanSlice";
import { getAllUsers } from "@/store/slices/userSlice";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import { useTranslation } from "react-i18next";

function ActionPlanFeatures() {
  const { actionPlan } = useOutletContext();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { t } = useTranslation();

  // Get users from Redux store
  const { users, isLoading: isLoadingUsers } = useSelector((state) => state.user);

  const [submitting, setSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    ...actionPlan,
    plannedBeginDate: actionPlan.plannedBeginDate ? new Date(actionPlan.plannedBeginDate).toISOString().split('T')[0] : '',
    plannedEndDate: actionPlan.plannedEndDate ? new Date(actionPlan.plannedEndDate).toISOString().split('T')[0] : '',
    approverId: actionPlan.approverId || (actionPlan.approver ? actionPlan.approver.id : 'none'),
    assigneeId: actionPlan.assigneeId || (actionPlan.assignee ? actionPlan.assignee.id : 'none')
  });

  // Fetch users on component mount
  useEffect(() => {
    dispatch(getAllUsers());
  }, [dispatch]);

  // Update form data when actionPlan changes
  useEffect(() => {
    setFormData({
      ...actionPlan,
      plannedBeginDate: actionPlan.plannedBeginDate ? new Date(actionPlan.plannedBeginDate).toISOString().split('T')[0] : '',
      plannedEndDate: actionPlan.plannedEndDate ? new Date(actionPlan.plannedEndDate).toISOString().split('T')[0] : '',
      approverId: actionPlan.approverId || (actionPlan.approver ? actionPlan.approver.id : 'none'),
      assigneeId: actionPlan.assigneeId || (actionPlan.assignee ? actionPlan.assignee.id : 'none')
    });
  }, [actionPlan]);

  // Handle input change
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle select change
  const handleSelectChange = (name, value) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      setSubmitting(true);

      const result = await dispatch(updateActionPlan({
        id: actionPlan.actionPlanID,
        actionPlanData: formData
      })).unwrap();

      // Show success toast without refreshing
      toast.success(t('admin.action_plans.features.update_success', 'Action plan updated successfully'), {
        duration: 3000 // Show for 3 seconds
      });

      // Update local form data with the result from the API
      if (result && result.data) {
        setFormData({
          ...result.data,
          plannedBeginDate: result.data.plannedBeginDate ? new Date(result.data.plannedBeginDate).toISOString().split('T')[0] : '',
          plannedEndDate: result.data.plannedEndDate ? new Date(result.data.plannedEndDate).toISOString().split('T')[0] : ''
        });
      }
    } catch (error) {
      console.error("Error updating action plan:", error);
      toast.error(t('admin.action_plans.features.update_error', 'Failed to update action plan'));
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div>
      <h2 className="text-xl font-semibold mb-6">{t('admin.action_plans.tabs.features', 'Features')}</h2>

      <form onSubmit={handleSubmit} className="space-y-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="flex flex-col md:col-span-2">
            <Label htmlFor="name" className="mb-2">{t('admin.action_plans.form.name', 'Name')} *</Label>
            <Input
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              placeholder={t('admin.action_plans.form.name_placeholder', 'Action plan name')}
              required
              className="w-full"
            />
          </div>

          <div className="flex flex-col md:col-span-1">
            <Label htmlFor="approverId" className="mb-2 flex items-center">
              <User className="h-4 w-4 mr-1" />
              {t('admin.action_plans.overview.approver', 'Approver')}
            </Label>
            <Select
              value={formData.approverId ? formData.approverId.toString() : 'none'}
              onValueChange={(value) => handleSelectChange("approverId", value !== 'none' ? parseInt(value) : null)}
            >
              <SelectTrigger id="approverId" className="w-full">
                <SelectValue placeholder={t('admin.action_plans.filters.select_approver', 'Select approver')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">{t('common.none', 'None')}</SelectItem>
                {users.map((user) => (
                  <SelectItem key={user.id} value={user.id.toString()}>
                    {user.username} ({user.role})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="flex flex-col md:col-span-1">
            <Label htmlFor="assigneeId" className="mb-2 flex items-center">
              <User className="h-4 w-4 mr-1" />
              {t('admin.action_plans.overview.assignee', 'Assignee')}
            </Label>
            <Select
              value={formData.assigneeId ? formData.assigneeId.toString() : 'none'}
              onValueChange={(value) => handleSelectChange("assigneeId", value !== 'none' ? parseInt(value) : null)}
            >
              <SelectTrigger id="assigneeId" className="w-full">
                <SelectValue placeholder={t('admin.action_plans.filters.select_assignee', 'Select assignee')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">{t('common.none', 'None')}</SelectItem>
                {users.map((user) => (
                  <SelectItem key={user.id} value={user.id.toString()}>
                    {user.username} ({user.role})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="flex flex-col">
            <Label htmlFor="category" className="mb-2">{t('admin.action_plans.columns.category', 'Category')}</Label>
            <Input
              id="category"
              name="category"
              value={formData.category || ''}
              onChange={handleInputChange}
              placeholder={t('admin.action_plans.filters.select_category', 'Enter category')}
              className="w-full"
            />
          </div>

          <div className="flex flex-col">
            <Label htmlFor="nature" className="mb-2">{t('admin.action_plans.columns.nature', 'Nature')}</Label>
            <Select
              value={formData.nature || ''}
              onValueChange={(value) => handleSelectChange("nature", value)}
            >
              <SelectTrigger id="nature" className="w-full">
                <SelectValue placeholder={t('admin.action_plans.filters.select_nature', 'Select nature')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Preventive">{t('admin.action_plans.nature.preventive', 'Preventive')}</SelectItem>
                <SelectItem value="Corrective">{t('admin.action_plans.nature.corrective', 'Corrective')}</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex flex-col">
            <Label htmlFor="origin" className="mb-2">{t('admin.action_plans.columns.origin', 'Origin')}</Label>
            <Select
              value={formData.origin || ''}
              onValueChange={(value) => handleSelectChange("origin", value)}
            >
              <SelectTrigger id="origin" className="w-full">
                <SelectValue placeholder={t('admin.action_plans.filters.select_origin', 'Select origin')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Audit">{t('admin.action_plans.origin.audit', 'Audit')}</SelectItem>
                <SelectItem value="Compliance">{t('admin.action_plans.origin.compliance', 'Compliance')}</SelectItem>
                <SelectItem value="Event">{t('admin.action_plans.origin.event', 'Event')}</SelectItem>
                <SelectItem value="Risk">{t('admin.action_plans.origin.risk', 'Risk')}</SelectItem>
                <SelectItem value="RFC">{t('admin.action_plans.origin.rfc', 'RFC')}</SelectItem>
                <SelectItem value="Other">{t('admin.action_plans.origin.other', 'Other')}</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex flex-col">
            <Label htmlFor="priority" className="mb-2">{t('admin.action_plans.columns.priority', 'Priority')}</Label>
            <Select
              value={formData.priority || ''}
              onValueChange={(value) => handleSelectChange("priority", value)}
            >
              <SelectTrigger id="priority" className="w-full">
                <SelectValue placeholder={t('admin.action_plans.filters.select_priority', 'Select priority')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Low">{t('common.priority.low', 'Low')}</SelectItem>
                <SelectItem value="Medium">{t('common.priority.medium', 'Medium')}</SelectItem>
                <SelectItem value="High">{t('common.priority.high', 'High')}</SelectItem>
                <SelectItem value="Critical">{t('common.priority.critical', 'Critical')}</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex flex-col">
            <Label htmlFor="organizationalLevel" className="mb-2">{t('admin.action_plans.columns.organizational_level', 'Organizational Level')}</Label>
            <Select
              value={formData.organizationalLevel || ''}
              onValueChange={(value) => handleSelectChange("organizationalLevel", value)}
            >
              <SelectTrigger id="organizationalLevel" className="w-full">
                <SelectValue placeholder={t('admin.action_plans.filters.select_organizational_level', 'Select organizational level')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Local">{t('admin.action_plans.organizational_level.local', 'Local')}</SelectItem>
                <SelectItem value="Global">{t('admin.action_plans.organizational_level.global', 'Global')}</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex flex-col">
            <Label htmlFor="means" className="mb-2">{t('admin.action_plans.columns.means', 'Means')}</Label>
            <Input
              id="means"
              name="means"
              value={formData.means || ''}
              onChange={handleInputChange}
              placeholder={t('admin.action_plans.filters.select_means', 'Enter means')}
              className="w-full"
            />
          </div>

          <div className="flex flex-col">
            <Label htmlFor="plannedBeginDate" className="mb-2">{t('admin.action_plans.columns.planned_begin_date', 'Planned Begin Date')}</Label>
            <Input
              id="plannedBeginDate"
              name="plannedBeginDate"
              type="date"
              value={formData.plannedBeginDate || ''}
              onChange={handleInputChange}
              className="w-full"
            />
          </div>

          <div className="flex flex-col">
            <Label htmlFor="plannedEndDate" className="mb-2">{t('admin.action_plans.columns.planned_end_date', 'Planned End Date')}</Label>
            <Input
              id="plannedEndDate"
              name="plannedEndDate"
              type="date"
              value={formData.plannedEndDate || ''}
              onChange={handleInputChange}
              className="w-full"
            />
          </div>
        </div>

        <div className="flex flex-col">
          <Label htmlFor="comment" className="mb-2">{t('admin.action_plans.columns.comment', 'Comment')}</Label>
          <Textarea
            id="comment"
            name="comment"
            value={formData.comment || ''}
            onChange={handleInputChange}
            placeholder={t('admin.action_plans.form.comment_placeholder', 'Enter comment')}
            className="w-full h-24 resize-y"
          />
        </div>

        <div className="flex justify-end gap-4">
          <Button
            type="submit"
            className="bg-[#F62D51] hover:bg-[#d42a49] text-white"
            disabled={submitting}
          >
            {submitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {t('common.updating', 'Updating...')}
              </>
            ) : (
              t('admin.action_plans.features.update_action_plan', 'Update Action Plan')
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}

export default ActionPlanFeatures;
