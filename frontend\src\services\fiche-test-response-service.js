import axios from 'axios';
import { getApiBaseUrl } from '@/utils/api-config';

const apiInstance = axios.create({
  baseURL: `${getApiBaseUrl()}/audit/fiches-de-test-responses`,
  timeout: 10000,
  withCredentials: true
});

// Get all responses for a fiche de travail
export const getResponsesByFicheId = async (ficheDeTravailID, signal) => {
  try {
    const response = await apiInstance.get(`/fiche/${ficheDeTravailID}`, { signal });
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    throw error;
  }
};

// Get response by ID
export const getResponseById = async (id, signal) => {
  try {
    const response = await apiInstance.get(`/${id}`, { signal });
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    throw error;
  }
};

// Create or update a response (upsert)
export const upsertResponse = async (responseData, signal) => {
  try {
    const response = await apiInstance.post('/upsert', responseData, { signal });
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    throw error;
  }
};

// Bulk upsert responses
export const bulkUpsertResponses = async (bulkData, signal) => {
  try {
    const response = await apiInstance.post('/bulk-upsert', bulkData, { signal });
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    throw error;
  }
};

// Delete a response
export const deleteResponse = async (id, signal) => {
  try {
    const response = await apiInstance.delete(`/${id}`, { signal });
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    throw error;
  }
};

// Delete all responses for a fiche de travail
export const deleteResponsesByFicheId = async (ficheDeTravailID, signal) => {
  try {
    const response = await apiInstance.delete(`/fiche/${ficheDeTravailID}`, { signal });
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    throw error;
  }
}; 