'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Check if ficheDeTravailID column already exists
    const tableInfo = await queryInterface.describeTable('FicheDeTestAttachments');
    
    // Add ficheDeTravailID column if it doesn't exist
    if (!tableInfo.ficheDeTravailID) {
      await queryInterface.addColumn('FicheDeTestAttachments', 'ficheDeTravailID', {
        type: Sequelize.STRING,
        allowNull: true, // Initially allow null to avoid breaking existing records
      });
    }

    // Check the actual table name in the database
    const [tables] = await queryInterface.sequelize.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name ILIKE '%fichedetest%' 
      AND table_name != 'FicheDeTestAttachments';
    `);
    
    const ficheDeTestTableName = tables.length > 0 ? tables[0].table_name : 'FicheDeTests';
    
    // Update existing records to set ficheDeTravailID based on ficheDeTestID
    await queryInterface.sequelize.query(`
      UPDATE "FicheDeTestAttachments" AS fta
      SET "ficheDeTravailID" = fdt."ficheDeTravailID"
      FROM "${ficheDeTestTableName}" AS fdt
      WHERE fta."ficheDeTestID" = fdt."id"
      AND fta."ficheDeTravailID" IS NULL
    `);

    // Make ficheDeTravailID non-nullable after updating existing records
    await queryInterface.changeColumn('FicheDeTestAttachments', 'ficheDeTravailID', {
      type: Sequelize.STRING,
      allowNull: false,
    });

    // Make ficheDeTestID nullable if it's not already
    if (tableInfo.ficheDeTestID && !tableInfo.ficheDeTestID.allowNull) {
      await queryInterface.changeColumn('FicheDeTestAttachments', 'ficheDeTestID', {
        type: Sequelize.STRING,
        allowNull: true,
      });
    }

    // Add index for better performance if it doesn't exist
    try {
      await queryInterface.addIndex('FicheDeTestAttachments', ['ficheDeTravailID'], {
        name: 'idx_fiche_de_test_attachments_fiche_de_travail_id'
      });
    } catch (error) {
      // Index might already exist, which is fine
      console.log('Index might already exist:', error.message);
    }
  },

  down: async (queryInterface, Sequelize) => {
    // Remove index
    try {
      await queryInterface.removeIndex('FicheDeTestAttachments', 'idx_fiche_de_test_attachments_fiche_de_travail_id');
    } catch (error) {
      console.log('Index might not exist:', error.message);
    }

    // Make ficheDeTestID non-nullable again
    await queryInterface.changeColumn('FicheDeTestAttachments', 'ficheDeTestID', {
      type: Sequelize.STRING,
      allowNull: false,
    });

    // Remove ficheDeTravailID column
    await queryInterface.removeColumn('FicheDeTestAttachments', 'ficheDeTravailID');
  }
};

// npx sequelize-cli db:migrate --name 20240601000000-update-fiche-de-test-attachment.js

