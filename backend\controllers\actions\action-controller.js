const db = require('../../models');
const { Action, User, ActionPlan } = db;
const { v4: uuidv4 } = require('uuid');

// Get all actions
const getAllActions = async (req, res) => {
  try {
    const actions = await Action.findAll({
      include: [{
        model: User,
        as: 'assignee',
        attributes: ['id', 'username', 'email'],
        include: [
          {
            model: db.Role,
            as: 'roles',
            through: { attributes: [] },
            attributes: ['id', 'name', 'code']
          }
        ]
      }]
    });

    res.json({
      success: true,
      data: actions
    });
  } catch (error) {
    console.error('Error fetching all actions:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch actions'
    });
  }
};

// Get all actions for an action plan
const getActionsByActionPlanId = async (req, res) => {
  try {
    const { actionPlanId } = req.params;

    const actions = await Action.findAll({
      include: [
        {
          model: User,
          as: 'assignee',
          attributes: ['id', 'username', 'email'],
          include: [
            {
              model: db.Role,
              as: 'roles',
              through: { attributes: [] },
              attributes: ['id', 'name', 'code']
            }
          ]
        },
        {
          model: ActionPlan,
          as: 'actionPlans',
          where: { actionPlanID: actionPlanId },
          attributes: ['actionPlanID']
        }
      ]
    });

    res.json({
      success: true,
      data: actions
    });
  } catch (error) {
    console.error('Error fetching actions:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch actions'
    });
  }
};

// Get action by ID
const getActionById = async (req, res) => {
  try {
    const { id } = req.params;

    const action = await Action.findByPk(id, {
      include: [{
        model: User,
        as: 'assignee',
        attributes: ['id', 'username', 'email'],
        include: [
          {
            model: db.Role,
            as: 'roles',
            through: { attributes: [] },
            attributes: ['id', 'name', 'code']
          }
        ]
      }]
    });

    if (!action) {
      return res.status(404).json({
        success: false,
        message: 'Action not found'
      });
    }

    res.json({
      success: true,
      data: action
    });
  } catch (error) {
    console.error('Error fetching action:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch action'
    });
  }
};

// Create new action
const createAction = async (req, res) => {
  try {
    const {
      name,
      priority,
      status,
      description,
      startDate,
      endDate,
      actionPlanID,
      assigneeId
    } = req.body;

    // Validate required fields
    if (!name || !actionPlanID) {
      return res.status(400).json({
        success: false,
        message: 'Name and action plan ID are required',
        missingFields: ['name', 'actionPlanID'].filter(field => !req.body[field])
      });
    }

    const action = await Action.create({
      actionID: `ACT_${uuidv4().substring(0, 8)}`,
      name,
      priority: priority || 'Low',
      status: status || 'Not Started',
      description: description || null,
      startDate: startDate || null,
      endDate: endDate || null,
      actionPlanID,
      assigneeId: assigneeId || null
    });

    // Fetch the created action with assignee information
    const createdAction = await Action.findByPk(action.actionID, {
      include: [{
        model: User,
        as: 'assignee',
        attributes: ['id', 'username', 'email'],
        include: [
          {
            model: db.Role,
            as: 'roles',
            through: { attributes: [] },
            attributes: ['id', 'name', 'code']
          }
        ]
      }]
    });

    res.status(201).json({
      success: true,
      message: 'Action created successfully',
      data: createdAction
    });
  } catch (error) {
    console.error('Error creating action:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create action'
    });
  }
};

// Update action
const updateAction = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      priority,
      status,
      description,
      startDate,
      endDate,
      actionPlanID,
      assigneeId
    } = req.body;

    const action = await Action.findByPk(id, {
      include: [{
        model: User,
        as: 'assignee',
        attributes: ['id', 'username', 'email'],
        include: [
          {
            model: db.Role,
            as: 'roles',
            through: { attributes: [] },
            attributes: ['id', 'name', 'code']
          }
        ]
      }]
    });

    if (!action) {
      return res.status(404).json({
        success: false,
        message: 'Action not found'
      });
    }

    // Update basic fields
    await action.update({
      name: name || action.name,
      priority: priority !== undefined ? priority : action.priority,
      status: status !== undefined ? status : action.status,
      description: description !== undefined ? description : action.description,
      startDate: startDate !== undefined ? startDate : action.startDate,
      endDate: endDate !== undefined ? endDate : action.endDate,
      assigneeId: assigneeId !== undefined ? assigneeId : action.assigneeId
    });

    // If actionPlanID is provided, update the relationship
    if (actionPlanID) {
      const actionPlan = await ActionPlan.findByPk(actionPlanID);
      if (!actionPlan) {
        return res.status(404).json({
          success: false,
          message: 'Action plan not found'
        });
      }
      await action.addActionPlan(actionPlan);
    }

    // Fetch the updated action with all relationships
    const updatedAction = await Action.findByPk(id, {
      include: [
        {
          model: User,
          as: 'assignee',
          attributes: ['id', 'username', 'email'],
          include: [
            {
              model: db.Role,
              as: 'roles',
              through: { attributes: [] },
              attributes: ['id', 'name', 'code']
            }
          ]
        },
        {
          model: ActionPlan,
          as: 'actionPlans',
          attributes: ['actionPlanID']
        }
      ]
    });

    res.json({
      success: true,
      message: 'Action updated successfully',
      data: updatedAction
    });
  } catch (error) {
    console.error('Error updating action:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update action'
    });
  }
};

// Delete action
const deleteAction = async (req, res) => {
  try {
    const { id } = req.params;

    const action = await Action.findByPk(id);

    if (!action) {
      return res.status(404).json({
        success: false,
        message: 'Action not found'
      });
    }

    await action.destroy();

    res.json({
      success: true,
      message: 'Action deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting action:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete action'
    });
  }
};

// Delete multiple actions
const deleteMultipleActions = async (req, res) => {
  try {
    const { ids } = req.body;

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No action IDs provided'
      });
    }

    const result = await Action.destroy({
      where: {
        actionID: ids
      }
    });

    res.json({
      success: true,
      message: `${result} actions deleted successfully`
    });
  } catch (error) {
    console.error('Error deleting actions:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete actions'
    });
  }
};

module.exports = {
  getAllActions,
  getActionsByActionPlanId,
  getActionById,
  createAction,
  updateAction,
  deleteAction,
  deleteMultipleActions
};
