const { User } = require('../../models');

// Get all users (public version with limited fields)
const getAllUsersPublic = async (req, res) => {
    try {
        const users = await User.findAll({
            attributes: ['id', 'username', 'email'],
            include: [
                {
                    model: require('../../models').Role,
                    as: 'roles',
                    through: { attributes: [] },
                    attributes: ['id', 'name', 'code']
                }
            ]
        });

        // Transform the data to include a virtual role field for backward compatibility
        const transformedUsers = users.map(user => {
            const userData = user.toJSON();

            // Determine primary role for backward compatibility
            let primaryRole = 'user'; // Default role

            if (userData.roles && userData.roles.length > 0) {
                // Priority order: GRC Administrator > GRC Manager > Risk Manager > GRC Contributor
                const rolePriority = {
                    'grc_admin': 1,
                    'grc_manager': 2,
                    'risk_manager': 3,
                    'grc_contributor': 4
                };

                // Sort roles by priority and get the highest priority role
                const sortedRoles = [...userData.roles].sort((a, b) =>
                    (rolePriority[a.code] || 999) - (rolePriority[b.code] || 999)
                );

                // Map new role codes to old role names for backward compatibility
                const roleCodeMap = {
                    'grc_admin': 'super_admin',
                    'grc_manager': 'admin',
                    'risk_manager': 'admin', // Map Risk Manager to admin for backward compatibility
                    'grc_contributor': 'user'
                };

                primaryRole = roleCodeMap[sortedRoles[0].code] || 'user';
            }

            // Add virtual role field
            userData.role = primaryRole;

            return userData;
        });

        res.status(200).json({
            success: true,
            data: transformedUsers
        });
    } catch (error) {
        console.error('Error fetching users:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch users'
        });
    }
};

module.exports = {
    getAllUsersPublic
};
