import { useState, useEffect, useRef } from "react";
import { useOutletContext, useNavigate } from "react-router-dom";
import { <PERSON><PERSON><PERSON>, Loader2 } from "lucide-react";
import axios from "axios";
import jsPDF from "jspdf";
import * as XLSX from "xlsx";
import { Doughnut } from "react-chartjs-2";
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from "chart.js";
import { getApiBaseUrl } from "@/utils/api-config";
import { useTranslation } from "react-i18next";
// Register Chart.js components
ChartJS.register(ArcElement, Tooltip, Legend);

// Custom plugin to display progression percentage in the center of the doughnut chart
const centerTextPlugin = {
  id: 'centerText',
  beforeDraw(chart) {
    const { ctx, chartArea } = chart;
    const completedCount = chart.config.data.datasets[0].data[chart.config.data.labels.indexOf("Completed")] || 0;
    const total = chart.config.data.datasets[0].data.reduce((sum, val) => sum + val, 0);
    const progression = total > 0 ? ((completedCount / total) * 100).toFixed(1) : 0;

    ctx.save();
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.font = 'bold 20px Arial';
    ctx.fillStyle = '#1A2942';
    const centerX = (chartArea.left + chartArea.right) / 2;
    const centerY = (chartArea.top + chartArea.bottom) / 2;
    ctx.fillText(`${progression}% Complété`, centerX, centerY);
    ctx.restore();
  }
};
const API_BASE_URL = getApiBaseUrl();
// Ensure Frappe Gantt script is loaded synchronously and patched immediately
const loadFrappeGantt = () => {
  return new Promise((resolve, reject) => {
    if (window.Gantt) {
      patchFrappeGantt();
      resolve();
      return;
    }

    const script = document.createElement("script");
    script.src = "https://cdn.jsdelivr.net/npm/frappe-gantt@0.6.1/dist/frappe-gantt.min.js";
    script.async = false;

    script.onload = () => {
      if (window.Gantt) {
        patchFrappeGantt();
        resolve();
      } else {
        reject(new Error("Frappe Gantt not available after script load"));
      }
    };

    script.onerror = () => reject(new Error("Failed to load Frappe Gantt"));
    document.head.appendChild(script);
  });
};

// Patch Frappe Gantt to disable labels and handle getBBox errors
const patchFrappeGantt = () => {
  console.log("Attempting to patch Frappe Gantt...");
  if (!window.Gantt || !window.Gantt.prototype) {
    console.warn("Frappe Gantt or its prototype is not defined. Skipping patch.");
    return;
  }

  if (!window.Gantt.prototype.Bar) {
    console.warn("Frappe Gantt Bar class is undefined, possibly due to version mismatch or script loading issue. Skipping patch.");
    return;
  }

  const createDummyElement = () => ({
    getBBox: () => ({ x: 0, y: 0, width: 0, height: 0 }),
    setAttribute: () => {},
    style: {},
    remove: () => {},
  });

  const originalBar = window.Gantt.prototype.Bar;

  window.Gantt.prototype.Bar = function(...args) {
    const bar = new originalBar(...args);
    bar.$bar_label = createDummyElement();
    bar.draw_label = () => {};
    bar.update_label_position = () => {};
    bar.make_label = () => createDummyElement();
    return bar;
  };

  window.Gantt.prototype.Bar.prototype = Object.create(originalBar.prototype);
  window.Gantt.prototype.Bar.prototype.constructor = window.Gantt.prototype.Bar;

  window.Gantt.prototype.Bar.prototype.draw_label = () => {};
  window.Gantt.prototype.Bar.prototype.update_label_position = () => {};
  window.Gantt.prototype.Bar.prototype.make_label = () => createDummyElement();

  console.log("Frappe Gantt patched successfully.");
};

function ActionPlanProgress() {
  const { actionPlan } = useOutletContext();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [actions, setActions] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [viewMode, setViewMode] = useState("Day");
  const [isPDFButtonDisabled, setIsPDFButtonDisabled] = useState(false);
  const ganttRef = useRef(null);
  const ganttInstanceRef = useRef(null);
  const chartContainerRef = useRef(null);
  const doughnutChartRef = useRef(null);
  const isDownloadingPDF = useRef(false);

  useEffect(() => {
    loadFrappeGantt().catch((err) => {
      console.error("Failed to load Frappe Gantt:", err);
      setError("Échec du chargement de la bibliothèque de diagramme de Gantt");
    });
  }, []);

  useEffect(() => {
    const fetchActions = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const response = await axios.get(
          `${API_BASE_URL}/actions/action-plan/${actionPlan.actionPlanID}`,
          { withCredentials: true }
        );
        const fetchedActions = response.data.data;
        setActions(fetchedActions);
      } catch (err) {
        setError(err.response?.data?.message || "Échec de la récupération des actions");
      } finally {
        setIsLoading(false);
      }
    };

    if (actionPlan?.actionPlanID) {
      fetchActions();
    }
  }, [actionPlan]);

  // Compute status distribution for the doughnut chart
  const statusDistribution = actions.reduce((acc, action) => {
    const status = action.status || "Non démarré";
    acc[status] = (acc[status] || 0) + 1;
    return acc;
  }, {});

  const statusLabels = Object.keys(statusDistribution);
  const statusCounts = Object.values(statusDistribution);
  const totalActions = statusCounts.reduce((sum, count) => sum + count, 0);
  const completedCount = statusDistribution["Completed"] || 0;
  const progression = totalActions > 0 ? ((completedCount / totalActions) * 100).toFixed(1) : 0;
  const statusPercentages = statusCounts.map(count => ((count / totalActions) * 100).toFixed(1));

  const doughnutData = {
    labels: statusLabels,
    datasets: [
      {
        label: "Actions par statut",
        data: statusCounts,
        backgroundColor: ["#FF6384", "#36A2EB", "#FFCE56", "#4BC0C0"],
        borderColor: ["#FF6384", "#36A2EB", "#FFCE56", "#4BC0C0"],
        borderWidth: 1,
      },
    ],
  };

  const doughnutOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: "top",
      },
      title: {
        display: true,
        text: "Répartition des actions par statut",
      },
      centerText: true, // Enable the custom plugin
    },
  };

  useEffect(() => {
    console.log("Action Plan:", actionPlan);
    console.log("Actions:", actions);

    if (actions.length > 0 && ganttRef.current && window.Gantt) {
      ganttRef.current.innerHTML = "";

      const today = new Date();
      const planStart = actionPlan.plannedBeginDate ? new Date(actionPlan.plannedBeginDate) : new Date(today.getFullYear(), today.getMonth(), 1);
      const planEnd = actionPlan.plannedEndDate ? new Date(actionPlan.plannedEndDate) : new Date(today.getFullYear(), today.getMonth() + 2, 0);
      if (planEnd < new Date("2025-05-31")) {
        planEnd.setFullYear(2025, 4, 31);
      }

      console.log("Gantt Date Range:", {
        planStart: planStart.toISOString(),
        planEnd: planEnd.toISOString(),
      });

      const tasks = filteredActions.map((action, index) => {
        const progress = action.status === "Completed" ? 100 : action.status === "In Progress" ? 50 : 0;
        const task = {
          id: action.actionID,
          name: null,
          start: action.startDate,
          end: action.endDate,
          progress,
          dependencies: "",
          custom_class: `gantt-task-${index}`,
          fullName: `${action.name} (${action.assignee?.username || "N/A"})`,
        };
        console.log(`Task ${index + 1}:`, {
          name: task.fullName,
          start: task.start,
          end: task.end,
          progress: task.progress,
        });
        return task;
      });

      console.log("Processed Tasks:", tasks);

      if (tasks.length === 0) {
        console.warn("No valid tasks to render after filtering.");
        setError("Aucune tâche avec des dates valides à afficher dans le diagramme de Gantt.");
        return;
      }

      try {
        const startDate = new Date(planStart);
        const endDate = new Date(planEnd);
        const daysDiff = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
        const columnWidth = 30;
        const chartWidth = daysDiff * columnWidth;

        const ganttOptions = {
          header_date_format: "YYYY-MM-DD",
          view_mode: viewMode,
          bar_height: 20,
          padding: 10,
          column_width: columnWidth,
          language: "fr",
          date_range: {
            start: planStart.toISOString().split('T')[0],
            end: planEnd.toISOString().split('T')[0],
          },
          custom_popup_html: (task) => `
            <div class="details-container">
              <h5>${task.fullName}</h5>
              <p>Début: ${task.start}</p>
              <p>Fin: ${task.end}</p>
              <p>Progression: ${task.progress}%</p>
            </div>
          `,
          on_click: (task) => {
            console.log("Task clicked:", task);
            navigate(`/admin/data/actions/edit/${task.id}/overview`);
          },
          show_label: false,
        };

        ganttInstanceRef.current = new window.Gantt(ganttRef.current, tasks, ganttOptions);

        setTimeout(() => {
          if (ganttRef.current) {
            const ganttContainer = ganttRef.current.querySelector(".gantt-container");
            if (ganttContainer) {
              ganttContainer.querySelectorAll(".bar-label").forEach((label) => label.remove());
              const svg = ganttContainer.querySelector("svg");
              if (svg) {
                svg.style.width = `${chartWidth}px`;
                svg.setAttribute("width", chartWidth.toString());
                console.log("Main Gantt SVG content after initialization:", svg.outerHTML.substring(0, 1000) + "...");
                const bars = svg.querySelectorAll(".bar, .bar-progress");
                console.log(`Number of bars rendered in SVG: ${bars.length}`);
              } else {
                console.error("SVG element not found in main Gantt chart");
              }
            } else {
              console.error("Gantt container not found in main chart");
            }
          } else {
            console.error("ganttRef.current is null or undefined");
          }
        }, 500);
      } catch (err) {
        console.error("Failed to initialize Gantt chart:", err);
        setError("Échec du rendu du diagramme de Gantt : " + err.message);
      }
    }

    const cleanup = () => {
      document.querySelectorAll('text[style*="visibility: hidden"]').forEach(el => el.remove());
    };

    return () => {
      if (ganttRef.current) {
        ganttRef.current.innerHTML = "";
        cleanup();
      }
    };
  }, [actions, actionPlan, viewMode, navigate]);

  const handleViewModeChange = (e) => {
    const newViewMode = e.target.value;
    setViewMode(newViewMode);
    if (ganttInstanceRef.current) {
      try {
        ganttInstanceRef.current.change_view_mode(newViewMode);
      } catch (err) {
        console.error("Error changing view mode:", err);
      }
    }
  };

  const svgToPng = (svgElement, chartWidth) => {
    return new Promise((resolve, reject) => {
      try {
        const svgString = new XMLSerializer().serializeToString(svgElement);
        const svgBlob = new Blob([svgString], { type: "image/svg+xml;charset=utf-8" });
        const url = URL.createObjectURL(svgBlob);

        const img = new Image();
        img.onload = () => {
          const canvas = document.createElement("canvas");
          canvas.width = chartWidth;
          canvas.height = svgElement.getAttribute("height") || 260;
          const ctx = canvas.getContext("2d");
          ctx.fillStyle = "#FFFFFF";
          ctx.fillRect(0, 0, canvas.width, canvas.height);
          ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

          const pngDataUrl = canvas.toDataURL("image/png");
          URL.revokeObjectURL(url);
          resolve(pngDataUrl);
        };
        img.onerror = (err) => {
          URL.revokeObjectURL(url);
          reject(new Error("Failed to load SVG as image: " + err.message));
        };
        img.src = url;
      } catch (err) {
        reject(new Error("Failed to convert SVG to PNG: " + err.message));
      }
    });
  };

  const applySafeStylesToSVG = (svgElement, chartWidth) => {
    svgElement.style.backgroundColor = "#FFFFFF";
    svgElement.style.fill = "none";
    svgElement.style.width = `${chartWidth}px`;
    svgElement.setAttribute("width", chartWidth.toString());
    svgElement.querySelectorAll(".bar-label, text.bar-label").forEach((label) => label.remove());
    svgElement.querySelectorAll(".bar").forEach((bar) => {
      bar.style.fill = "#304a70";
      bar.style.stroke = "#000000";
    });
    svgElement.querySelectorAll(".bar-progress").forEach((progress) => {
      progress.style.fill = "#304a70";
      progress.style.stroke = "#000000";
    });
    svgElement.querySelectorAll(".grid-header").forEach((header) => {
      header.style.fill = "#FFFFFF";
      header.style.stroke = "#000000";
    });
    svgElement.querySelectorAll(".grid-row").forEach((row) => {
      row.style.fill = "#FFFFFF";
      row.style.stroke = "#E0E0E0";
    });
    svgElement.querySelectorAll(".arrow").forEach((arrow) => {
      arrow.style.fill = "none";
      arrow.style.stroke = "#000000";
    });
    svgElement.querySelectorAll(".tick").forEach((tick) => {
      tick.style.stroke = "#000000";
    });
    svgElement.querySelectorAll("text").forEach((text) => {
      text.style.fill = "#000000";
    });
  };

  const generatePDFContent = (pdf, actions, viewMode, actionPlan) => {
    const pdfInstanceId = `pdf-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    console.log(`Starting PDF generation (Instance ID: ${pdfInstanceId})`);

    pdf.setFontSize(16);
    const planName = actionPlan?.name || actionPlan?.actionPlanID || "Plan inconnu";
    const titleText = `Plan d'action : ${planName}`;
    pdf.text(titleText, 10, 10);

    pdf.setFontSize(12);
    pdf.text("Actions", 10, 25);
    let yPosition = 35;

    const today = new Date();
    const planStart = actionPlan.plannedBeginDate ? new Date(actionPlan.plannedBeginDate) : new Date(today.getFullYear(), today.getMonth(), 1);
    const planEnd = actionPlan.plannedEndDate ? new Date(actionPlan.plannedEndDate) : new Date(today.getFullYear(), today.getMonth() + 2, 0);
    if (planEnd < new Date("2025-05-31")) {
        planEnd.setFullYear(2025, 4, 31);
    }

    if (isNaN(planStart.getTime()) || isNaN(planEnd.getTime())) {
      throw new Error("Dates prévues invalides dans le plan d'action");
    }

    console.log(`Actions for PDF (Instance ID: ${pdfInstanceId}):`, actions);
    actions.forEach((action, index) => {
      const actionText = `${action.name} (${action.assignee?.username || "N/A"})`;
      console.log(`Adding action ${index + 1} to PDF (Instance ID: ${pdfInstanceId}):`, actionText);
      pdf.text(actionText, 10, yPosition);
      yPosition += 10;
    });
    console.log(`Finished adding Actions section (Instance ID: ${pdfInstanceId})`);

    return { pdf, yPosition, pdfInstanceId, planStart, planEnd };
  };

  const downloadPDF = async () => {
    console.log("downloadPDF called at:", new Date().toISOString());
    if (isDownloadingPDF.current) {
      console.warn("PDF download already in progress, please wait...");
      return;
    }
    isDownloadingPDF.current = true;
    setIsPDFButtonDisabled(true);

    let pdf = new jsPDF({ orientation: "landscape" });
    try {
      let { pdf: updatedPdf, yPosition, pdfInstanceId, planStart, planEnd } = generatePDFContent(
        pdf,
        filteredActions,
        viewMode,
        actionPlan
      );
      pdf = updatedPdf;

      const startDate = new Date(planStart);
      const endDate = new Date(planEnd);
      const daysDiff = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
      const columnWidth = 30;
      const chartWidth = daysDiff * columnWidth;

      const chartContainer = chartContainerRef.current;
      if (chartContainer) {
        const tempContainer = document.createElement("div");
        tempContainer.style.position = "absolute";
        tempContainer.style.top = "0";
        tempContainer.style.left = "0";
        tempContainer.style.width = `${chartWidth}px`;
        tempContainer.style.height = "100%";
        tempContainer.style.zIndex = "-1";
        const clonedElement = chartContainer.cloneNode(true);
        tempContainer.appendChild(clonedElement);
        document.body.appendChild(tempContainer);

        const tempGanttContainer = clonedElement.querySelector(".gantt-container");
        if (tempGanttContainer) {
          tempGanttContainer.innerHTML = "";
          const tasks = filteredActions.map((action, index) => {
            const progress = action.status === "Completed" ? 100 : action.status === "In Progress" ? 50 : 0;
            return {
              id: action.actionID,
              name: null,
              start: action.startDate,
              end: action.endDate,
              progress,
              dependencies: "",
              custom_class: `gantt-task-${index}`,
              fullName: `${action.name} (${action.assignee?.username || "N/A"})`,
            };
          });

          try {
            patchFrappeGantt();

            const tempGantt = new window.Gantt(tempGanttContainer, tasks, {
              header_date_format: "YYYY-MM-DD",
              view_mode: viewMode,
              bar_height: 20,
              padding: 10,
              column_width: columnWidth,
              language: "fr",
              date_range: {
                start: planStart.toISOString().split('T')[0],
                end: planEnd.toISOString().split('T')[0],
              },
              custom_popup_html: (task) => `
                <div class="details-container">
                  <h5>${task.fullName}</h5>
                  <p>Début: ${task.start}</p>
                  <p>Fin: ${task.end}</p>
                  <p>Progression: ${task.progress}%</p>
                </div>
              `,
              show_label: false,
            });

            const svg = tempGanttContainer.querySelector("svg");
            if (svg) {
              applySafeStylesToSVG(svg, chartWidth);
              console.log(`Forced SVG width to ${chartWidth}px (Instance ID: ${pdfInstanceId})`);
              console.log("Temporary Gantt SVG content:", svg.outerHTML.substring(0, 1000) + "...");

              const pngDataUrl = await svgToPng(svg, chartWidth);
              if (!pngDataUrl || pngDataUrl === "data:,") {
                throw new Error("Failed to convert SVG to PNG: empty image data");
              }

              const imgWidth = 800;
              const originalImgHeight = (parseFloat(svg.getAttribute("height")) * imgWidth) / chartWidth;
              const imgHeight = originalImgHeight * 2;
              pdf.addImage(pngDataUrl, "PNG", 10, yPosition + 10, imgWidth, imgHeight);
              yPosition += imgHeight + 20;
              console.log(`Gantt chart added to PDF (Instance ID: ${pdfInstanceId})`);
            } else {
              throw new Error("SVG element not found in temporary Gantt chart");
            }
          } catch (err) {
            console.error("Failed to initialize temporary Gantt chart for PDF:", err);
            pdf.text(`Image du diagramme de Gantt non disponible en raison d'une erreur de rendu : ${err.message}`, 10, yPosition + 10);
            yPosition += 20;
            console.log(`Failed to add Gantt chart to PDF (Instance ID: ${pdfInstanceId})`);
          }
        } else {
          pdf.text("Conteneur du diagramme de Gantt non trouvé", 10, yPosition + 10);
          yPosition += 20;
          console.log(`Gantt container not found (Instance ID: ${pdfInstanceId})`);
        }

        document.body.removeChild(tempContainer);
      } else {
        pdf.text("Image du diagramme de Gantt non disponible", 10, yPosition + 10);
        yPosition += 20;
        console.log(`Chart container not found (Instance ID: ${pdfInstanceId})`);
      }

      pdf.text("Données du diagramme :", 10, yPosition);
      yPosition += 10;
      console.log(`filteredActions before Chart Data section (Instance ID: ${pdfInstanceId}):`, filteredActions);
      filteredActions.forEach((action, index) => {
        const progress = action.status === "Completed" ? 100 : action.status === "In Progress" ? 50 : 0;
        const chartDataText = `${action.name}: ${action.startDate} à ${action.endDate}, Progression: ${progress}%`;
        console.log(`Adding chart data ${index + 1} to PDF (Instance ID: ${pdfInstanceId}):`, chartDataText);
        pdf.text(chartDataText, 10, yPosition);
        yPosition += 10;
      });
      console.log(`Finished adding Chart Data section (Instance ID: ${pdfInstanceId})`);

      pdf.save(`diagramme-gantt-${actionPlan.actionPlanID}-${viewMode}.pdf`);
      console.log(`PDF generation completed (Instance ID: ${pdfInstanceId})`);
    } catch (err) {
      console.error("Error generating PDF:", err.message, err.stack);
      alert(`Échec du téléchargement du PDF : ${err.message}`);
    } finally {
      isDownloadingPDF.current = false;
      setIsPDFButtonDisabled(false);
      pdf = null;
    }
  };

  const downloadDoughnutPDF = async () => {
    try {
      if (!doughnutChartRef.current) {
        console.error("Doughnut chart reference is not available.");
        alert("Erreur : Impossible de générer le PDF. Le graphique n'est pas prêt.");
        return;
      }

      // Get the chart as a base64 image
      const chartImage = doughnutChartRef.current.toBase64Image();
      console.log("Doughnut chart image generated successfully.");

      // Create a new jsPDF instance
      const pdf = new jsPDF({
        orientation: 'landscape',
        unit: 'in',
        format: 'letter',
      });

      // Define page dimensions
      const pageWidth = pdf.internal.pageSize.getWidth(); // 11 inches
      const pageHeight = pdf.internal.pageSize.getHeight(); // 8.5 inches
      const margin = 0.5;
      const maxWidth = pageWidth - 2 * margin; // 10 inches
      const maxHeight = pageHeight - 2 * margin - 2; // Reserve 2 inches for Détails section

      // Calculate image dimensions while maintaining aspect ratio
      const imgProps = pdf.getImageProperties(chartImage);
      let pdfWidth = maxWidth;
      let pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;

      // Scale down if the height exceeds the maximum height
      if (pdfHeight > maxHeight) {
        pdfHeight = maxHeight;
        pdfWidth = (imgProps.width * pdfHeight) / imgProps.height;
      }

      // Position the chart in the center
      const chartX = (pageWidth - pdfWidth) / 2; // Center the chart horizontally

      // Add the chart image to the PDF
      pdf.addImage(chartImage, 'JPEG', chartX, margin, pdfWidth, pdfHeight);

      // Add the Détails section as text
      let yPosition = pdfHeight + margin + 0.5; // Start below the chart
      pdf.setFontSize(16);
      pdf.setTextColor(45, 55, 72); // RGB equivalent of #2D3748
      pdf.text('Détails', margin, yPosition);
      yPosition += 0.5;

      // Total Actions
      pdf.setFontSize(12);
      pdf.setTextColor(113, 128, 150); // RGB equivalent of #718096
      pdf.text("Nombre total d'actions:", margin, yPosition);
      pdf.setTextColor(26, 41, 66); // RGB equivalent of #1A2942
      pdf.setFont('helvetica', 'bold');
      pdf.text(`${totalActions}`, margin + 3, yPosition);
      yPosition += 0.3;

      // Most Frequent Status
      pdf.setFont('helvetica', 'normal');
      pdf.setTextColor(113, 128, 150); // RGB equivalent of #718096
      pdf.text('Statut le plus fréquent:', margin, yPosition);
      pdf.setTextColor(26, 41, 66); // RGB equivalent of #1A2942
      pdf.setFont('helvetica', 'bold');
      pdf.text(`${statusLabels[statusCounts.indexOf(Math.max(...statusCounts))] || "N/A"}`, margin + 3, yPosition);
      yPosition += 0.5;

      // List of Statuses and Counts
      pdf.setFont('helvetica', 'normal');
      pdf.setTextColor(55, 65, 81); // RGB equivalent of #374151
      pdf.setFontSize(11);
      statusLabels.forEach((label, index) => {
        if (yPosition > pageHeight - margin) {
          pdf.addPage();
          yPosition = margin;
        }
        pdf.text(
          `${label}: ${statusCounts[index]} action(s) (${statusPercentages[index]}%)`,
          margin + 0.2,
          yPosition
        );
        yPosition += 0.3;
      });

      // Save the PDF
      pdf.save(`action_plan_progress_${actionPlan.actionPlanID}.pdf`);
      console.log("Doughnut chart PDF generated and downloaded successfully.");
    } catch (err) {
      console.error("Error generating Doughnut chart PDF:", err);
      alert("Erreur lors de la génération du PDF. Veuillez vérifier la console pour plus de détails.");
    }
  };

  const downloadGanttExcel = () => {
    const ganttData = filteredActions.map((action) => {
      const progress = action.status === "Completed" ? "100%" : action.status === "In Progress" ? "50%" : "0%";
      return {
        Action: action.name,
        Assignee: action.assignee?.username || "N/A",
        StartDate: action.startDate,
        EndDate: action.endDate,
        Status: action.status,
        Progress: progress,
      };
    });

    const ganttWorksheet = XLSX.utils.json_to_sheet(ganttData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, ganttWorksheet, "Diagramme de Gantt");
    XLSX.writeFile(workbook, `diagramme-gantt-${actionPlan.actionPlanID}-${viewMode}.xlsx`);
  };

  const downloadDoughnutExcel = () => {
    const statusData = [
      ...statusLabels.map((label, index) => ({
        Status: label,
        Count: statusCounts[index],
        Percentage: `${statusPercentages[index]}%`,
      })),
      { Status: "Progression globale", Count: "", Percentage: `${progression}%` }
    ];

    const statusWorksheet = XLSX.utils.json_to_sheet(statusData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, statusWorksheet, "Répartition des statuts");
    XLSX.writeFile(workbook, `action_plan_progress_${actionPlan.actionPlanID}.xlsx`);
  };

  const filteredActions = actions
    .filter((action) => {
      if (!action.startDate || !action.endDate) return false;
      const actionStart = new Date(action.startDate);
      const actionEnd = new Date(action.endDate);
      return !isNaN(actionStart.getTime()) && !isNaN(actionEnd.getTime());
    })
    .sort((a, b) => {
      const dateA = new Date(a.startDate);
      const dateB = new Date(b.startDate);
      return dateA - dateB;
    });

  return (
    <div className="p-6">
      <h2 className="text-xl font-semibold mb-6">{t('admin.action_plans.tabs.progress', 'Progress Report')}</h2>

      <div className="space-y-4">
        <details className="bg-white rounded-lg shadow" open>
          <summary className="text-lg font-medium text-gray-700 p-4 cursor-pointer hover:bg-gray-50">
            Diagramme de Gantt
          </summary>
          <div className="p-4">
            <div className="mb-6 flex justify-between items-center relative z-20">
              <div className="flex items-center">
                <label htmlFor="view-mode" className="mr-3 text-base font-medium text-gray-700">
                  Mode de vue :
                </label>
                <select
                  id="view-mode"
                  value={viewMode}
                  onChange={handleViewModeChange}
                  disabled={actions.length === 0}
                  className="bg-white border border-gray-300 rounded-lg shadow-sm px-4 py-2 text-base text-gray-700 focus:outline-none focus:ring-2 focus:ring-[#F62D51] disabled:opacity-50"
                >
                  <option value="Day">Jours</option>
                  <option value="Month">Mois</option>
                  <option value="Year">Années</option>
                </select>
              </div>
              <div className="flex space-x-4">
                <button
                  onClick={downloadPDF}
                  disabled={actions.length === 0 || isPDFButtonDisabled}
                  className="bg-[#F62D51] hover:bg-red-700 text-white px-4 py-2 rounded-lg shadow-sm disabled:opacity-50"
                >
                  Télécharger PDF
                </button>
                <button
                  onClick={downloadGanttExcel}
                  disabled={actions.length === 0}
                  className="bg-[#F62D51] hover:bg-red-700 text-white px-4 py-2 rounded-lg shadow-sm disabled:opacity-50"
                >
                  Télécharger Excel
                </button>
              </div>
            </div>

            {isLoading ? (
              <div className="flex items-center justify-center py-12">
                <Loader2 className="h-8 w-8 animate-spin text-[#F62D51]" />
              </div>
            ) : error ? (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
                Erreur : {error}
              </div>
            ) : actions.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-12 text-center">
                <BarChart className="h-16 w-16 text-gray-300 mb-4" />
                <h3 className="text-lg font-medium text-gray-700 mb-2">
                  Aucune action disponible
                </h3>
                <p className="text-gray-500 max-w-md">
                  Ajoutez des actions à ce plan d'action pour générer un diagramme de Gantt.
                </p>
              </div>
            ) : filteredActions.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-12 text-center">
                <BarChart className="h-16 w-16 text-gray-300 mb-4" />
                <h3 className="text-lg font-medium text-gray-700 mb-2">
                  Aucune action valide disponible
                </h3>
                <p className="text-gray-500 max-w-md">
                  Les actions doivent avoir des dates de début et de fin valides pour être affichées dans le diagramme de Gantt.
                </p>
              </div>
            ) : (
              <div ref={chartContainerRef} className="flex">
                <div className="w-48 flex-shrink-0 bg-gray-50 border-r border-gray-200">
                  <div className="h-[40px] flex items-center px-3 text-sm font-medium text-gray-500">
                    Actions
                  </div>
                  {filteredActions.map((action) => (
                    <div
                      key={action.actionID}
                      className="h-[30px] flex items-center px-3 text-sm text-gray-700 truncate border-t border-gray-200"
                      style={{ height: "30px" }}
                    >
                      {`${action.name} (${action.assignee?.username || "N/A"})`}
                    </div>
                  ))}
                </div>
                <div ref={ganttRef} className="flex-1 h-64 overflow-x-auto gantt-container" />
              </div>
            )}
          </div>
        </details>

        <details className="bg-white rounded-lg shadow">
          <summary className="text-lg font-medium text-gray-700 p-4 cursor-pointer hover:bg-gray-50">
            Rapport d'avancement
          </summary>
          <div className="p-4">
            {isLoading ? (
              <div className="flex items-center justify-center py-12">
                <Loader2 className="h-8 w-8 animate-spin text-[#F62D51]" />
              </div>
            ) : error ? (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
                Erreur : {error}
              </div>
            ) : actions.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-12 text-center">
                <BarChart className="h-16 w-16 text-gray-300 mb-4" />
                <h3 className="text-lg font-medium text-gray-700 mb-2">
                  Aucune action disponible
                </h3>
                <p className="text-gray-500 max-w-md">
                  Ajoutez des actions à ce plan d'action pour afficher les métriques de progression.
                </p>
              </div>
            ) : (
              <div>
                <div className="mb-6 flex justify-between items-center relative z-20">
                  <div className="flex items-center">
                    {/* Placeholder for potential future controls */}
                  </div>
                  <div className="flex space-x-4">
                    <button
                      onClick={downloadDoughnutPDF}
                      disabled={actions.length === 0}
                      className="bg-[#F62D51] hover:bg-red-700 text-white px-4 py-2 rounded-lg shadow-sm disabled:opacity-50"
                    >
                      Télécharger PDF
                    </button>
                    <button
                      onClick={downloadDoughnutExcel}
                      disabled={actions.length === 0}
                      className="bg-[#F62D51] hover:bg-red-700 text-white px-4 py-2 rounded-lg shadow-sm disabled:opacity-50"
                    >
                      Télécharger Excel
                    </button>
                  </div>
                </div>
                <div className="w-full max-w-md mx-auto mb-6">
                  <Doughnut ref={doughnutChartRef} data={doughnutData} options={doughnutOptions} plugins={[centerTextPlugin]} />
                </div>
                <div className="mt-4">
                  <h3 className="text-lg font-semibold">Détails des statuts :</h3>
                  <ul className="list-disc pl-5">
                    {statusLabels.map((label, index) => (
                      <li key={index}>
                        {label}: {statusCounts[index]} action(s) ({statusPercentages[index]}%)
                      </li>
                    ))}
                  </ul>
                </div>
                <div className="mt-6 bg-gray-50 rounded-xl p-4 border border-gray-200">
                  <h3 className="text-lg font-semibold mb-3 text-gray-800">Détails</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                      <p className="text-sm text-gray-500 mb-1">Nombre total d'actions</p>
                      <p className="text-xl font-bold text-[#1A2942]">{totalActions}</p>
                    </div>
                    <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                      <p className="text-sm text-gray-500 mb-1">Statut le plus fréquent</p>
                      <p className="text-xl font-bold text-[#1A2942]">
                        {statusLabels[statusCounts.indexOf(Math.max(...statusCounts))] || "N/A"}
                      </p>
                    </div>
                  </div>
                  <div className="mt-4">
                    <h4 className="text-md font-semibold mb-2 text-gray-800">Liste des actions</h4>
                    <ul className="list-disc pl-5">
                      {actions.map((action) => (
                        <li key={action.actionID} className="text-gray-700">
                          Nom de l'action : {action.name}, Statut : {action.status || "Non démarré"}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            )}
          </div>
        </details>
      </div>

      <link
        rel="stylesheet"
        href="https://cdn.jsdelivr.net/npm/frappe-gantt@0.6.1/dist/frappe-gantt.min.css"
      />
      <style>
        {`
          .gantt-container svg {
            margin: 0 !important;
            padding: 0 !important;
          }
          .gantt-container .gantt .bar-wrapper {
            margin-top: 0 !important;
          }
          .gantt-container .gantt .grid-header {
            margin: 0 !important;
            fill: #FFFFFF !important;
            background-color: #FFFFFF !important;
          }
          .gantt-container .gantt .bar {
            fill: #304a70 !important;
          }
          .gantt-container .gantt .bar-progress {
            fill: #304a70 !important;
          }
          .gantt-container .gantt .bar-label {
            display: none !important;
          }
          .gantt-container .gantt .grid-row {
            fill: #FFFFFF !important;
          }
        `}
      </style>
    </div>
  );
}

export default ActionPlanProgress;