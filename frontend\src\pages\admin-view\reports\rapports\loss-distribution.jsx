import React, { useState, useRef } from "react";
import { Bar } from "react-chartjs-2";
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend } from "chart.js";
import { jsPDF } from "jspdf";
import * as XLSX from "https://cdn.jsdelivr.net/npm/xlsx@0.18.5/xlsx.mjs";
import { Button } from "@/components/ui/button";
import { lossDistributionData } from "./mock-data";
import EmailReportModal from '@/components/reports/EmailReportModal';

ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);

const LossDistributionRapport = (props) => {
  // Add state for email modal
  const [isEmailModalOpen, setIsEmailModalOpen] = useState(false);
  
  // Add function to handle email button click
  const handleEmailReport = () => {
    setIsEmailModalOpen(true);
  };
  const [data] = useState(lossDistributionData);
  const [loading] = useState(false);
  const chartRef = useRef(null); // Reference to the chart instance

  const chartData = {
    labels: data.map(item => item.TargetRisk),
    datasets: [
      {
        label: "Probability",
        data: data.map(item => item.Probability),
        backgroundColor: data.map(item => {
          switch (item.TargetRisk) {
            case "Very Low": return "rgba(0, 128, 0, 0.7)"; // Darker Green
            case "Low": return "rgba(144, 238, 144, 0.7)";   // Light Green
            case "Medium": return "rgba(255, 215, 0, 0.7)"; // Gold
            case "High": return "rgba(255, 140, 0, 0.7)";   // Dark Orange
            case "Very High": return "rgba(220, 20, 60, 0.7)"; // Crimson
            default: return "rgba(128, 128, 128, 0.7)";     // Gray
          }
        }),
        borderColor: data.map(item => {
          switch (item.TargetRisk) {
            case "Very Low": return "rgba(0, 100, 0, 1)"; // Darker Green border
            case "Low": return "rgba(46, 139, 87, 1)";   // Sea Green border
            case "Medium": return "rgba(184, 134, 11, 1)"; // Dark Goldenrod border
            case "High": return "rgba(205, 92, 92, 1)";   // Indian Red border
            case "Very High": return "rgba(178, 34, 34, 1)"; // Firebrick border
            default: return "rgba(105, 105, 105, 1)";     // Dim Gray border
          }
        }),
        borderWidth: 1,
        borderRadius: 6,
        hoverBackgroundColor: data.map(item => {
          switch (item.TargetRisk) {
            case "Very Low": return "rgba(0, 128, 0, 0.9)";
            case "Low": return "rgba(144, 238, 144, 0.9)";
            case "Medium": return "rgba(255, 215, 0, 0.9)";
            case "High": return "rgba(255, 140, 0, 0.9)";
            case "Very High": return "rgba(220, 20, 60, 0.9)";
            default: return "rgba(128, 128, 128, 0.9)";
          }
        }),
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: true,
    scales: {
      y: {
        title: { display: true, text: "Probability (Number of Incidents)", font: { weight: 'bold' } },
        grid: { color: 'rgba(200, 200, 200, 0.2)' },
        ticks: { font: { size: 12 } }
      },
      x: {
        title: { display: true, text: "Target Risk Level", font: { weight: 'bold' } },
        grid: { display: false },
        ticks: { font: { size: 12 } }
      },
    },
    plugins: {
      legend: {
        position: "top",
        labels: { font: { size: 14 } }
      },
      title: {
        display: true,
        text: "Loss Distribution by Risk Level",
        font: { size: 18, weight: 'bold' },
        color: '#1A2942',
        padding: { bottom: 20 }
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.7)',
        titleFont: { size: 14 },
        bodyFont: { size: 13 },
        padding: 12,
        cornerRadius: 6,
        displayColors: false
      }
    },
    animation: {
      duration: 2000,
      easing: 'easeOutQuart'
    },
  };

  const downloadPDF = async () => {
    try {
      if (!chartRef.current) {
        console.error("Chart reference is not available.");
        alert("Error: Unable to generate PDF. The chart is not ready.");
        return;
      }

      // Get the chart as a base64 image
      const chartImage = chartRef.current.toBase64Image();
      console.log("Chart image generated successfully.");

      // Create a new jsPDF instance
      const pdf = new jsPDF({
        orientation: 'landscape',
        unit: 'in',
        format: 'letter'
      });

      // Calculate the dimensions for the chart image
      const imgProps = pdf.getImageProperties(chartImage);
      const pdfWidth = pdf.internal.pageSize.getWidth() - 1; // 1 inch margin
      const pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;

      // Add the chart image to the PDF
      pdf.addImage(chartImage, 'JPEG', 0.5, 0.5, pdfWidth, pdfHeight);

      // Add the Details section as text
      let yPosition = pdfHeight + 1; // Start below the chart
      pdf.setFontSize(16);
      pdf.setTextColor(45, 55, 72); // RGB equivalent of #2D3748
      pdf.text('Details', 0.5, yPosition);
      yPosition += 0.5;

      pdf.setFontSize(12);
      pdf.setTextColor(113, 128, 150); // RGB equivalent of #718096
      pdf.text('Total Number of Incidents:', 0.5, yPosition);
      pdf.setTextColor(26, 41, 66); // RGB equivalent of #1A2942
      pdf.setFont('helvetica', 'bold');
      pdf.text(`${totalIncidents}`, 3.5, yPosition);
      yPosition += 0.5;

      pdf.setFont('helvetica', 'normal');
      pdf.setTextColor(113, 128, 150); // RGB equivalent of #718096
      pdf.text('Dominant Risk Level:', 0.5, yPosition);
      pdf.setTextColor(26, 41, 66); // RGB equivalent of #1A2942
      pdf.setFont('helvetica', 'bold');
      pdf.text(`${dominantRiskLevel}`, 3.5, yPosition);

      // Save the PDF
      pdf.save('loss_distribution_report.pdf');
      console.log("PDF generated and downloaded successfully.");
    } catch (err) {
      console.error("Error generating PDF:", err);
      alert("Error generating PDF. Please check the console for details.");
    }
  };

  const downloadExcel = () => {
    if (!data) return;
    const excelData = data.map(item => ({
      'Risk Level': item.TargetRisk,
      Probability: item.Probability,
    }));
    const worksheet = XLSX.utils.json_to_sheet(excelData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "LossDistribution");
    XLSX.writeFile(workbook, "loss_distribution.xlsx");
  };

  const totalIncidents = data.reduce((sum, item) => sum + item.Probability, 0);
  const dominantRiskLevel = data.length > 0
    ? data.reduce((max, item) => item.Probability > max.Probability ? item : max, data[0]).TargetRisk
    : "N/A";

  if (loading) return (
    <div className="p-6 flex justify-center items-center h-64">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-red-600"></div>
    </div>
  );

  if (!data) return (
    <div className="p-6 bg-gray-50 rounded-lg border border-gray-200 text-gray-500 text-center">
      <svg className="w-12 h-12 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
      </svg>
      <p className="text-lg">No data available</p>
    </div>
  );

  return (
    <div className="p-6 bg-gradient-to-b from-gray-50 to-gray-100 min-h-screen">
      <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-[#1A2942]">Loss Distribution</h2>
        </div>

        <div id="loss-distribution-chart" className="bg-white p-4 rounded-lg border border-gray-100 shadow-sm">
          <div className="h-[400px]">
            <Bar ref={chartRef} data={chartData} options={options} />
          </div>
        </div>

        <div className="flex gap-4 mt-4">
          <Button onClick={downloadPDF} className="bg-[#22c55e] hover:bg-[#16a34a] text-white">
            Download PDF
          </Button>
          <Button onClick={downloadExcel} className="bg-[#22c55e] hover:bg-[#16a34a] text-white">
            Download Excel
          </Button>
          <Button
            onClick={handleEmailReport}
            className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
              <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
            </svg>
            Send via Email
          </Button>
        </div>

        <div className="mt-6 bg-gray-50 rounded-xl p-4 border border-gray-200">
          <h3 className="text-lg font-semibold mb-3 text-gray-800">Risk Level Legend</h3>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <div className="flex items-center bg-white p-2 rounded-lg shadow-sm">
              <div className="w-5 h-5 rounded-full bg-[rgba(220,20,60,0.7)] mr-2"></div>
              <span className="text-sm">Very High</span>
            </div>
            <div className="flex items-center bg-white p-2 rounded-lg shadow-sm">
              <div className="w-5 h-5 rounded-full bg-[rgba(255,140,0,0.7)] mr-2"></div>
              <span className="text-sm">High</span>
            </div>
            <div className="flex items-center bg-white p-2 rounded-lg shadow-sm">
              <div className="w-5 h-5 rounded-full bg-[rgba(255,215,0,0.7)] mr-2"></div>
              <span className="text-sm">Medium</span>
            </div>
            <div className="flex items-center bg-white p-2 rounded-lg shadow-sm">
              <div className="w-5 h-5 rounded-full bg-[rgba(144,238,144,0.7)] mr-2"></div>
              <span className="text-sm">Low</span>
            </div>
            <div className="flex items-center bg-white p-2 rounded-lg shadow-sm">
              <div className="w-5 h-5 rounded-full bg-[rgba(0,128,0,0.7)] mr-2"></div>
              <span className="text-sm">Very Low</span>
            </div>
          </div>
        </div>
      </div>
      <EmailReportModal
        isOpen={isEmailModalOpen}
        onClose={() => setIsEmailModalOpen(false)}
        reportType="loss-distribution"
        reportTitle="Loss Distribution"
        reportData={chartData}
      />
    </div>
  );
};

export default LossDistributionRapport;
