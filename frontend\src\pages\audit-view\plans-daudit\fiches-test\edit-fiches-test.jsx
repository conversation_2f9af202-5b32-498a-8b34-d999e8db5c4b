import React, { useState, useEffect, createContext, useContext } from "react";
import { useParams, useNavigate, Link } from "react-router-dom";
import {
  FileText,
  Loader2,
  Activity,
  ClipboardList,
  ArrowLeft,
  ChevronRight
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import DetailHeader from "@/components/ui/detail-header";
import { getFicheDeTestById } from "@/services/fiche-de-test-service";
import FichesTestCaracteristiquesTab from "./fiches-test-tabs/caracteristiques/caracteristiques";
import { toast } from "sonner";

export const OutletContext = createContext(null);
export const useCustomOutletContext = () => {
  const context = useContext(OutletContext);
  if (context === undefined) {
    throw new Error('useCustomOutletContext must be used within an OutletContextProvider');
  }
  return context;
};

function EditFichesTest() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [ficheTest, setFicheTest] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchFicheTest = async () => {
      setLoading(true);
      setError(null);
      try {
        const response = await getFicheDeTestById(id);
        if (response && response.success) {
          setFicheTest(response.data);
        } else {
          throw new Error(response?.message || "Fiche de test non trouvée");
        }
      } catch (error) {
        console.error('Error fetching fiche de test:', error);
        setError(error.message || "Une erreur est survenue lors du chargement de la fiche de test");
        toast.error("Erreur lors du chargement de la fiche de test");
      } finally {
        setLoading(false);
      }
    };
    
    if (id) {
      fetchFicheTest();
    }
  }, [id]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#F62D51]"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4">
        <Button variant="outline" onClick={() => navigate(-1)} className="mb-4">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Retour
        </Button>
        <div className="text-center py-8">
          <h2 className="text-2xl font-semibold text-gray-800 mb-2">Erreur</h2>
          <p className="text-gray-600">{error}</p>
        </div>
      </div>
    );
  }

  if (!ficheTest) {
    return (
      <div className="p-4">
        <Button variant="outline" onClick={() => navigate(-1)} className="mb-4">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Retour
        </Button>
        <div className="text-center py-8">
          <h2 className="text-2xl font-semibold text-gray-800 mb-2">Fiche de test non trouvée</h2>
          <p className="text-gray-600">La fiche de test demandée n'existe pas ou a été supprimée.</p>
        </div>
      </div>
    );
  }

  // Extract dynamic data from the ficheTest object
  console.log('ficheTest data:', ficheTest);
  
  // Try different data access paths
  const planName = ficheTest?.ficheDeTravail?.auditActivity?.auditMission?.auditPlan?.name || 
                   ficheTest?.ficheDeTravail?.auditMission?.auditPlan?.name ||
                   'Plan';
  const planId = ficheTest?.ficheDeTravail?.auditActivity?.auditMission?.auditPlan?.id ||
                 ficheTest?.ficheDeTravail?.auditMission?.auditPlan?.id;
  
  const missionName = ficheTest?.ficheDeTravail?.auditActivity?.auditMission?.name ||
                      ficheTest?.ficheDeTravail?.auditMission?.name ||
                      'Mission';
  const missionId = ficheTest?.ficheDeTravail?.auditActivity?.auditMission?.id ||
                    ficheTest?.ficheDeTravail?.auditMission?.id;
  
  const activityName = ficheTest?.ficheDeTravail?.auditActivity?.name || 'Activité';
  const activityId = ficheTest?.ficheDeTravail?.auditActivity?.id;
  const ficheTravailName = ficheTest?.ficheDeTravail?.name || 'Fiche de travail';
  const ficheTravailId = ficheTest?.ficheDeTravail?.id;
  const ficheTestName = ficheTest?.titre || 'Fiche de test';

  console.log('Extracted data:', {
    planName,
    planId,
    missionName,
    missionId,
    activityName,
    activityId,
    ficheTravailName,
    ficheTravailId,
    ficheTestName
  });

  // Generate metadata with proper data
  const metadata = [
    `Mission: ${missionName}`,
    `Activité: ${activityName}`,
    `Fiche de travail: ${ficheTravailName}`
  ];

  // Get status badge details
  const getStatusBadgeInfo = () => {
    return { label: 'En cours', variant: 'default', color: 'bg-blue-100 text-blue-800' };
  };

  // Dynamic breadcrumb with proper data
  const breadcrumb = (
    <Breadcrumb>
      <BreadcrumbList className="text-sm">
        <BreadcrumbItem>
          <BreadcrumbLink href="/audit" className="text-gray-500 font-medium">Audit</BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator />
        <BreadcrumbItem>
          <BreadcrumbLink href="/audit/plans-daudit" className="text-gray-800 hover:text-gray-600 font-medium">
            Plans d'audit
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator />
        <BreadcrumbItem>
          <BreadcrumbLink href={`/audit/plans-daudit/${planId}`} className="text-gray-800 hover:text-gray-600 font-medium">
            {planName}
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator />
        <BreadcrumbItem>
          <BreadcrumbLink href={`/audit/plans-daudit/${planId}/missions-audits/${missionId}`} className="text-gray-800 hover:text-gray-600 font-medium">
            {missionName}
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator />
        <BreadcrumbItem>
          <BreadcrumbLink href={`/audit/plans-daudit/${planId}/missions-audits/${missionId}/activites/${activityId}`} className="text-gray-800 hover:text-gray-600 font-medium">
            {activityName}
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator />
        <BreadcrumbItem>
          <BreadcrumbLink href={`/audit/plans-daudit/${planId}/missions-audits/${missionId}/activites/${activityId}/fiches-travail/${ficheTravailId}`} className="text-gray-800 hover:text-gray-600 font-medium">
            {ficheTravailName}
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator />
        <BreadcrumbItem>
          <BreadcrumbPage className="text-gray-800 font-medium">{ficheTestName}</BreadcrumbPage>
        </BreadcrumbItem>
      </BreadcrumbList>
    </Breadcrumb>
  );

  const handleGoBack = () => {
    // Navigate back to the fiches de test list
    navigate(`/audit/plans-daudit/${planId}/missions-audits/${missionId}/activites/${activityId}/fiches-travail/${ficheTravailId}/fiches-test`);
  };

  return (
    <div className="p-6 max-w-[1200px] mx-auto space-y-6">
      {/* Header with fiche de test information and breadcrumb */}
      <DetailHeader
        title={ficheTest.titre}
        icon={<FileText className="h-6 w-6 text-[#F62D51]" />}
        badges={[getStatusBadgeInfo()]}
        metadata={metadata}
        onBack={handleGoBack}
        backLabel="Retour à la liste des fiches de test"
        breadcrumb={breadcrumb}
      />
      
      {/* Tab content */}
      <OutletContext.Provider value={{ ficheTest, setFicheTest }}>
        <div className="bg-white rounded-lg shadow-sm p-6">
          <Tabs defaultValue="caracteristiques" className="w-full">
            <TabsList className="grid w-full grid-cols-1">
              <TabsTrigger value="caracteristiques" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Caractéristiques
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="caracteristiques" className="mt-6">
              <FichesTestCaracteristiquesTab />
            </TabsContent>
          </Tabs>
        </div>
      </OutletContext.Provider>
    </div>
  );
}

export default EditFichesTest;

