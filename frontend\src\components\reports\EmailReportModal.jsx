import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { getApiEndpointUrl, getAuthHeaders } from '@/utils/api-config';
import { toast } from 'sonner';
import { format } from 'date-fns';

const EmailReportModal = ({ isOpen, onClose, reportType, reportTitle, reportData, filters, startDate, endDate, timePeriod, totalNetLosses, totalIncidents, getDMRLabel }) => {
  const [recipients, setRecipients] = useState([]);
  const [loading, setLoading] = useState(false);
  const [sending, setSending] = useState(false);
  const [selectedRecipient, setSelectedRecipient] = useState('');
  const [customEmail, setCustomEmail] = useState('');
  const [useCustomEmail, setUseCustomEmail] = useState(false);
  const [message, setMessage] = useState('');
  
  useEffect(() => {
    if (isOpen) {
      // First try the test route
      testCorsConnection();
    }
  }, [isOpen]);

  const testCorsConnection = async () => {
    try {
      const url = getApiEndpointUrl('reports/email/test');
      console.log('Testing CORS with:', url);
      
      const response = await axios.get(url, {
        withCredentials: true
      });
      
      console.log('CORS test response:', response.data);
      
      // If test is successful, fetch recipients
      fetchRecipients();
    } catch (error) {
      console.error('CORS test failed:', error);
      toast.error('CORS test failed. Check console for details.');
    }
  };
  
  const fetchRecipients = async () => {
    try {
      setLoading(true);
      
      // Use getApiEndpointUrl to construct the correct URL
      const url = getApiEndpointUrl('reports/email/recipients');
      console.log('Fetching recipients from:', url);
      
      // Try with fetch API instead of axios
      const response = await fetch(url, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': localStorage.getItem('authToken') ? `Bearer ${localStorage.getItem('authToken')}` : ''
        }
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.success) {
        setRecipients(data.data || []);
      } else {
        toast.error('Failed to load recipients');
      }
    } catch (error) {
      console.error('Error fetching recipients:', error);
      toast.error('Failed to load recipients');
    } finally {
      setLoading(false);
    }
  };
  
  const handleSendEmail = async () => {
    // Validate recipient
    if (!useCustomEmail && !selectedRecipient) {
      toast.error('Please select a recipient');
      return;
    }
    
    if (useCustomEmail && !customEmail) {
      toast.error('Please enter an email address');
      return;
    }
    
    if (useCustomEmail && !isValidEmail(customEmail)) {
      toast.error('Please enter a valid email address');
      return;
    }
    
    try {
      setSending(true);
      
      const payload = {
        reportType,
        reportTitle,
        reportData,
        message,
        reportFormat: 'pdf'
      };
      
      if (useCustomEmail) {
        payload.recipientEmail = customEmail;
      } else {
        payload.recipientUserId = selectedRecipient;
      }
      
      // Use getApiEndpointUrl to construct the correct URL
      const url = getApiEndpointUrl('reports/email/send');
      console.log('Sending email to:', url);
      console.log('With payload:', payload);
      
      const response = await axios.post(url, payload, {
        withCredentials: true,
        headers: getAuthHeaders()
      });
      
      if (response.data.success) {
        toast.success('Report sent successfully');
        onClose();
      } else {
        toast.error(response.data.message || 'Failed to send report');
      }
    } catch (error) {
      console.error('Error sending report:', error);
      toast.error(error.response?.data?.message || 'Failed to send report');
    } finally {
      setSending(false);
    }
  };
  
  const isValidEmail = (email) => {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
  };
  
  // Add the displayFilterValue helper function
  const displayFilterValue = (filterValue, options, formatter) => {
    if (!filterValue || (Array.isArray(filterValue) && filterValue.length === 0)) {
      return 'All';
    }
    
    if (Array.isArray(filterValue)) {
      return filterValue.map(item => {
        if (typeof item === 'object' && item.label) {
          return item.label;
        } else if (formatter) {
          return formatter(item);
        } else if (options) {
          return formatFilterValue(item, options);
        } else {
          return item;
        }
      }).join(', ');
    }
    
    if (typeof filterValue === 'object' && filterValue.label) {
      return filterValue.label;
    } else if (formatter) {
      return formatter(filterValue);
    } else if (options) {
      return formatFilterValue(filterValue, options);
    } else {
      return filterValue;
    }
  };

  // Update the email body to include all filters
  const generateEmailBody = () => {
    const dateRange = startDate && endDate 
      ? `${format(new Date(startDate), 'MMM d, yyyy')} - ${format(new Date(endDate), 'MMM d, yyyy')}` 
      : 'All Time';
    
    return `
      Evolution of Losses and Incidents Over Time Report
      
      Date Range: ${dateRange}
      Time Period: ${timePeriod}
      Risk Control Level (DMR): ${displayFilterValue(filters?.DMR, [], getDMRLabel)}
      Incident Types: ${displayFilterValue(filters?.incidentTypes, [])}
      Incidents: ${displayFilterValue(filters?.incidents, [])}
      Entities: ${displayFilterValue(filters?.entities, [])}
      Business Processes: ${displayFilterValue(filters?.businessProcesses, [])}
      Organizational Processes: ${displayFilterValue(filters?.organizationalProcesses, [])}
      
      Total Net Losses: ${totalNetLosses}
      Total Incidents: ${totalIncidents}
      
      This report was generated on ${format(new Date(), 'MMM d, yyyy')} at ${format(new Date(), 'h:mm a')}.
    `;
  };

  if (!isOpen) return null;
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h2 className="text-xl font-bold mb-4">Send Report via Email</h2>
        
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Report Type
          </label>
          <div className="p-2 bg-gray-100 rounded">
            {reportTitle || reportType}
          </div>
        </div>
        
        <div className="mb-4">
          <label className="flex items-center text-sm font-medium text-gray-700 mb-1">
            <input
              type="checkbox"
              checked={useCustomEmail}
              onChange={() => setUseCustomEmail(!useCustomEmail)}
              className="mr-2"
            />
            Use custom email address
          </label>
          
          {useCustomEmail ? (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Recipient Email
              </label>
              <input
                type="email"
                value={customEmail}
                onChange={(e) => setCustomEmail(e.target.value)}
                placeholder="Enter email address"
                className="w-full p-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          ) : (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Select Recipient
              </label>
              <select
                value={selectedRecipient}
                onChange={(e) => setSelectedRecipient(e.target.value)}
                className="w-full p-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={loading}
              >
                <option value="">Select a recipient</option>
                {recipients.map((user) => (
                  <option key={user.id} value={user.id}>
                    {user.name || user.username || user.email}
                  </option>
                ))}
              </select>
            </div>
          )}
        </div>
        
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Message (Optional)
          </label>
          <textarea
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            placeholder="Add a message to the email"
            className="w-full p-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            rows={3}
          />
        </div>
        
        <div className="flex justify-end space-x-2">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-200 rounded hover:bg-gray-300 transition-colors"
            disabled={sending}
          >
            Cancel
          </button>
          <button
            onClick={handleSendEmail}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            disabled={sending}
          >
            {sending ? 'Sending...' : 'Send Report'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default EmailReportModal;



