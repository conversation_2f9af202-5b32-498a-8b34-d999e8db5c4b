{"common": {"creating": "Création en cours...", "saving": "Enregistrement en cours...", "header": {"search": "Rechercher...", "notifications": "Notifications", "mark_all_read": "Tout marquer comme lu", "no_notifications": "Aucune notification.", "show_more": "Afficher plus de notifications", "more": "plus", "check_profile": "Voir le profil", "help": "Aide", "logout": "Déconnexion", "language": "<PERSON><PERSON>"}, "sidebar": {"toggle_menu": "Basculer le menu"}, "tabs": {"overview": "Vue d'ensemble", "features": "Caractéristiques", "details": "Détails", "actions": "Actions", "activity": "Activité", "evaluation": "Évaluation"}, "buttons": {"save": "Enregistrer", "cancel": "Annuler", "add": "Ajouter", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "Mettre à jour", "back": "Retour", "next": "Suivant", "previous": "Précédent", "submit": "So<PERSON><PERSON><PERSON>", "confirm": "Confirmer", "search": "<PERSON><PERSON><PERSON>", "view": "Voir", "retry": "<PERSON><PERSON><PERSON><PERSON>"}, "status": {"active": "Actif", "inactive": "Inactif", "pending": "En attente", "completed": "<PERSON><PERSON><PERSON><PERSON>", "in_progress": "En cours", "cancelled": "<PERSON><PERSON><PERSON>", "delayed": "Retardé", "all": "Tous les statuts", "open": "Ouvert", "resolved": "R<PERSON>ol<PERSON>"}, "priority": {"all": "Toutes les priorités", "high": "Haute", "medium": "<PERSON><PERSON><PERSON>", "low": "<PERSON><PERSON>"}, "fields": {"name": "Nom", "description": "Description", "status": "Statut", "type": "Type", "category": "<PERSON><PERSON><PERSON><PERSON>", "date": "Date", "start_date": "Date de début", "end_date": "Date de fin", "created_at": "<PERSON><PERSON><PERSON>", "updated_at": "Mis à jour le", "created_by": "C<PERSON><PERSON> par", "updated_by": "Mis à jour par", "comments": "Commentaires", "actions": "Actions", "title": "Titre", "priority": "Priorité", "reported_date": "Date de signalement", "last_updated": "Dernière mise à jour"}}, "admin": {"sidebar": {"home": "Accueil", "dashboard": "Tableau de bord", "incidents": "Incidents", "incidents_list": "Liste des incidents", "create_incident": "<PERSON><PERSON><PERSON> un incident", "ai_assistant": "Assistant IA", "incident_types": "Types d'incidents", "risks": "Risques", "risks_list": "Liste des risques", "risk_types": "Types de risques", "controls": "<PERSON><PERSON><PERSON><PERSON>", "processes": "Processus", "tree_view": "Vue arborescente", "business_processes": "<PERSON><PERSON> m<PERSON>s", "organizational_processes": "Processus org.", "operations": "Opérations", "environment": "Environnement", "entities": "Entités", "control_types": "Types de contrôle", "business_lines": "<PERSON><PERSON><PERSON> m<PERSON>", "applications": "Applications", "action_plans": "Plans d'action", "actions": "Actions", "reports": "Rapports"}, "action_plans": {"title": "Gestion des plans d'action", "description": "Définir et gérer les plans d'action au sein de votre organisation.", "loading": "Chargement des plans d'action...", "error": "Erreur: {{message}}", "not_found": "Plan d'action non trouvé", "back_to_list": "Retour aux plans d'action", "delete_confirm": "Êtes-vous sûr de vouloir supprimer {{count}} plan(s) d'action ?", "error_creating": "<PERSON><PERSON>ur lors de la création du plan d'action: {{error}}", "search_placeholder": "Rechercher des plans d'action...", "filters": {"approver": "Approbateur", "assignee": "Responsable", "category": "<PERSON><PERSON><PERSON><PERSON>", "nature": "Nature", "origin": "Origine", "priority": "Priorité", "organizational_level": "Niveau organisationnel", "means": "Moyens", "select_approver": "Sélectionner un approbateur", "select_assignee": "Sélectionner un responsable", "select_category": "Sélectionner une catégorie", "select_nature": "Sélectionner une nature", "select_origin": "Sélectionner une origine", "select_priority": "Sélectionner une priorité", "select_organizational_level": "Sélectionner un niveau organisationnel", "select_means": "Sélectionner des moyens"}, "columns": {"name": "Nom", "approver": "Approbateur", "assignee": "Responsable", "category": "<PERSON><PERSON><PERSON><PERSON>", "nature": "Nature", "origin": "Origine", "priority": "Priorité", "organizational_level": "Niveau organisationnel", "means": "Moyens", "comment": "Commentaire", "planned_begin_date": "Date de début planifiée", "planned_end_date": "Date de fin planifiée"}, "form": {"name": "Nom", "nature": "Nature", "comment": "Commentaire", "name_placeholder": "Nom du plan d'action", "nature_placeholder": "Nature du plan d'action", "comment_placeholder": "Commentaire sur le plan d'action"}, "tabs": {"overview": "Vue d'ensemble", "features": "Caractéristiques", "actions": "Actions", "progress": "Rapport de progression", "activity": "Activité", "workflow": "Workflow"}, "overview": {"title": "Vue d'ensemble du plan d'action", "details": "Détails", "timeline": "Chronologie", "description": "Description", "category": "<PERSON><PERSON><PERSON><PERSON>", "nature": "Nature", "origin": "Origine", "organizational_level": "Niveau organisationnel", "approver": "Approbateur", "assignee": "Responsable", "means": "Moyens", "planned_begin_date": "Date de début planifiée", "planned_end_date": "Date de fin planifiée", "duration": "<PERSON><PERSON><PERSON>", "days": "jours", "not_set": "Non défini", "not_specified": "Non spécifié", "not_assigned": "Non assigné", "not_available": "Non disponible", "no_description": "Aucune description fournie."}, "actions": {"title": "Actions", "add_action": "Ajouter une action", "no_actions": "Aucune action trouvée pour ce plan d'action.", "create_first": "<PERSON><PERSON><PERSON> votre première action en cliquant sur le bouton ci-dessus.", "action_name": "Nom de l'action", "action_name_placeholder": "Entrez le nom de l'action", "status": "Statut", "assignee": "Responsable", "due_date": "Date d'échéance", "progress": "Progression", "add_new_action": "Ajouter une nouvelle action", "edit_action": "Modifier l'action", "action_details": "Détails de l'action", "description": "Description", "description_placeholder": "Entrez la description de l'action", "save_action": "Enregistrer l'action", "update_action": "Mettre à jour l'action", "delete_action_confirm": "Êtes-vous sûr de vouloir supprimer cette action ?", "link_actions": "Lier des actions existantes", "link_actions_desc": "Sélectionnez des actions existantes à lier à ce plan d'action.", "link_action": "Lier une action existante", "link_selected": "Lier la sélection", "search_actions": "Rechercher des actions...", "no_actions_to_link": "Aucune action disponible à lier", "add_action_desc": "Ajouter une nouvelle action à ce plan d'action.", "update_action_desc": "Mettre à jour les détails de cette action."}, "features": {"coming_soon": "Fonctionnalités à venir", "description": "Cette section affichera les caractéristiques détaillées de ce plan d'action.", "update_success": "Plan d'action mis à jour avec succès", "update_error": "Échec de la mise à jour du plan d'action", "update_action_plan": "Mettre à jour le plan d'action"}, "activity": {"coming_soon": "Suivi d'activité à venir", "description": "Cette section affichera les journaux d'activité et l'historique de ce plan d'action."}, "workflow": {"coming_soon": "Gestion des Workflows à venir", "description": "Cette section vous permettra de gérer les Workflows et les processus liés à ce plan d'action."}, "nature": {"preventive": "Préventif", "corrective": "Correctif"}, "origin": {"audit": "Audit", "compliance": "Conformité", "event": "Événement", "risk": "Risque", "rfc": "RFC", "other": "<PERSON><PERSON>"}, "organizational_level": {"local": "Local", "global": "Global"}}, "control_types": {"title": "Gestion des Types de Contrôle", "description": "Définir et gérer les types de contrôle au sein de votre organisation.", "search_placeholder": "Rechercher des types de contrôle...", "back_to_list": "Retour aux Types de Contrôle", "buttons": {"add": "Ajouter un Type de Contrôle", "create": "Créer un Type de Contrôle", "update": "Mettre à jour le Type de Contrôle"}, "columns": {"name": "Nom", "code": "Code", "comment": "Commentaire", "parent": "Type de Contrôle Parent"}, "form": {"name": "Nom", "name_placeholder": "Entrez le nom du type de contrôle", "code": "Code", "code_placeholder": "Entrez le code", "comment": "Commentaire", "comment_placeholder": "Entrez un commentaire", "parent": "Type de Contrôle Parent", "parent_placeholder": "Sélectionnez un type de contrôle parent", "none": "Aucun"}, "filters": {"parent": "Type de Contrôle Parent", "select_parent": "Sélectionnez un type de contrôle parent"}, "dialog": {"title": "Ajouter un Nouveau Type de Contrôle", "description": "Remplis<PERSON>z les détails pour créer un nouveau type de contrôle.", "edit_title": "Modifier le Type de Contrôle", "edit_description": "Mettre à jour les détails du type de contrôle."}, "overview": {"title": "Vue d'ensemble du Type de Contrôle", "details": "Détails", "not_available": "N/D", "unknown": "Inconnu", "no_code": "Pas de code", "no_description": "Pas de description", "created_at": "<PERSON><PERSON><PERSON>"}, "features": {"title": "Modifier le Type de Contrôle"}, "items_found": "{{count}} type(s) de contrôle trouvé(s)", "confirm": {"delete": "Êtes-vous sûr de vouloir supprimer {{count}} type(s) de contrôle sélectionné(s) ?"}, "success": {"created": "Type de contrôle créé avec succès", "updated": "Type de contrôle mis à jour avec succès", "deleted": "{{count}} type(s) de contrôle supprimé(s) avec succès"}, "error": {"fetch_failed": "Erreur lors de la récupération des types de contrôle :", "fetch_failed_toast": "Échec de la récupération des types de contrôle", "create_failed": "Erreur lors de la création du type de contrôle :", "create_failed_toast": "Échec de la création du type de contrôle", "update_failed": "Erreur lors de la mise à jour du type de contrôle :", "update_failed_toast": "Échec de la mise à jour du type de contrôle", "delete_failed": "Erreur lors de la suppression des types de contrôle :", "delete_unexpected": "Une erreur inattendue s'est produite lors de la suppression des types de contrôle", "delete_parent": "Impossible de supprimer \"{{id}}\" : Il est référencé par d'autres types de contrôle comme parent. V<PERSON> devez d'abord supprimer ou réaffecter tous les types de contrôle enfants avant de supprimer ce parent.", "delete_in_use": "Impossible de supprimer \"{{id}}\" : Il est utilisé par un ou plusieurs contrôles. <PERSON><PERSON> devez d'abord mettre à jour ou supprimer tous les contrôles utilisant ce type de contrôle avant de le supprimer.", "delete_failed_item": "Échec de la suppression de {{id}} : {{error}}", "loading": "Erreur : {{message}}", "not_found": "Type de contrôle non trouvé", "self_parent": "Un type de contrôle ne peut pas être son propre parent", "invalid_parent": "Type de contrôle parent invalide. Veuillez sélectionner un parent valide ou 'Aucun'.", "server_error": "Erreur serveur. Veuillez consulter la console pour plus de détails."}}, "controls": {"title": "<PERSON><PERSON><PERSON><PERSON>", "list_title": "Liste des Contrôles", "create_title": "<PERSON><PERSON>er un Contrôle", "edit_title": "Modifier le Contrôle", "details": "<PERSON><PERSON><PERSON> du Contrôle", "no_controls": "Au<PERSON>n contrôle trouvé", "management": {"title": "Gestion des Contrôles", "description": "Définir et gérer les contrôles au sein de votre organisation.", "search_placeholder": "Rechercher des contrôles...", "loading": "Chargement des contrôles...", "error": "Erreur : {{message}}", "no_controls": "Au<PERSON>n contrôle trouvé", "delete_confirm": "Êtes-vous sûr de vouloir supprimer {{count}} contrôle(s) sélectionné(s) ?", "delete_success": "Les contrôles sélectionnés ont été supprimés avec succès", "delete_error": "Une erreur s'est produite lors du processus de suppression", "delete_failed": "Échec de la suppression du contrôle {{id}} : {{error}}", "create_dialog": {"title": "Créer un Nouveau Contrôle", "name": "Nom", "code": "Code", "control_key": "Contrôle Clé", "execution_method": "Méthode d'Exécution", "name_placeholder": "Entrez le nom du contrôle", "code_placeholder": "Entrez le code du contrôle", "control_key_placeholder": "Sélectionnez le contrôle clé", "execution_method_placeholder": "Sélectionnez la méthode d'exécution", "required": "requis"}, "validation": {"name_required": "Le nom est requis", "control_key_required": "Le Contrôle Clé est requis", "execution_method_required": "La Méthode d'Exécution est requise", "control_key_integer": "Le Contrôle Clé doit être 0 ou 1."}, "buttons": {"add": "Ajouter un Contrôle", "delete": "<PERSON><PERSON><PERSON><PERSON>", "deleting": "Suppression en cours...", "create": "<PERSON><PERSON>er un Contrôle", "creating": "Création en cours...", "cancel": "Annuler"}, "filters": {"testing_frequency": "<PERSON><PERSON><PERSON>", "sample_type": "Type d'Échantillon", "testing_method": "<PERSON><PERSON><PERSON><PERSON>", "select_frequency": "Sélectionner la fréquence", "select_type": "Sélectionner le type", "select_method": "Sélectionner la méthode"}, "columns": {"name": "Nom", "code": "Code", "control_key": "Contrôle Clé", "testing_frequency": "<PERSON><PERSON><PERSON>", "sample_type": "Type d'Échantillon", "testing_method": "<PERSON><PERSON><PERSON><PERSON>"}}, "edit": {"back_to_controls": "Retour aux Contrôles", "control_not_found": "Contrôle non trouvé", "error_loading": "Échec du chargement des données du contrôle. Veuillez réessayer.", "error_refresh": "Échec de l'actualisation des données du contrôle", "error_tab_change": "Erreur lors du changement d'onglets. Veuillez réessayer.", "permission_error": "Vous n'avez pas la permission d'accéder à cette page", "unnamed": "Contrôle Sans Nom", "metadata": {"code": "Code : {{value}}", "code_na": "Code : N/A", "control_key": "Contrôle Clé : {{value}}", "organizational_level": "Niveau Organisationnel : {{value}}", "organizational_level_na": "Niveau Organisationnel : N/A"}, "tabs": {"overview": "Vue d'ensemble", "features": "Caractéristiques", "action_plan": "Plan d'Action", "evaluation": "Évaluation"}}, "features": {"title": "Modifier le Contrôle", "create_title": "<PERSON><PERSON>er un Contrôle", "id": "ID", "sections": {"basic": "Informations de Base", "details": "<PERSON><PERSON><PERSON> du Contrôle", "testing": "Détails des Tests", "reference": "<PERSON><PERSON><PERSON> de Référence", "descriptions": "Descriptions"}, "form": {"objective": "Objectif", "execution_procedure": "Procédure d'Exécution", "comment": "Commentaire", "name": "Nom", "code": "Code", "control_key": "Contrôle Clé", "control_execution_method": "Méthode d'Exécution du Contrôle", "operational_cost": "Coût Opérationnel", "organizational_level": "Niveau Organisationnel", "sample_type": "Type d'Échantillon", "testing_frequency": "<PERSON><PERSON><PERSON>", "testing_method": "<PERSON><PERSON><PERSON><PERSON>", "testing_population_size": "Taille de la Population de Test", "testing_procedure": "<PERSON>cé<PERSON><PERSON>", "select_control_type": "Sélectionner un type de contrôle", "search_control_types": "Rechercher des types de contrôle...", "select_business_process": "Sélectionner un processus métier", "search_business_processes": "Rechercher des processus métiers...", "select_organizational_process": "Sélectionner un processus organisationnel", "search_organizational_processes": "Rechercher des processus organisationnels...", "select_operation": "Sélectionner une opération", "search_operations": "Rechercher des opérations...", "select_application": "Sélectionner une application", "search_applications": "Rechercher des applications...", "select_entity": "Sélectionner une entité", "search_entities": "Rechercher des entités...", "select_risk": "Sélectionner un risque", "search_risks": "Rechercher des risques..."}, "buttons": {"save": "Enregistrer les Modifications", "create": "<PERSON><PERSON>er un Contrôle"}, "success": {"created": "Contrôle créé avec succès", "updated": "Contrôle mis à jour avec succès"}, "error": {"name_required": "Le nom est requis", "update": "Erreur lors de la mise à jour du contrôle :", "foreign_key": "Le {{fieldName}} avec l'ID {{fieldValue}} n'existe pas dans la base de données.", "foreign_key_constraint": "Erreur de contrainte de clé étrangère : Une des références sélectionnées n'existe pas.", "retry": "Erreur lors de la nouvelle tentative :", "retry_failed": "Échec de l'enregistrement du contrôle même après une nouvelle tentative", "save_failed": "Échec de l'enregistrement du contrôle", "reference_data": "Erreur lors du chargement des données de référence :", "reference_data_refresh": "Échec du chargement de certaines données de référence. Veuillez rafraîchir la page."}}, "overview": {"title": "Vue d'ensemble du Contrôle", "basic_information": "Informations de Base", "testing_details": "Détails des Tests", "reference_data": "<PERSON><PERSON><PERSON> de Référence", "descriptions": "Descriptions", "name": "Nom", "code": "Code", "control_key": "Contrôle Clé", "operational_cost": "Coût Opérationnel", "organizational_level": "Niveau Organisationnel", "control_execution_method": "Méthode d'Exécution du Contrôle", "sample_type": "Type d'Échantillon", "testing_frequency": "<PERSON><PERSON><PERSON>", "testing_method": "<PERSON><PERSON><PERSON><PERSON>", "testing_population_size": "Taille de la Population de Test", "testing_procedure": "<PERSON>cé<PERSON><PERSON>", "control_type": "Type de Contrôle", "business_process": "<PERSON><PERSON>", "organizational_process": "Processus Organisationnel", "operation": "Opération", "application": "Application", "entity": "Entité", "associated_risk": "<PERSON><PERSON><PERSON>", "action_plan": "Plan d'Action", "objective": "Objectif", "execution_procedure": "Procédure d'Exécution", "comment": "Commentaire"}, "evaluation": {"title": "Évaluation du Contrôle", "assessment": "Évaluation du Contrôle", "permission_error": "Vous n'avez pas la permission de mettre à jour ce contrôle", "success": {"updated": "Évaluation du contrôle mise à jour avec succès"}, "error": {"update": "Échec de la mise à jour de l'évaluation du contrôle"}, "design_quality": {"question": "Quel est le niveau de qualité de conception du controle?", "satisfactory": "Satisfaisant", "unsatisfactory": "Insatisfaisant"}, "effectiveness": {"question": "Quel est le niveau d'efficacité du controle?", "satisfactory": "Satisfaisant", "unsatisfactory": "Insatisfaisant", "auto_set": "Le niveau d'efficacité est automatiquement défini comme Insatisfaisant lorsque la qualité de conception est Insatisfaisante"}}, "action_plan": {"title": "Plan d'Action du Contrôle", "loading": "Chargement des plans d'action...", "linking": "Liaison du plan d'action...", "no_linked": "Aucun Plan d'Action Lié", "no_linked_description": "Liez un plan d'action existant ou créez-en un nouveau pour aider à mettre en œuvre ce contrôle.", "no_nature": "Aucune nature spécifiée", "no_plans": "Aucun plan d'action disponible", "no_search_results": "Aucun plan d'action ne correspond à votre recherche", "id": "ID", "search_placeholder": "Rechercher des plans d'action...", "buttons": {"create": "Créer un Plan d'Action", "link": "Lier un Plan d'Action", "unlink": "Délier"}, "form": {"name": "Nom", "name_placeholder": "Entrez le nom du plan d'action", "nature": "Nature", "nature_placeholder": "Sélectionnez la nature", "preventive": "Préventif", "corrective": "Correctif", "comment": "Commentaire", "comment_placeholder": "Entrez un commentaire"}, "dialog": {"create_title": "Créer un Nouveau Plan d'Action", "create_description": "Remp<PERSON><PERSON>z les détails ci-dessous pour créer un nouveau plan d'action.", "link_title": "Lier un Plan d'Action Existant", "link_description": "Sélectionnez un plan d'action à lier à ce contrôle."}, "success": {"created_linked": "Plan d'action créé et lié avec succès", "linked": "Plan d'action lié avec succès", "unlinked": "Plan d'action délié avec succès"}, "error": {"create": "Erreur lors de la création du plan d'action :", "create_failed": "Échec de la création du plan d'action", "link": "Erreur lors de la liaison du plan d'action :", "link_failed": "Échec de la liaison du plan d'action", "unlink": "Erreur lors de la déliaison du plan d'action :", "unlink_failed": "Échec de la déliaison du plan d'action", "control_missing": "Contrôle non disponible ou ID de contrôle manquant", "control_refresh": "Contrôle non disponible. Veuillez rafraîchir la page.", "data_not_available": "Données de contrôle non disponibles", "data_refresh": "Les données de contrôle ne sont pas correctement chargées. Veuillez rafraîchir la page."}, "confirm": {"unlink": "Êtes-vous sûr de vouloir délier ce plan d'action ?"}}}, "business_lines": {"title": "Gestion des Lignes Métiers", "description": "Définir et gérer les lignes métiers au sein de votre organisation.", "search_placeholder": "Rechercher des lignes métiers...", "back_to_list": "Retour aux Lignes Métiers", "buttons": {"add": "A<PERSON>ter une Ligne Métier", "create": "<PERSON><PERSON><PERSON> une Ligne Métier", "update": "Mettre à jour la Ligne Métier"}, "columns": {"name": "Nom", "description": "Description"}, "form": {"name": "Nom", "name_placeholder": "Entrez le nom de la ligne métier", "description": "Description", "description_placeholder": "Entrez une description"}, "dialog": {"title": "Ajouter une Nouvelle Ligne Métier", "description": "Remp<PERSON><PERSON>z les détails pour créer une nouvelle ligne métier.", "edit_title": "Modifier la Ligne Métier", "edit_description": "Mettre à jour les détails de la ligne métier."}, "overview": {"title": "Vue d'ensemble de la Ligne Métier", "details": "Détails", "no_description": "Aucune description fournie"}, "features": {"title": "Modifier la Ligne Métier"}, "confirm": {"delete": "Êtes-vous sûr de vouloir supprimer {{count}} ligne(s) métier sélectionnée(s) ?"}, "error": {"fetch_failed": "Erreur lors de la récupération des lignes métiers :", "create_failed": "Erreur lors de la création de la ligne métier :", "update_failed": "Échec de la mise à jour de la ligne métier", "delete_failed": "Erreur lors de la suppression des lignes métiers :", "loading": "Erreur : {{message}}", "not_found": "Ligne métier non trouvée"}}, "applications": {"title": "Gestion des Applications", "description": "Définir et gérer les applications utilisées par votre organisation.", "search_placeholder": "Rechercher des applications...", "back_to_list": "Retour aux Applications", "buttons": {"add": "Ajouter une Application", "create": "<PERSON><PERSON><PERSON> une Application"}, "columns": {"name": "Nom", "comment": "Commentaire"}, "form": {"name": "Nom", "name_placeholder": "Entrez le nom de l'application", "comment": "Commentaire", "comment_placeholder": "Entrez un commentaire"}, "dialog": {"title": "Ajouter une Nouvelle Application", "description": "Remplissez les détails pour créer une nouvelle application."}, "overview": {"title": "Vue d'ensemble de l'Application", "details": "Détails", "no_comment": "Aucun commentaire fourni"}, "features": {"title": "Modifier l'Application"}, "version": "Version", "vendor": "Fournisseur", "status": {"active": "Actif", "inactive": "Inactif"}, "confirm": {"delete": "Êtes-vous sûr de vouloir supprimer {{count}} application(s) sélectionnée(s) ?"}, "success": {"created": "Application créée avec succès", "deleted": "Toutes les applications sélectionnées ont été supprimées avec succès"}, "error": {"fetch_failed": "Échec de la récupération des applications", "create_failed": "Échec de la création de l'application", "update_failed": "Échec de la mise à jour de l'application", "none_selected": "Aucune application sélectionnée", "delete_referenced": "Impossible de supprimer l'application {{id}} : Elle est référencée par d'autres enregistrements. Veuillez réaffecter ou supprimer les enregistrements dépendants d'abord.", "delete_multiple_failed": "Échec de la suppression de {{count}} application(s) : {{message}}", "delete_process": "Une erreur s'est produite lors du processus de suppression", "loading": "Erreur lors du chargement des données de l'application. Veuillez réessayer.", "not_found": "Aucune application trouvée avec l'ID : {{id}}"}}, "dashboard": {"title": "Tableau de bord", "welcome": "Bienvenue sur Vitalis GRC", "summary": "Résumé", "recent_activity": "Activité récente", "statistics": "Statistiques", "risks": "Risques", "incidents": "Incidents", "controls": "<PERSON><PERSON><PERSON><PERSON>", "processes": "Processus", "loading": "Chargement des données du tableau de bord...", "total_risks": "<PERSON><PERSON><PERSON> to<PERSON>", "total_incidents": "Incidents to<PERSON>ux", "high_priority": "Haute priorité", "action_plans": "Plans d'action", "active_risk_monitoring": "Surveillance active des risques", "incident_management": "Gestion des incidents", "critical_attention": "Attention critique requise", "last_update": "<PERSON><PERSON><PERSON> mise à jour {{time}}", "no_updates": "<PERSON><PERSON>ne mise à jour récente", "recent_items": "Risques et incidents récents", "no_recent_items": "Aucun risque ou incident récent trouvé.", "showing_items": "Affichage de {{count}} éléments récents", "charts": {"incidents_overview": "Vue d'ensemble des incidents", "incidents_over_time": "Nombre d'incidents dans le temps", "incidents_by_type": "Incidents par type", "losses_over_time": "<PERSON><PERSON> dans le temps", "losses_by_type": "Pertes par type d'incident", "net_loss_by_risk": "Perte nette par type de risque"}}, "entities": {"title": "Entités", "description": "<PERSON><PERSON><PERSON> vos entités ici", "back": "Retour aux entités", "not_found": "Entité non trouvée", "error": "<PERSON><PERSON><PERSON>", "loading": "Chargement des entités...", "confirm_delete": "Êtes-vous sûr de vouloir supprimer {{count}} entité(s) sélectionnée(s) ?", "success": {"delete": "Toutes les entités sélectionnées ont été supprimées avec succès"}, "errors": {"no_selection": "Aucune entité sélectionnée", "cannot_delete_parent": "Impossible de supprimer \"{{name}}\" (ID: {{id}}) car c'est un parent de : {{dependents}}.", "unknown": "<PERSON><PERSON><PERSON> inconnue", "referenced": "Impossible de supprimer l'entité {{id}} : Elle est référencée par d'autres enregistrements. Veuillez réassigner ou supprimer d'abord les enregistrements dépendants.", "delete_failed": "Échec de la suppression de {{count}} entité(s) : {{error}}", "delete_failed_generic": "Échec de la suppression des entités", "refresh_failed": "Échec de l'actualisation des données de l'entité"}, "badges": {"external": "Externe", "internal": "Interne"}, "metadata": {"type": "Type", "no_type": "Aucun type", "no_comment": "Aucun commentaire"}, "tabs": {"overview": "Vue d'ensemble", "features": "Caractéristiques"}, "features": {"title": "Modifier l'entité", "form": {"name": "Nom", "name_placeholder": "Entrez le nom de l'entité", "code": "Code", "code_placeholder": "Entrez le code de l'entité", "type": "Type", "type_placeholder": "Sélectionnez le type d'entité", "branch": "Agence", "subsidiary": "Filiale", "head_office": "Siège", "internal_external": "Interne/Externe", "internal_external_placeholder": "Sélectionnez interne/externe", "internal": "Interne", "external": "Externe", "local_currency": "Devise locale", "local_currency_placeholder": "ex. : USD, EUR", "parent_entity": "Entité parente", "parent_entity_placeholder": "Sélectionnez l'entité parente", "comment": "Commentaire", "comment_placeholder": "Entrez un commentaire", "none": "Aucun"}, "buttons": {"saving": "Enregistrement...", "save": "Enregistrer les modifications"}}, "overview": {"title": "Détails de l'entité", "fields": {"name": "Nom", "code": "Code", "type": "Type", "internal_external": "Interne/Externe", "internal": "Interne", "external": "Externe", "local_currency": "Devise locale", "parent_entity": "Entité parente", "comment": "Commentaire", "created_at": "<PERSON><PERSON><PERSON>", "none": "N/A"}}, "buttons": {"add": "Ajouter une entité", "delete": "<PERSON><PERSON><PERSON><PERSON>", "deleting": "Suppression...", "create": "<PERSON><PERSON>er une entité", "creating": "Création...", "update": "Mettre à jour l'entité", "updating": "Mise à jour..."}, "dialog": {"create_title": "<PERSON><PERSON>er une nouvelle entité", "create_description": "Remp<PERSON><PERSON>z les détails ci-dessous pour créer une nouvelle entité. Les champs marqués d'un * sont obligatoires.", "update_title": "Mettre à jour l'entité", "update_description": "Modifiez les champs ci-dessous pour mettre à jour les détails de l'entité."}}, "risks": {"title": "Risques", "list_title": "Liste des Risques", "create_title": "Créer un Risque", "edit_title": "Modifier le Risque", "details": "Dé<PERSON> du Risque", "no_risks": "Aucun risque trouvé", "management": {"title": "Gestion des Risques", "description": "Identifier, évaluer et gérer les risques dans votre organisation. Suivre le statut des risques et mettre en œuvre des stratégies d'atténuation.", "search_placeholder": "Rechercher des risques...", "loading": "Chargement des risques...", "loading_reference_data": "Chargement des données de référence...", "error": "Erreur : {{message}}", "no_risks": "Aucun risque trouvé", "delete_confirm": "Êtes-vous sûr de vouloir supprimer {{count}} risque(s) sélectionné(s) ?", "delete_success": "{{count}} risque(s) supprimé(s) avec succès", "delete_error": "Une erreur inattendue s'est produite lors de la suppression", "delete_failed": "Échec de la suppression du risque {{id}} : {{error}}", "create_dialog": {"title": "Créer un Nouveau Risque", "description": "Remp<PERSON><PERSON>z les détails ci-dessous pour créer un nouveau risque. Les champs marqués d'un * sont obligatoires.", "name": "Nom *", "code": "Code", "comment": "Commentaire", "name_placeholder": "Entrez le nom du risque", "code_placeholder": "Entrez le code du risque", "comment_placeholder": "Entrez un commentaire", "required": "obligatoire"}, "validation": {"name_required": "Le nom est obligatoire"}, "buttons": {"add": "Ajouter un Risque", "delete": "<PERSON><PERSON><PERSON><PERSON>", "deleting": "Suppression...", "create": "Créer un Risque", "creating": "Création...", "cancel": "Annuler"}, "filters": {"impact": "Impact", "control_level": "Niveau de Contrôle", "probability": "Probabilité", "appetite": "Appétence", "mitigating_action_plan": "Plan d'Action d'Atténuation", "business_process": "<PERSON><PERSON>", "organizational_process": "Processus Organisationnel", "operation": "Opération", "application": "Application", "entity": "Entité", "risk_type": "Type de Risque", "control": "Contr<PERSON>le", "select_impact": "Sélectionner l'impact", "select_control_level": "Sélectionner le Niveau de Contrôle", "select_probability": "Sélectionner la probabilité", "select_appetite": "Sélectionner l'appétence", "search_action_plan": "Rechercher un plan d'action...", "select_business_process": "Sélectionner un processus métier", "select_org_process": "Sélectionner un processus org.", "select_operation": "Sélectionner une opération", "select_application": "Sélectionner une application", "select_entity": "Sélectionner une entité", "select_risk_type": "Sélectionner un type de risque", "select_control": "Sélectionner un contrôle", "all": "Tous", "very_low": "<PERSON><PERSON><PERSON>", "low": "Faible", "medium": "<PERSON><PERSON><PERSON>", "high": "<PERSON><PERSON><PERSON>", "very_high": "<PERSON><PERSON><PERSON>", "very_strong": "Très Fort", "strong": "Fort", "weak": "Faible", "very_weak": "<PERSON><PERSON><PERSON>"}, "evaluation": {"title": "Évaluation des Risques", "refresh": "Actualiser", "add_evaluation": "Ajouter une Évaluation", "current_assessment": "Évaluation Actuelle", "impact_label": "Impact", "probability_label": "Probabilité", "not_assessed": "Non évalué", "last_evaluated": "Dernière évaluation {{date}} par {{user}}", "history_title": "Historique des Évaluations", "no_history_title": "Aucun historique d'évaluation", "no_history_description": "Ajoutez des évaluations pour suivre l'évolution de ce risque dans le temps", "table": {"date": "Date", "user": "Utilisa<PERSON>ur", "impact": "Impact", "probability": "Probabilité", "notes": "Notes"}, "impact": {"very_low": "<PERSON><PERSON><PERSON>", "low": "Faible", "medium": "<PERSON><PERSON><PERSON>", "high": "<PERSON><PERSON><PERSON>", "very_high": "<PERSON><PERSON><PERSON>", "unknown": "Inconnu"}, "probability": {"very_low": "<PERSON><PERSON><PERSON>", "low": "Faible", "medium": "<PERSON><PERSON><PERSON>", "high": "<PERSON><PERSON><PERSON>", "very_high": "<PERSON><PERSON><PERSON>", "unknown": "Inconnu"}, "modal": {"title": "Nouvelle Évaluation de Risque", "description": "Mettre à jour l'évaluation du risque avec les informations actuelles", "current_user": "Utilisateur Actuel", "impact_label": "Impact", "probability_label": "Probabilité", "select_impact": "Sélectionner le niveau d'impact", "select_probability": "Sélectionner le niveau de probabilité", "notes_label": "Notes (Optionnel)", "notes_placeholder": "Ajouter toute information pertinente sur cette évaluation", "validation_required": "L'impact et la probabilité sont requis", "cancel": "Annuler", "save": "Enregistrer l'Évaluation", "saving": "Enregistrement..."}, "error_loading_data": "Échec du chargement des données d'évaluation des risques", "validation_required": "Veuillez sélectionner à la fois l'impact et la probabilité", "risk_not_available": "Données de risque non disponibles. Veuillez réessayer.", "success_added": "Évaluation ajoutée avec succès", "error_adding": "Échec de l'ajout de l'évaluation"}, "mitigation": {"title": "Atténuation", "strategy_title": "Stratégie", "residual_risk_label": "<PERSON><PERSON>que R<PERSON>", "appetite_label": "Appétence", "select_appetite": "Sélectionner le niveau d'appétence", "treatment_options": "Options de Traitement", "save_strategy": "Enregistrer la Stratégie", "saving": "Enregistrement...", "controls_title": "<PERSON><PERSON><PERSON><PERSON>", "unlink": "Délier", "control_key": "Clé de Contrôle", "organizational_level": "Niveau Organisationnel", "org_level_short": "Niveau Org", "yes": "O<PERSON>", "no": "Non", "na": "N/A", "no_control_linked": "Aucun contrôle lié à ce risque.", "create_or_link": "<PERSON><PERSON>er un nouveau contrôle ou lier un existant.", "create": "<PERSON><PERSON><PERSON>", "link": "<PERSON><PERSON>", "appetite": {"very_low": "<PERSON><PERSON><PERSON>", "low": "Faible", "medium": "<PERSON><PERSON><PERSON>", "high": "<PERSON><PERSON><PERSON>", "very_high": "<PERSON><PERSON><PERSON>"}, "residual_risk": {"na": "N/A", "critical": "Critique", "high": "<PERSON><PERSON><PERSON>", "medium": "<PERSON><PERSON><PERSON>", "low": "Faible", "very_low": "<PERSON><PERSON><PERSON>"}, "treatment": {"acceptance": "Acceptation", "avoidance": "Évitement", "insurance": "Assurance", "reduction": "Réduction"}, "create_modal": {"title": "Créer un Nouveau Contrôle", "description": "Remp<PERSON><PERSON>z les détails ci-dessous pour créer un nouveau contrôle.", "name_label": "Nom *", "name_placeholder": "Entrer le nom du contrôle", "control_key_label": "Clé de Contrôle", "select_control_key": "Sélectionner la clé de contrôle", "cancel": "Annuler", "create_control": "<PERSON><PERSON><PERSON> le Contrôle", "creating": "Création..."}, "link_modal": {"title": "Lier un Contrôle Existant", "description": "Sélectionner un contrôle à lier à ce risque.", "search_placeholder": "Rechercher des contrôles...", "no_controls_found": "Aucun contrôle trouvé. Essayez une recherche différente ou créez un nouveau contrôle.", "cancel": "Annuler"}, "strategy_updated": "Stratégie d'atténuation mise à jour avec succès", "strategy_update_failed": "Échec de la mise à jour de la stratégie d'atténuation", "control_created_linked": "Contrôle créé et lié avec succès", "control_create_failed": "Échec de la création du contrôle", "control_linked": "Contrôle lié avec succès", "control_link_failed": "Échec de la liaison du contrôle", "confirm_unlink": "Êtes-vous sûr de vouloir délier ce contrôle ?", "control_unlinked": "Contrôle dé<PERSON>é avec succès", "control_unlink_failed": "Échec de la déliaison du contrôle"}, "action_plan": {"title": "Plan d'Action de Risque", "create_action_plan": "Créer un Plan d'Action", "loading": "Chargement des plans d'action...", "created_linked_success": "Plan d'action créé et lié avec succès", "create_failed": "Échec de la création du plan d'action", "linked_success": "Plan d'action lié avec succès", "link_failed": "Échec de la liaison du plan d'action", "confirm_unlink": "Êtes-vous sûr de vouloir délier ce plan d'action ?", "unlinked_success": "Plan d'action délié avec succès", "unlink_failed": "Échec de la déliaison du plan d'action", "nature": "Nature", "no_nature": "Aucune nature spécifiée", "comment": "Commentaire", "unlink": "Délier", "no_linked_title": "Aucun Plan d'Action Lié", "no_linked_description": "Liez un plan d'action existant ou créez-en un nouveau pour aider à atténuer ce risque.", "create_modal": {"title": "Créer un Nouveau Plan d'Action", "description": "Remp<PERSON><PERSON>z les détails ci-dessous pour créer un nouveau plan d'action.", "name_label": "Nom *", "name_placeholder": "Entrer le nom du plan d'action", "nature_label": "Nature", "select_nature": "Sélectionner la nature", "preventive": "Préventif", "corrective": "Correctif", "comment_label": "Commentaire", "comment_placeholder": "Entrer un commentaire", "cancel": "Annuler", "create_action_plan": "Créer le Plan d'Action", "creating": "Création..."}, "link_modal": {"title": "Lier un Plan d'Action Existant", "description": "Sélectionner un plan d'action à lier à ce risque.", "search_placeholder": "Rechercher des plans d'action...", "linking": "Liaison du plan d'action...", "no_match": "Aucun plan d'action ne correspond à votre recherche", "no_available": "Aucun plan d'action disponible"}}, "columns": {"name": "Nom", "code": "Code", "impact": "Impact", "control_level": "Niveau de Contrôle", "probability": "Probabilité", "appetite": "Appétence", "method_of_identification": "Méthode d'Identification", "comment": "Commentaire", "mitigating_action_plan": "Plan d'Action d'Atténuation", "business_process": "<PERSON><PERSON>", "organizational_process": "Processus Organisationnel", "operation": "Opération", "application": "Application", "entity": "Entité", "risk_type": "Type de Risque", "control": "Contr<PERSON>le", "workflow": "Workflow"}, "edit": {"delete_button": "Supp<PERSON><PERSON> le risque", "delete_confirm": "Êtes-vous sûr de vouloir supprimer ce risque ?", "delete_success": "Risque supprimé avec succès", "delete_error": "Échec de la suppression du risque"}}, "overview": {"title": "Vue d'ensemble du Risque", "key_information": "Informations Clés", "name": "Nom", "code": "Code", "status": "Statut", "major_risk": "<PERSON><PERSON><PERSON>", "contributors": "Contributeurs", "loading_contributors": "Chargement des contributeurs...", "unknown_user": "Utilisa<PERSON><PERSON>", "method_of_identification": "Méthode d'Identification", "risk_assessment": "Évaluation du Risque", "impact": "Impact", "probability": "Probabilité", "control_level": "Niveau de Contrôle (DMR)", "inherent_risk": "Risque Inhérent", "residual_risk": "<PERSON><PERSON>que R<PERSON>", "mitigation": "Atténuation", "appetite": "Appétence", "treatment_options": "Options de Traitement", "acceptance": "Acceptation", "avoidance": "Évitement", "insurance": "Assurance", "reduction": "Réduction", "no_treatment_options": "Aucune option de traitement spécifiée", "comment": "Commentaire", "related_information": "Informations Connexes", "business_process": "<PERSON><PERSON>", "organizational_process": "Processus Organisationnel", "operation": "Opération", "application": "Application", "entity": "Entité", "risk_type": "Type de Risque", "control": "Contr<PERSON>le", "mitigating_action_plan": "Plan d'Action d'Atténuation", "attachments": "Pièces Jointes", "none": "N/A"}, "features": {"title": "Modifier les Détails du Risque", "create_title": "Créer un Risque", "loading": "Chargement des données du risque...", "basic_information": "Informations de Base", "name": "Nom", "code": "Code", "method_of_identification": "Méthode d'Identification", "select_method": "Sélectionner une méthode", "major_risk": "<PERSON><PERSON><PERSON>", "risk_assessment": "Évaluation du Risque", "control_level": "Niveau de Contrôle", "select_control_level": "Sélectionner le Niveau de Contrôle", "contributors": "Contributeurs", "save_first": "Enregistrez d'abord le risque pour ajouter des contributeurs", "related_information": "Informations Connexes", "business_process": "<PERSON><PERSON>", "organizational_process": "Processus Organisationnel", "operation": "Opération", "application": "Application", "entity": "Entité", "risk_type": "Type de Risque", "control": "Contr<PERSON>le", "mitigating_action_plan": "Plan d'Action d'Atténuation", "select_business_process": "Sélectionner un processus métier", "search_business_processes": "Rechercher des processus métiers...", "select_organizational_process": "Sélectionner un processus organisationnel", "search_organizational_processes": "Rechercher des processus organisationnels...", "select_operation": "Sélectionner une opération", "search_operations": "Rechercher des opérations...", "select_application": "Sélectionner une application", "search_applications": "Rechercher des applications...", "select_entity": "Sélectionner une entité", "search_entities": "Rechercher des entités...", "select_risk_type": "Sélectionner un type de risque", "search_risk_types": "Rechercher des types de risque...", "select_control": "Sélectionner un contrôle", "search_controls": "Rechercher des contrôles...", "select_action_plan": "Sélectionner un plan d'action", "search_action_plans": "Rechercher des plans d'action...", "none": "Aucun", "success": {"updated": "Risque mis à jour avec succès"}, "error": {"update": "Échec de la mise à jour du risque"}, "methods": {"survey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "incident_database": "Base de Données d'Incidents", "audit_mission": "Mission d'Audit", "workshop": "Atelier"}, "survey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "incident_database": "Base de Données d'Incidents", "audit_mission": "Mission d'Audit", "workshop": "Atelier", "comment": "Commentaire", "comment_placeholder": "Ajoutez vos commentaires ici...", "cancel": "Annuler", "save_changes": "Enregistrer les Modifications", "saving": "Enregistrement..."}, "contributors": {"error_loading": "Échec du chargement des contributeurs", "error_loading_users": "Échec du chargement des utilisateurs", "select_user": "Veuillez sélectionner un utilisateur à assigner", "no_risk": "Aucun risque sélectionné", "invalid_user": "Utilisateur sélectionné invalide", "error_assign": "Échec de l'assignation du contributeur", "invalid_contributor": "Contributeur invalide", "remove_confirm": "Êtes-vous sûr de vouloir supprimer ce contributeur ?", "error_remove": "Échec de la suppression du contributeur", "loading": "Chargement des contributeurs...", "no_contributors": "Aucun contributeur assigné à ce risque.", "unknown_user": "Utilisa<PERSON><PERSON>", "added_by": "Ajouté par", "no_permission_remove": "Vous n'avez pas la permission de supprimer des contributeurs", "assign_contributor": "Assigner un Contributeur", "assign_title": "Assigner un Contributeur de Risque", "assign_description": "Sélectionnez un utilisateur à assigner comme contributeur à ce risque :", "no_users": "Aucun utilisateur disponible à assigner", "cancel": "Annuler", "assign": "Assigner", "assigning": "Assignation...", "no_permission": "Vous avez besoin de la permission de mise à jour pour gérer les contributeurs"}, "attachments": {"error_loading": "Échec du chargement des pièces jointes", "error_loading_description": "Veuillez actualiser la page", "business_documents": "Documents Métier", "external_references": "Références Externes", "uploading": "Téléchargement...", "upload_document": "Télécharger un Document", "upload_reference": "Télécharger une Référence", "drop_files": "Déposez les fichiers ici", "no_business_docs": "Aucun document métier téléchargé", "drag_drop_hint": "G<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>z les fichiers ici ou cliquez sur le bouton de téléchargement", "file_restrictions": "Taille maximale : 50 Mo. Types de fichiers autorisés : PDF, documents Office, images, archives.", "drop_to_upload": "Déposez les fichiers pour télécharger", "name": "Nom", "size": "<PERSON><PERSON>", "date": "Date", "actions": "Actions", "download": "Télécharger", "delete": "<PERSON><PERSON><PERSON><PERSON>"}, "fields": {"name": "Nom", "description": "Description", "impact": "Impact", "probability": "Probabilité", "control_level": "Niveau de Contrôle", "inherent_risk": "Risque Inhérent", "residual_risk": "<PERSON><PERSON>que R<PERSON>", "risk_owner": "Propriétaire du Risque", "risk_type": "Type de Risque", "risk_category": "Catégor<PERSON> de Risque", "status": "Statut"}, "tabs": {"overview": "Vue d'ensemble", "features": "Caractéristiques", "evaluation": "Évaluation", "mitigation": "Atténuation", "action_plan": "Plan d'Action", "reports": "Rapports", "activity_feed": "Fil d'Activité", "workflow": "Workflow"}, "edit": {"back_to_risks": "Retour aux Risques", "risk_not_found": "Risque non trouvé", "error_loading": "Échec du chargement des données du risque. Veuillez réessayer.", "error_refresh": "Échec de l'actualisation des données du risque", "error_tab_change": "Erreur lors du changement d'onglet. Veuillez réessayer.", "permission_error": "Vous n'avez pas la permission d'accéder à cette page", "unnamed": "Risque Sans Nom", "delete_button": "Supp<PERSON><PERSON> le risque", "delete_confirm": "Êtes-vous sûr de vouloir supprimer le risque \"{{name}}\" ?", "delete_success": "Risque supprimé avec succès", "delete_error": "Échec de la suppression du risque", "delete_permission": "Vous n'avez pas la permission de supprimer des risques", "metadata": {"code": "Code : {{value}}", "code_na": "Code : N/A", "impact": "Impact : {{value}}", "control_level": "Niveau de Contrôle : {{value}}"}, "tabs": {"overview": "Vue d'ensemble", "features": "Caractéristiques", "evaluation": "Évaluation", "mitigation": "Atténuation", "action_plan": "Plan d'Action", "reports": "Rapports", "activity_feed": "Fil d'Activité", "workflow": "Workflow"}}, "reports": {"title": "Rapports", "risk_reports": "Rapports de risques", "placeholder": "Les rapports et analyses de risques seront affichés ici."}, "activity_feed": {"title": "Fil d'activité", "loading": "Chargement des activités...", "error_loading": "Échec du chargement des activités", "no_activities": "Aucune activité enregistrée pour le moment.", "created_risk": "a créé ce risque", "updated_risk": "a mis à jour le risque", "changed_workflow": "a changé l'état du workflow", "performed_action": "a effectué une action", "previous": "Précédent", "next": "Suivant", "page_info": "Page {{current}} sur {{total}}"}, "workflow": {"loading_workflow_data": "Chargement des données du workflow...", "loading_roles": "Chargement...", "unknown_role": "<PERSON><PERSON><PERSON> inconnu", "updating": "Mise à jour...", "error_label": "<PERSON><PERSON><PERSON>", "retry": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "Annuler", "error_missing_id": "L'ID du risque est manquant", "error_transitions": "Échec du chargement des transitions du workflow", "error_workflow_data": "Échec du chargement des données du workflow", "error_unauthorized": "Non autorisé : <PERSON><PERSON><PERSON><PERSON> vous reconnecter", "error_not_found": "Risque introuvable ou workflow non initialisé pour ce risque", "error_user_missing": "Les informations utilisateur sont manquantes. Veuillez vous reconnecter.", "error_no_permission": "Vous n'avez pas la permission d'effectuer cette action", "status": {"risk_created": "R<PERSON>que c<PERSON>", "to_submit": "À soumettre", "to_validate": "À valider", "validated": "<PERSON><PERSON><PERSON>", "rejected": "<PERSON><PERSON><PERSON>"}, "tabs": {"activity": "Activité", "participants": "Participants"}, "activity": {"risk_creator": "Créateur du risque", "no_activity": "Aucune activité enregistrée pour le moment.", "step_reached": "étape at<PERSON>", "performed_by": "Effectué par", "using_transition": "en utilisant la transition", "previous": "Précédent", "next": "Suivant", "page_of": "Page {{current}} sur {{total}}"}, "participants": {"no_participants": "Aucun participant enregis<PERSON><PERSON> pour le moment.", "table": {"name": "Nom", "role": "R<PERSON><PERSON>", "last_action": "Dernière action", "date": "Date"}, "creator_badge": "<PERSON><PERSON><PERSON><PERSON>"}, "reject_dialog": {"title": "Confirm<PERSON> le rejet", "message": "Êtes-vous sûr de vouloir rejeter ce risque ? Il sera réinitialisé à la première étape. Veuillez fournir une raison :", "placeholder": "Entrez la raison du rejet..."}, "buttons": {"submit": "So<PERSON><PERSON><PERSON>", "submit_for_validation": "Soumettre pour validation", "validate_risk": "Valider le risque", "reject": "<PERSON><PERSON><PERSON>"}, "permissions": {"no_reject_permission": "Vous n'avez pas la permission de rejeter les risques", "no_validate_permission": "Vous n'avez pas la permission de valider les risques", "no_advance_permission": "Vous n'avez pas la permission de faire avancer le workflow"}, "validation": {"rejection_reason_required": "Veuillez entrer une raison de rejet"}}}, "incidents": {"ai": {"prompt_required": "<PERSON>eu<PERSON>z saisir une invite", "failed_to_process": "Échec du traitement de votre demande. Veuillez réessayer.", "initializing": "Initialisation de l'assistant IA...", "title_section": "Incident", "title_section_ai": "Assistant IA", "use_ai": "Utilisez l'intelligence artificielle pour créer et analyser des incidents à partir de vos descriptions.", "describe_incident": "Décrire l'incident", "describe_incident_placeholder": "Décrivez l'incident en détail. Par exemple : 'Une panne du système de traitement des paiements a eu lieu hier à 15h et a duré 2 heures...'", "processing": "Traitement...", "generate_incident_details": "<PERSON><PERSON><PERSON>rer les détails de l'incident", "ai_analysis": "Analyse IA", "ai_response_will_appear": "La réponse de l'IA apparaîtra ici après la soumission de votre description.", "create_incident": "<PERSON><PERSON><PERSON> un incident", "name": "Nom *", "name_placeholder": "Nom de l'incident", "declared_by": "Déclaré par *", "declared_by_placeholder": "Nom du déclarant", "declaration_date": "Date de déclaration *", "detection_date": "Date de détection", "occurrence_date": "Date d'occurrence", "impact": "Impact", "select_impact_level": "Sélectionner le niveau d'impact", "very_low": "<PERSON><PERSON><PERSON> faible", "low": "Faible", "medium": "<PERSON><PERSON><PERSON>", "high": "<PERSON><PERSON><PERSON>", "very_high": "<PERSON><PERSON><PERSON>", "priority": "Priorité", "select_priority_level": "Sélectionner le niveau de priorité", "nature": "Nature", "nature_placeholder": "Nature de l'incident", "description": "Description", "description_placeholder": "Description de l'incident", "cancel": "Annuler"}, "add": {"failed_to_fetch_entity_data": "Échec du chargement des entités. Veuillez rafraîchir la page.", "missing_fields": "Champs obligatoires manquants : {{fields}}", "incident_created_successfully": "Incident cré<PERSON> avec succès", "failed_to_create_incident": "Échec de la création de l'incident", "title": "Ajouter un nouvel incident", "description": "C<PERSON>ez un nouvel incident avec les informations essentielles", "back_to_incidents": "Retour aux incidents", "name": "Nom *", "incident_name": "Nom de l'incident", "entity": "Entité *", "select_entity": "Sélectionner une entité", "no_entities_found": "Aucune entité trouvée.", "search_entities": "Rechercher des entités...", "detection_date": "Date de détection", "occurrence_date": "Date d'occurrence", "incident_description": "Description de l'incident", "cancel": "Annuler", "add_incident": "Ajouter l'incident"}, "edit": {"no_permission": "Vous n'avez pas la permission d'accéder à cet onglet", "delete_success": "Incident supprimé avec succès", "delete_error": "Échec de la suppression de l'incident", "delete_error_details": "Erreur lors de la suppression de l'incident : {0}", "reference_data_error": "Échec du chargement des données de référence", "load_data_error": "Échec du chargement des données de l'incident. Veuillez rafraîchir la page ou contacter le support.", "financial_entries_updated": "Entrées financières mises à jour avec succès", "financial_entries_update_error": "Échec de la mise à jour des entrées financières : {0}", "financial_entries_update_error_details": "L'incident a été mis à jour mais une erreur est survenue lors de la mise à jour des entrées financières : {0}", "incident_updated": "Incident mis à jour avec succès", "incident_update_error": "Échec de la mise à jour de l'incident", "error_loading_incident": "Erreur lors du chargement de l'incident", "back_to_incidents": "Retour aux incidents", "incident_overview": "Vue d'ensemble de l'incident", "impact": "Impact", "priority": "Priorité", "key_information": "Informations clés", "name": "Nom", "status": "Statut", "contributors": "Contributeurs", "loading_contributors": "Chargement des contributeurs...", "unknown_user": "Utilisateur inconnu", "none": "Aucun", "delete_confirm": "Êtes-vous sûr de vouloir supprimer cet incident ? Cette action ne peut pas être annulée.", "no_description": "Aucune description fournie", "incident_details": "<PERSON><PERSON><PERSON> de l'incident", "declared_by": "Déclaré par", "declaration_date": "Date de déclaration", "declarant_entity": "Entité du déclarant", "detection_date": "Date de détection", "occurrence_date": "Date d'occurrence", "near_miss": "Quasi-incident", "nature": "Nature", "financial_information": "Informations financières", "currency": "<PERSON><PERSON>", "gross_loss": "Perte brute", "recoveries": "Recouvrements", "provisions": "Provisions", "references": "Références", "action_plan": "Plan d'action", "risk": "Risque", "control": "Contr<PERSON>le", "entity": "Entité", "business_line": "<PERSON><PERSON>", "incident_type": "Type d'incident", "business_process": "<PERSON><PERSON>", "organizational_process": "Processus organisationnel", "product": "Produit", "application": "Application", "description": "Description", "attachments": "Pièces jointes", "select_tab": "Sé<PERSON><PERSON>ner un onglet", "edit_incident": "Modifier l'incident", "modify_incident_details": "Modifiez les détails de l'incident, l'évaluation de l'impact et les informations associées.", "incident": "Incident", "edit": "Modifier", "cancel": "Annuler", "save_changes": "Enregistrer les modifications"}, "tabs": {"overview": "Vue d'ensemble", "attachments": "Pièces jointes", "features": "Caractéristiques", "financial": "<PERSON><PERSON><PERSON> finan<PERSON>", "action_plan": "Plan d'action", "activity": "Fil d'activité", "workflow": "Workflow"}, "attachments": {"title": "Pièces jointes", "businessDocuments": "Documents métier", "externalReferences": "Références externes", "uploadDocument": "Télécharger un document", "uploading": "Téléchargement en cours...", "dropFilesHere": "Déposez les fichiers ici", "noBusinessDocumentsUploadedYet": "Aucun document métier téléchargé", "dragDropFiles": "G<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>z les fichiers ici ou cliquez sur le bouton de téléchargement", "maxFileSize": "Taille maximale : 50 Mo. Types de fichiers autorisés : PDF, documents Office, images, archives.", "name": "Nom", "size": "<PERSON><PERSON>", "date": "Date", "actions": "Actions", "download": "Télécharger", "delete": "<PERSON><PERSON><PERSON><PERSON>", "addReference": "Ajouter une référence", "noExternalReferencesAddedYet": "Aucune référence externe ajoutée", "clickAddReference": "Cliquez sur le bouton 'Ajouter une référence' pour ajouter un lien", "description": "Description", "addExternalReference": "Ajouter une référence externe", "addLink": "Ajouter un lien vers une ressource externe liée à cet incident.", "url": "URL", "brieflyDescribe": "Décrivez brièvement cette référence", "cancel": "Annuler", "failedToLoadAttachments": "Échec du chargement des pièces jointes", "filesTooLarge": "Fichiers trop volumineux", "unsupportedFileTypes": "Types de fichiers non pris en charge", "uploadSuccessful": "Téléchargement réussi", "uploadTimedOut": "<PERSON><PERSON><PERSON> de t<PERSON>léchargement dépassé", "fileTooLarge": "Fichier trop volumineux", "unsupportedFileType": "Type de fichier non pris en charge", "uploadFailed": "Échec du téléchargement", "urlRequired": "L'URL est requise", "referenceAddedSuccessfully": "Référence a<PERSON> avec succès", "failedToAddReference": "Échec de l'ajout de la référence", "downloadFailed": "Échec du téléchargement", "confirmDeleteDocument": "Êtes-vous sûr de vouloir supprimer ce document ?", "documentDeleted": "Document supprimé", "deleteFailed": "Échec de la suppression", "confirmDeleteReference": "Êtes-vous sûr de vouloir supprimer cette référence ?", "referenceDeleted": "Réfé<PERSON>ce supprimée"}, "action_plan": {"title": "Plan d'action", "action_plan": "Plan d'action", "create_action_plan": "Créer un plan d'action", "create_new_action_plan": "Créer un nouveau plan d'action", "fill_in_the_details_below_to_create_a_new_action_plan": "Remp<PERSON><PERSON>z les détails ci-dessous pour créer un nouveau plan d'action.", "name": "Nom", "enter_action_plan_name": "Entrez le nom du plan d'action", "nature": "Nature", "select_nature": "Sélectionnez la nature", "preventive": "Préventif", "corrective": "Correctif", "comment": "Commentaire", "enter_comment": "Entrez un commentaire", "cancel": "Annuler", "creating": "Création en cours...", "link_existing": "Lier un existant", "link_existing_action_plan": "Lier un plan d'action existant", "select_an_action_plan_to_link_to_this_incident": "Sélectionnez un plan d'action à lier à cet incident.", "search_action_plans": "Rechercher des plans d'action...", "linking_action_plan": "Liaison du plan d'action en cours...", "no_action_plans_match_your_search": "Aucun plan d'action ne correspond à votre recherche", "no_action_plans_available": "Aucun plan d'action disponible", "no_nature_specified": "Aucune nature spécifiée", "unlink": "Délier", "no_action_plan_linked": "Aucun plan d'action lié", "link_an_existing_action_plan_or_create_a_new_one_to_help_manage_this_incident": "Lie un plan d'action existant ou créez-en un nouveau pour aider à gérer cet incident.", "created_and_linked_successfully": "Plan d'action créé et lié avec succès", "linked_successfully": "Plan d'action lié avec succès", "unlinked_successfully": "Plan d'action délié avec succès", "failed_to_create_action_plan": "Échec de la création du plan d'action", "failed_to_link_action_plan": "Échec de la liaison du plan d'action", "failed_to_unlink_action_plan": "Échec de la déliaison du plan d'action", "are_you_sure_you_want_to_unlink_this_action_plan": "Êtes-vous sûr de vouloir délier ce plan d'action ?", "loading_action_plans": "Chargement des plans d'action..."}, "incident_activity_feed": {"created_this_incident": "a créé cet incident", "updated_the_incident": "a mis à jour l'incident", "changed_the_workflow_state": "a changé l'état du workflow", "performed_an_action": "a effectué une action", "activity_feed": "Fil d'activité", "loading_activities": "Chargement des activités...", "no_activities_recorded_yet": "Aucune activité enregistrée pour le moment.", "previous": "Précédent", "page": "Page", "of": "sur", "next": "Suivant", "date_changed_from_to": "{{field}} modifié de {{oldValue}} à {{newValue}}", "removed_contributor": "Utilisateur avec l'ID {{userId}} supprimé comme contributeur de l'incident {{incidentId}}", "added_contributor": "Utilisateur avec l'ID {{userId}} ajouté comme contributeur à l'incident {{incidentId}}", "updated_contributor_role": "R<PERSON>le mis à jour pour l'utilisateur avec l'ID {{userId}} vers {{role}} dans l'incident {{incidentId}}", "occurrence_date_changed": "Date d'occurrence modifiée de {{oldValue}} à {{newValue}}", "detection_date_changed": "Date de détection modifiée de {{oldValue}} à {{newValue}}", "declaration_date_changed": "Date de déclaration modifiée de {{oldValue}} à {{newValue}}", "name_changed": "Nom modifié de {{oldValue}} à {{newValue}}", "description_changed": "Description modifiée de {{oldValue}} à {{newValue}}", "impact_changed": "Impact modifié de {{oldValue}} à {{newValue}}", "priority_changed": "Priorité modifiée de {{oldValue}} à {{newValue}}", "nature_changed": "Nature modifiée de {{oldValue}} à {{newValue}}", "near_miss_changed": "Presque accident modifié de {{oldValue}} à {{newValue}}", "fields": {"occurrence_date": "Date d'occurrence", "detection_date": "Date de détection", "declaration_date": "Date de déclaration", "name": "Nom", "description": "Description", "impact": "Impact", "priority": "Priorité", "nature": "Nature", "near_miss": "Presque accident"}}, "characteristics": {"characteristics": "Caractéristiques", "basicInformation": "Informations de base", "name": "Nom", "incidentName": "Nom de l'incident", "declaredBy": "Déclaré par", "declarantName": "Nom du déclarant", "declarantEntity": "Entité du déclarant", "declarationDate": "Date de déclaration", "detectionDate": "Date de détection", "occurrenceDate": "Date d'occurrence", "contributors": "Contributeurs", "incidentEvaluation": "Évaluation de l'incident", "nearMiss": "Presque accident", "selectNearMiss": "Sélectionner un presque accident", "nature": "Nature", "selectNature": "Sélectionner la nature", "impact": "Impact", "selectImpact": "Sélectionner l'impact", "priority": "Priorité", "selectPriority": "Sélectionner la priorité", "referenceData": "<PERSON><PERSON><PERSON> de référence", "entity": "Entité", "selectEntity": "Sélectionner l'entité", "businessLine": "Ligne d'activité", "selectBusinessLine": "Sélectionner la ligne d'activité", "noBusinessLinesFound": "Aucune ligne d'activité trouvée.", "searchBusinessLines": "Rechercher des lignes d'activité...", "businessProcess": "<PERSON><PERSON>", "selectBusinessProcess": "Sélectionner le processus métier", "noBusinessProcessesFound": "Aucun processus métier trouvé.", "searchBusinessProcesses": "Rechercher des processus métier...", "organizationalProcess": "Processus organisationnel", "selectOrganizationalProcess": "Sélectionner le processus organisationnel", "noOrganizationalProcessesFound": "Aucun processus organisationnel trouvé.", "searchOrganizationalProcesses": "Rechercher des processus organisationnels...", "product": "Produit", "selectProduct": "Sélectionner le produit", "noProductsFound": "Aucun produit trouvé.", "searchProducts": "Rechercher des produits...", "application": "Application", "selectApplication": "Sélectionner l'application", "noApplicationsFound": "Aucune application trouvée.", "searchApplications": "Rechercher des applications...", "description": "Description", "incidentDescription": "Description de l'incident", "qualitativeAnalysis": "Analyse qualitative", "assignedRisk": "Risque <PERSON>", "noRiskAssigned": "Aucun risque assigné", "addNewRisk": "Ajouter un nouveau risque", "linkToExistingRisk": "Lier à un risque existant", "control": "Contr<PERSON>le", "noControlAssigned": "Aucun contr<PERSON>le <PERSON>", "addNewControl": "Ajouter un nouveau contrôle", "linkToExistingControl": "Lier à un contrôle existant", "incidentType": "Type d'incident", "noIncidentTypeAssigned": "Aucun type d'incident assigné", "addNewIncidentType": "Ajouter un nouveau type d'incident", "linkToExistingIncidentType": "Lier à un type d'incident existant", "riskNameRequired": "Le nom du risque est requis", "riskCreated": "Risque créé et assigné avec succès", "failedToCreateRisk": "Échec de la création du risque", "riskAssigned": "Risque assigné avec succès", "invalidRiskSelected": "Risque sélectionné invalide", "failedToAssignRisk": "Échec de l'assignation du risque", "loadingRisks": "Chargement des risques...", "unnamedRisk": "Risque sans nom", "riskID": "ID du risque : {riskID}", "controlNameRequired": "Le nom du contrôle est requis", "controlCreated": "Contrôle créé et assigné avec succès", "failedToCreateControl": "Échec de la création du contrôle", "controlAssigned": "Contrôle assigné avec succès", "invalidControlSelected": "Contrôle sélectionné invalide", "failedToAssignControl": "Échec de l'assignation du contrôle", "loadingControls": "Chargement des contrôles...", "unnamedControl": "Contrôle sans nom", "controlID": "ID du contrôle : {controlID}", "incidentTypeNameRequired": "Le nom du type d'incident est requis", "incidentTypeCreated": "Type d'incident créé et assigné avec succès", "failedToCreateIncidentType": "Échec de la création du type d'incident", "incidentTypeAssigned": "Type d'incident assigné avec succès", "invalidIncidentTypeSelected": "Type d'incident sélectionné invalide", "failedToAssignIncidentType": "Échec de l'assignation du type d'incident", "loadingIncidentTypes": "Chargement des types d'incident...", "unnamedIncidentType": "Type d'incident sans nom", "incidentTypeID": "ID du type d'incident : {incidentTypeID}"}, "financial": {"title": "<PERSON><PERSON><PERSON> finan<PERSON>", "summary": "Résumé financier", "currency": "<PERSON><PERSON>", "grossLoss": "Perte brute", "recoveries": "Récupérations", "provisions": "Provisions", "analysisTitle": "<PERSON><PERSON><PERSON> finan<PERSON>", "newEntry": "Nouvelle entrée", "addNewEntry": "Ajouter une nouvelle entrée", "name": "Nom", "amount": "<PERSON><PERSON>", "localAmount": "Montant local", "entryNamePlaceholder": "Entrez le nom de l'entrée", "amountPlaceholder": "<PERSON><PERSON><PERSON> le montant", "localAmountPlaceholder": "Entrez le montant local", "cancel": "Annuler", "add": "Ajouter", "noEntriesFound": "<PERSON><PERSON>ne entrée trouvée", "total": "Total", "actions": "Actions", "loading": "Chargement des données financières...", "error": "Erreur lors du chargement des données financières", "save_success": "Données financières enregistrées avec succès", "save_error": "Échec de l'enregistrement des données financières"}, "workflow": {"title": "Workflow", "loading_workflow_data": "Chargement des données du Workflow...", "error_loading_workflow": "Erreur lors du chargement des données du Workflow", "current_state": "État actuel", "next_actions": "Actions suivantes", "history": "Historique", "date": "Date", "user": "Utilisa<PERSON>ur", "action": "Action", "comment": "Commentaire", "no_history": "Aucun historique disponible", "no_actions": "Aucune action disponible", "confirm_action": "Confirmer l'action", "reject": "<PERSON><PERSON><PERSON>", "approve": "Approuver", "cancel": "Annuler", "submit": "So<PERSON><PERSON><PERSON>", "action_success": "Action terminée avec succès", "action_error": "Erreur lors de l'exécution de l'action", "comment_required": "Le commentaire est requis", "confirm_reject": "Êtes-vous sûr de vouloir rejeter cet incident ?", "confirm_approve": "Êtes-vous sûr de vouloir approuver cet incident ?", "incident_id_missing": "L'ID de l'incident est manquant", "incident_state": "État de l'incident", "failed_to_load_workflow_transitions": "Échec du chargement des transitions du workflow", "error_loading_workflow_transitions": "Erreur lors du chargement des transitions du workflow", "failed_to_load_workflow_data": "Échec du chargement des données du workflow", "unknown_error_occurred": "Une erreur inconnue est survenue", "workflow_transitioned_successfully": "Le workflow a été mis à jour avec succès !", "failed_to_transition_workflow": "Échec de la mise à jour du workflow", "permission_denied": "Permission refusée :", "role_configuration_issue": " Ceci peut être dû à un problème de configuration des rôles.", "updating": "Mise à jour...", "processing_transition": "Traitement de la transition...", "you_dont_have_permission_to_perform_this_action": "Vous n'avez pas la permission d'effectuer cette action", "incident_created": "Incident créé", "validated": "<PERSON><PERSON><PERSON> [Déclaration d'incident]", "rejected": "<PERSON><PERSON><PERSON>", "rejected_ready_to_restart": "<PERSON><PERSON><PERSON> (<PERSON>rêt à redémarrer)", "to_submit": "À soumettre", "pending_approval": "En attente d'approbation", "to_validate": "À valider", "error": "Erreur :", "retry": "<PERSON><PERSON><PERSON><PERSON>", "reject_incident": "<PERSON><PERSON><PERSON> l'incident", "please_provide_reason_for_rejection": "Veuillez fournir une raison pour le rejet :", "rejection_reason_required": "Raison du rejet (obligatoire)", "rejecting": "Rejet en cours...", "incident_creator": "Créateur de l'incident :", "no_activity_recorded_yet": "Aucune activité enregistrée pour le moment.", "performed_by": "Effectué par", "using_transition": "utilisant la transition", "previous": "Précédent", "page": "Page", "of": "sur", "next": "Suivant", "no_participants_recorded_yet": "Aucun participant enregis<PERSON><PERSON> pour le moment.", "name": "Nom", "role": "R<PERSON><PERSON>", "last_action": "Dernière action", "user_information_missing": "Informations utilisateur manquantes", "states": {"start": "<PERSON><PERSON><PERSON><PERSON>", "to_submit": "À soumettre", "to_approve": "À approuver", "to_validate": "À valider", "rejected": "<PERSON><PERSON><PERSON>", "validated": "<PERSON><PERSON><PERSON>", "closed": "Clôturé"}, "buttons": {"approve": "Approuver", "reject": "<PERSON><PERSON><PERSON>", "activity": "Activité", "participants": "Participants", "submit": "So<PERSON><PERSON><PERSON>", "send_for_approval": "Envoyer pour approbation", "send_for_validation": "Envoyer pour validation", "validate_incident": "Valider l'incident", "close_incident": "Clôturer l'incident"}}, "contributors": {"title": "Contributeurs", "loading": "Chargement des contributeurs...", "no_contributors": "Aucun contributeur assigné", "add_contributor": "Ajouter un contributeur", "assign_contributor": "Assigner un contributeur", "select_contributor": "Sélectionner un contributeur", "search_contributors": "Rechercher des contributeurs...", "no_contributors_found": "<PERSON><PERSON><PERSON> <PERSON>ur trouvé", "added_by": "Ajouté par", "added_on": "<PERSON><PERSON><PERSON> le", "role": "R<PERSON><PERSON>", "select_role": "Sélectionner un rôle", "remove_contributor": "<PERSON><PERSON><PERSON><PERSON> le contributeur", "confirm_remove": "Êtes-vous sûr de vouloir supprimer ce contributeur ?", "contributor_added": "Contributeur a<PERSON><PERSON> avec succès", "contributor_removed": "Contributeur supprim<PERSON> avec succès", "failed_to_add": "Échec de l'ajout du contributeur", "failed_to_remove": "Échec de la suppression du contributeur", "roles": {"owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "manager": "Gestionnaire", "analyst": "<PERSON><PERSON><PERSON>", "reviewer": "<PERSON><PERSON><PERSON><PERSON>", "observer": "Observateur"}, "assign_incident_contributor": "Assigner un contributeur à l'incident", "select_user": "Sélectionner un utilisateur", "search_users": "Rechercher des utilisateurs...", "no_users_found": "Aucun utilisateur trouvé", "cancel": "Annuler", "assign": "Assigner", "assigning": "Assignation en cours...", "modal": {"title": "Assigner un contributeur", "description": "Sélectionnez un utilisateur à assigner comme contributeur à cet incident", "user_required": "Veuillez sélectionner un utilisateur", "role_required": "Veuillez sélectionner un rôle"}}, "new_risk": {"title": "Ajouter un nouveau risque", "name": "Nom du risque", "impact": "Impact", "dmr": "DMR", "probability": "Probabilité", "comment": "Commentaire", "comment_placeholder": "Ajoutez des commentaires supplémentaires sur ce risque", "cancel": "Annuler", "add_risk": "A<PERSON><PERSON> le risque"}, "risk_selection": {"select_risk": "Sélectionner un risque", "select_a_risk_to_associate_with_this_incident": "Sélectionnez un risque à associer à cet incident", "search_risks": "Rechercher des risques...", "impact": "Impact", "dmr": "DMR", "probability": "Probabilité", "cancel": "Annuler", "no_risks_found_matching_your_search_criteria": "Aucun risque trouvé correspondant à vos critères de recherche"}, "new_control": {"title": "Ajouter un nouveau contrôle", "name": "Nom du contrôle", "code": "Code", "code_placeholder": "Code du contrôle (optionnel)", "comment": "Commentaire", "comment_placeholder": "Ajoutez des commentaires supplémentaires sur ce contrôle", "cancel": "Annuler", "add_control": "A<PERSON><PERSON> le contrôle"}, "control_selection": {"select_control": "Sélectionner un contrôle", "select_a_control_to_associate_with_this_incident": "Sélectionnez un contrôle à associer à cet incident", "search_controls": "Rechercher des contrôles...", "code": "Code", "cancel": "Annuler", "no_description": "Aucune description disponible", "no_controls_found_matching_your_search_criteria": "Aucun contrôle trouvé correspondant à vos critères de recherche"}, "new_incident_type": {"title": "Ajouter un nouveau type d'incident", "name": "Nom du type d'incident", "code": "Code", "code_placeholder": "Code du type d'incident (optionnel)", "comment": "Commentaire", "comment_placeholder": "Ajoutez des commentaires supplémentaires sur ce type d'incident", "cancel": "Annuler", "add_incident_type": "Ajouter le type d'incident"}, "incident_type_selection": {"select_incident_type": "Sélectionner un type d'incident", "select_an_incident_type_to_associate_with_this_incident": "Sélectionnez un type d'incident à associer à cet incident", "search_incident_types": "Rechercher des types d'incident...", "code": "Code", "cancel": "Annuler", "no_description": "Aucune description disponible", "no_incident_types_found_matching_your_search_criteria": "Aucun type d'incident trouvé correspondant à vos critères de recherche"}}}, "audit": {"sidebar": {"home": "Accueil", "dashboard": "Tableau de bord", "audit": "Audit", "plans_daudit": "Plans d'audit", "missions_audits": "Missions d'audit", "incidents": "Incidents", "incidents_list": "Liste des incidents", "create_incident": "<PERSON><PERSON><PERSON> un incident", "risks": "Risques", "risks_list": "Liste des risques", "controls": "<PERSON><PERSON><PERSON><PERSON>", "processes": "Processus", "tree_view": "Vue arborescente", "business_processes": "<PERSON><PERSON> m<PERSON>s", "organizational_processes": "Processus org.", "operations": "Opérations", "reports": "Rapports"}, "breadcrumb": {"audit": "Audit", "plans_daudit": "Plans d'audit", "mission_daudit": "Mission d'audit"}, "page_titles": {"edit_mission_audit": "Modifier la mission d'audit", "plans_daudit": "Plans d'audit", "create_plan_daudit": "Créer un plan d'audit", "edit_plan_daudit": "Modifier le plan d'audit"}, "buttons": {"back": "Retour", "save": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "Annuler", "add": "Ajouter", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "Mettre à jour"}, "tabs": {"mission_details": "Détails de la mission", "caracteristiques": "Caractéristiques", "perimetre": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "evaluation_risques": "Évaluation Risques", "activites": "Activités", "recommandations": "Recommandations", "documents": "Documents", "depenses": "<PERSON>é<PERSON>ses", "rapport": "Rapport", "workflow": "Workflow", "overview": "Vue d'ensemble", "analyse_financiere": "<PERSON><PERSON><PERSON> finan<PERSON>", "plan_daction": "Plan d'action", "fil_dactivite": "Fil d'activité"}, "fields": {"name": "Nom", "code": "Code", "status": "Statut", "department": "Département", "start_date": "Date de début", "end_date": "Date de fin", "duration": "<PERSON><PERSON><PERSON>", "description": "Description", "objectives": "Objectifs", "scope": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "resources": "Ressources", "progress": "Progression", "findings": "Constatations", "recommendations": "Recommandations"}, "status": {"in_progress": "En cours", "completed": "<PERSON><PERSON><PERSON><PERSON>", "planned": "Planifié", "delayed": "Retardé", "cancelled": "<PERSON><PERSON><PERSON>"}}}