import React, { useState, useEffect, useCallback } from 'react';
import { CheckCircle, XCircle, Loader, AlertTriangle, User, ChevronLeft, ChevronRight, Zap } from 'lucide-react';
import axios from 'axios';
import { useParams } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { getApiBaseUrl } from '../../utils/api-config';
import { toast } from 'sonner';
import { hasPermission, hasWorkflowPermission } from '../../store/auth-slice';
import { useTranslation } from "react-i18next";

// API Base URL - use dynamic URL from utility function
const API_BASE_URL = getApiBaseUrl();

// ADMIN ROLE NAMES DIRECTLY FROM LOGS
const ADMIN_ROLES = ['GRC Admin', 'GRC Manager', 'Risk Manager', 'Incident Manager', 'Admin', 'Super Admin'];
const ADMIN_ROLE_CODES = ['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager', 'admin', 'super_admin'];

// Define workflow steps for better readability
const WORKFLOW_STEPS = {
  START: "Start",
  TO_SUBMIT: "To Submit",
  TO_APPROVE: "To Approve",
  TO_VALIDATE: "To Validate",
  REJECTED: "Rejected",
  VALIDATED: "Validated",
  CLOSED: "Closed"
};

export function WorkflowSection() {
  const { t } = useTranslation();
  // Get the incident ID from URL params
  const { id: incidentId } = useParams();
  const { user: currentUser } = useSelector((state) => state.auth);

  // Get the full Redux state for permission checks
  const reduxState = useSelector(state => state);

  // State for workflow data
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingRoles, setIsLoadingRoles] = useState(false);
  const [error, setError] = useState(null);
  const [currentStep, setCurrentStep] = useState(null);
  const [backendState, setBackendState] = useState(null); // Track the actual backend state
  const [events, setEvents] = useState([]);
  const [participants, setParticipants] = useState([]);
  const [userRoles, setUserRoles] = useState({});
  const [availableTransitions, setAvailableTransitions] = useState({});
  const [incidentCreator, setIncidentCreator] = useState(null);

  // Add missing state variables for transition handling
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [transitionError, setTransitionError] = useState(null);

  // State for dialogs and modals
  const [showRejectDialog, setShowRejectDialog] = useState(false);
  const [rejectMessage, setRejectMessage] = useState('');

  // State for active tab
  const [activeTab, setActiveTab] = useState('Activity');

  // All possible steps for incident workflow
  const steps = [
    WORKFLOW_STEPS.START,
    WORKFLOW_STEPS.TO_SUBMIT,
    WORKFLOW_STEPS.TO_APPROVE,
    WORKFLOW_STEPS.TO_VALIDATE,
    WORKFLOW_STEPS.REJECTED,
    WORKFLOW_STEPS.VALIDATED,
    WORKFLOW_STEPS.CLOSED
  ];

  // Add pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(5);

  // Permission checks for contributors
  const canAssign = hasPermission(reduxState, 'update');

  // Compute permissions based on roles and current state - will update when dependencies change
  const canValidateOrReject = hasWorkflowPermission(reduxState, 'validate', currentStep) ||
    hasWorkflowPermission(reduxState, 'reject', currentStep, currentUser?.id === incidentCreator);

  // Check if user is a GRC Contributor with limited permissions
  const isGrcContributor = !hasPermission(reduxState, 'workflow_advance') &&
    hasPermission(reduxState, 'workflow_advance_limited');

  // Check if user has admin-level permissions for the workflow
  const hasAdminRoles = hasPermission(reduxState, 'workflow_advance') &&
    hasPermission(reduxState, 'workflow_validate') &&
    hasPermission(reduxState, 'workflow_reject');

  // Helper function to check permissions that can be used in handlers
  const checkWorkflowPermission = useCallback((action, isCreator = false) => {
    return hasWorkflowPermission(reduxState, action, currentStep, isCreator);
  }, [reduxState, currentStep]);

  // Log permissions for debugging
  useEffect(() => {
    if (currentUser?.roles) {
      console.log('Permissions:', {
        canValidateOrReject,
        isGrcContributor,
        hasAdminRoles,
        currentStep,
        userRoles: currentUser.roles.map(r => `${r.name}/${r.code}`)
      });
    }
  }, [currentUser, canValidateOrReject, isGrcContributor, hasAdminRoles, currentStep]);

  // Fetch incident workflow data on component mount
  useEffect(() => {
    if (incidentId) {
      fetchWorkflowData();
      fetchUserRoles();
    }
  }, [incidentId]);

  // Fetch workflow data (state and events)
  const fetchWorkflowData = async () => {
    if (!incidentId) {
      setError(t('admin.incidents.workflow.incident_id_missing', 'Incident ID is missing'));
      toast.error(t('admin.incidents.workflow.incident_id_missing', 'Incident ID is missing'));
      return;
    }

    setIsLoading(true);
    setError(null); // Clear any previous errors

    try {
      console.log('Fetching workflow data for incident ID:', incidentId);

      // Get current state and events
      const stateResponse = await axios.get(`${API_BASE_URL}/incidents/${incidentId}/workflow`, {
        withCredentials: true,
        timeout: 10000
      });

      console.log('State response:', stateResponse.data);

      if (stateResponse.data && stateResponse.data.success) {
        // Get the actual state from the backend
        let actualBackendState = stateResponse.data.data.current_state;
        console.log('Actual state received from backend:', actualBackendState);

        // Store the actual backend state
        setBackendState(actualBackendState);

        const timeline = stateResponse.data.data.timeline || [];

        // Sort timeline in reverse chronological order (newest first)
        const sortedTimeline = [...timeline].sort((a, b) =>
          new Date(b.timestamp) - new Date(a.timestamp)
        );

        setEvents(sortedTimeline);

        // Find incident creator from the event with Create transition
        const createEvent = timeline.find(e => e.transition === 'Create');
        if (createEvent) {
          setIncidentCreator(createEvent.user);
        }

        // Extract unique participants with their actions
        const participantsMap = new Map();

        timeline.forEach(event => {
          if (!participantsMap.has(event.user)) {
            participantsMap.set(event.user, {
              name: event.user,
              actions: [],
              lastAction: null
            });
          }

          const participant = participantsMap.get(event.user);

          // Add this action
          participant.actions.push({
            transition: event.transition,
            timestamp: event.timestamp,
            step: event.step
          });

          // Update last action if this is more recent
          if (!participant.lastAction || new Date(event.timestamp) > new Date(participant.lastAction.timestamp)) {
            participant.lastAction = {
              transition: event.transition,
              timestamp: event.timestamp,
              step: event.step
            };
          }
        });

        // Convert map to array and sort by most recent activity
        const participantsList = Array.from(participantsMap.values())
          .sort((a, b) => new Date(b.lastAction.timestamp) - new Date(a.lastAction.timestamp));

        setParticipants(participantsList);

        // *** Override: If backend state is 'Rejected', force UI to 'Start' ***
        if (actualBackendState === WORKFLOW_STEPS.REJECTED) {
          console.log('Backend state is Rejected, overriding UI state to Start');
          setCurrentStep(WORKFLOW_STEPS.START);

          // Set transitions for Start state to allow the user to create a new submission
          // This is hardcoded to match what the transitions endpoint would return for 'Start' state
          setAvailableTransitions({
            "Advance": "To Submit",
            "Reject": "Rejected"
          });
        } else {
          // Otherwise, use the state received from the backend
          setCurrentStep(actualBackendState);

        // Get available transitions
          try {
        const transitionsResponse = await axios.get(`${API_BASE_URL}/incidents/${incidentId}/workflow/transitions`, {
          withCredentials: true,
          timeout: 10000
        });

        console.log('Transitions response:', transitionsResponse.data);

        if (transitionsResponse.data && transitionsResponse.data.success) {
          // Use the correct field name from the API
          setAvailableTransitions(transitionsResponse.data.data.available_transitions || {});
        } else {
          console.error('Failed to get transitions:', transitionsResponse.data);
          setError(t('admin.incidents.workflow.failed_to_load_workflow_transitions', 'Failed to load workflow transitions'));
          toast.error(t('admin.incidents.workflow.failed_to_load_workflow_transitions', 'Failed to load workflow transitions'));
            }
          } catch (error) {
            console.error('Error fetching transitions:', error);
            setError(t('admin.incidents.workflow.error_loading_workflow_transitions', 'Error loading workflow transitions: ' + (error.message || 'Unknown error')));
            toast.error(t('admin.incidents.workflow.error_loading_workflow_transitions', 'Error loading workflow transitions: ' + (error.message || 'Unknown error')));
          }
        }
      } else {
        console.error('Failed to get state:', stateResponse.data);
        setError(t('admin.incidents.workflow.failed_to_load_workflow_data', 'Failed to load workflow data'));
        toast.error(t('admin.incidents.workflow.failed_to_load_workflow_data', 'Failed to load workflow data'));
      }
    } catch (error) {
      console.error('Error fetching workflow data:', error);
      setError(t('admin.incidents.workflow.unknown_error_occurred', 'Unknown error occurred'));
      toast.error(t('admin.incidents.workflow.unknown_error_occurred', 'Unknown error occurred'));
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch user roles from backend
  const fetchUserRoles = async () => {
    setIsLoadingRoles(true);
    try {
      const response = await axios.get(`${API_BASE_URL}/users`, {
        withCredentials: true,
        timeout: 10000
      });

      if (response.data && response.data.success) {
        // Create a map of username/email to role
        const rolesMap = {};
        response.data.data.forEach(user => {
          // Assuming user has username/email and roles properties
          const identifier = user.username || user.email;
          const role = user.roles && user.roles.length > 0
            ? user.roles[0].name
            : 'User';

          rolesMap[identifier] = role;
        });

        setUserRoles(rolesMap);
      }
    } catch (err) {
      console.error('Error fetching user roles:', err);
    } finally {
      setIsLoadingRoles(false);
    }
  };

  // Helper function to get role display
  const getUserRoleDisplay = (userName) => {
    if (isLoadingRoles) {
      return <span className="flex items-center"><Loader className="h-3 w-3 animate-spin mr-1" /> Loading...</span>;
    }
    return userRoles[userName] || 'Unknown Role';
  };

  // Handle state transition
  const handleTransition = async (action, message) => {
    if (!currentUser || !currentUser.id) {
      toast.error(t('admin.incidents.workflow.user_information_missing', 'User information is missing'));
        return;
      }

    if (isTransitioning) {
      return; // Prevent multiple simultaneous transitions
    }

    try {
      setIsTransitioning(true);
      setTransitionError(null); // Clear any previous errors

      // Log detailed role information to help debug
      console.log('User attempting transition:', {
        id: currentUser.id,
        username: currentUser.username,
        roles: currentUser.roles?.map(r => ({ id: r.id, name: r.name, code: r.code }))
      });

      const endpoint = `${API_BASE_URL}/incidents/${incidentId}/workflow/transition`;

      // Include optional message in the request body
      // Note: userId is not needed as it's already available in the req.user object from the auth token
      const response = await axios.post(endpoint, {
        transition: action,
        message: message // This will be undefined if no message was provided
      }, {
        withCredentials: true // Include credentials for authentication
      });

      if (response.data.success) {
        console.log('Transition successful:', response.data);
        toast.success(t('admin.incidents.workflow.workflow_transitioned_successfully', 'Workflow transitioned successfully!'));

        if (action === 'Reject') {
          console.log('Rejection successful, resetting UI step to Start');
          // Set backend state to Rejected
          setBackendState(WORKFLOW_STEPS.REJECTED);
          // But display START in the UI
          setCurrentStep(WORKFLOW_STEPS.START);
          // Set the available transitions for Start state
          setAvailableTransitions({
            "Advance": "To Submit",
            "Reject": "Rejected"
          });
          fetchWorkflowData();
        } else {
          // For other transitions, use the state from the response
          const newState = response.data.data.current_state;
          setBackendState(newState);
          setCurrentStep(newState);
          if (response.data.data.timeline) {
            setEvents(response.data.data.timeline);
          }
          fetchWorkflowData();
        }
      } else {
        console.error('Transition failed:', response.data);
        setTransitionError(response.data.message);
        toast.error(response.data.message || t('admin.incidents.workflow.failed_to_transition_workflow', 'Failed to transition workflow'));
      }
    } catch (error) {
      console.error('Error in handleTransition:', error);
      console.error('Error response:', error.response?.data);

      const errorMessage = error.response?.data?.message ||
                            error.message ||
                            t('admin.incidents.workflow.failed_to_transition_workflow', 'Failed to transition workflow');

      // Add more specific error information
      let userFriendlyMessage = errorMessage;
      if (error.response?.status === 403) {
        userFriendlyMessage = t('admin.incidents.workflow.permission_denied', 'Permission denied: ') + errorMessage + t('admin.incidents.workflow.role_configuration_issue', '. This may be due to a role configuration issue.');

        // Add role information for debugging
        console.log('User roles during permission denial:',
          currentUser?.roles?.map(r => ({ name: r.name, code: r.code })) || 'No roles found'
        );
      }

      setTransitionError(userFriendlyMessage);
      toast.error(userFriendlyMessage);

      throw error; // Re-throw to allow callers (like handleReject) to catch it
    } finally {
      setIsTransitioning(false);
    }
  };

  // Helper function to get the current step index
  const getCurrentIndex = () => {
    return steps.indexOf(currentStep);
  };

  // Helper functions for pagination
  const indexOfLastEvent = currentPage * itemsPerPage;
  const indexOfFirstEvent = indexOfLastEvent - itemsPerPage;
  const currentEvents = events.slice(indexOfFirstEvent, indexOfLastEvent);
  const totalPages = Math.ceil(events.length / itemsPerPage);

  const nextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const prevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  // Function to format date nicely in dd/mm/yyyy format
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Helper function to handle rejection with reason
  const handleReject = async () => {
    // Close the dialog immediately
    setShowRejectDialog(false);

    try {
      // Attempt the transition
      await handleTransition('Reject', rejectMessage);

      // --- If handleTransition didn't throw an error, it means success ---
      console.log('handleReject: Transition successful, resetting step.');

      // Reset the message
      setRejectMessage('');

      // Explicitly set the backend state to Rejected
      setBackendState(WORKFLOW_STEPS.REJECTED);

      // Explicitly set the step to Start in the UI
      setCurrentStep(WORKFLOW_STEPS.START);

      // Explicitly set the available transitions for Start state
      setAvailableTransitions({
        "Advance": "To Submit",
        "Reject": "Rejected"
      });

      // Fetch fresh data to get correct timeline for most recent activity
      fetchWorkflowData();

    } catch (err) { // Use 'err' to avoid linter issues if catch block becomes non-empty
      // Error is assumed to be handled (toast shown) inside handleTransition
      console.log('Rejection failed (error caught in handleReject)', err);
      // Optionally add a specific toast here if handleTransition doesn't always show one on error
      // toast.error("Rejection failed. Please try again.");
    }
  };



  if (isLoading && !currentStep) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader className="h-8 w-8 animate-spin" />
        <span className="ml-2">{t('admin.incidents.workflow.loading_workflow_data', 'Loading workflow data...')}</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 text-red-500">
        <p>{t('admin.incidents.workflow.error', 'Error:')} {error}</p>
        <button
          className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          onClick={fetchWorkflowData}
        >
          {t('admin.incidents.workflow.retry', 'Retry')}
        </button>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Enhanced Status Indicator - Positioned at top left */}
      <div className="mb-6 flex items-start">
        {currentStep === WORKFLOW_STEPS.VALIDATED ? (
          <div className="flex items-center bg-green-100 px-4 py-2 rounded-lg">
            <CheckCircle className="h-6 w-6 text-green-500 mr-2" />
            <span className="text-lg font-medium text-green-700">{t('admin.incidents.workflow.validated', 'Validated [Incident Declaration]')}</span>
          </div>
        ) : currentStep === WORKFLOW_STEPS.REJECTED ? (
          <div className="flex items-center bg-red-100 px-4 py-2 rounded-lg">
            <XCircle className="h-6 w-6 text-red-500 mr-2" />
            <span className="text-lg font-medium text-red-700">{t('admin.incidents.workflow.rejected', 'Rejected')}</span>
          </div>
        ) : backendState === WORKFLOW_STEPS.REJECTED && currentStep === WORKFLOW_STEPS.START ? (
          <div className="flex items-center bg-orange-100 px-4 py-2 rounded-lg">
            <Zap className="h-6 w-6 text-orange-500 mr-2" />
            <span className="text-lg font-medium text-orange-700">{t('admin.incidents.workflow.rejected_ready_to_restart', 'Rejected (Ready to Restart)')}</span>
          </div>
        ) : currentStep === WORKFLOW_STEPS.START ? (
          <div className="flex items-center bg-blue-100 px-4 py-2 rounded-lg">
            <Zap className="h-6 w-6 text-blue-500 mr-2" />
            <span className="text-lg font-medium text-blue-700">{t('admin.incidents.workflow.incident_created', 'Incident Created')}</span>
          </div>
        ) : currentStep === WORKFLOW_STEPS.TO_SUBMIT ? (
          <div className="flex items-center bg-yellow-100 px-4 py-2 rounded-lg">
            <Zap className="h-6 w-6 text-yellow-500 mr-2" />
            <span className="text-lg font-medium text-yellow-700">{t('admin.incidents.workflow.to_submit', 'To Submit')}</span>
          </div>
        ) : currentStep === WORKFLOW_STEPS.TO_APPROVE ? (
          <div className="flex items-center bg-purple-100 px-4 py-2 rounded-lg">
            <Zap className="h-6 w-6 text-purple-500 mr-2" />
            <span className="text-lg font-medium text-purple-700">{t('admin.incidents.workflow.pending_approval', 'Pending Approval')}</span>
          </div>
        ) : currentStep === WORKFLOW_STEPS.TO_VALIDATE ? (
          <div className="flex items-center bg-orange-100 px-4 py-2 rounded-lg">
            <Zap className="h-6 w-6 text-orange-500 mr-2" />
            <span className="text-lg font-medium text-orange-700">{t('admin.incidents.workflow.to_validate', 'To Validate')}</span>
          </div>
        ) : (
          <div className="flex items-center bg-gray-100 px-4 py-2 rounded-lg">
            <Zap className="h-6 w-6 text-gray-500 mr-2" />
            <span className="text-lg font-medium text-gray-700">{currentStep}</span>
          </div>
        )}
        {isLoading &&
          <div className="flex items-center bg-blue-50 px-3 py-1 rounded ml-4">
            <Loader className="h-4 w-4 mr-2 animate-spin text-blue-500" />
            <span className="text-blue-500 text-sm">{t('admin.incidents.workflow.updating', 'Updating...')}</span>
          </div>
        }
        {isTransitioning &&
          <div className="flex items-center bg-yellow-50 px-3 py-1 rounded ml-4">
            <Loader className="h-4 w-4 mr-2 animate-spin text-yellow-500" />
            <span className="text-yellow-500 text-sm">{t('admin.incidents.workflow.processing_transition', 'Processing transition...')}</span>
          </div>
        }
      </div>

      {/* Display transition error if any */}
      {transitionError && (
        <div className="mb-6 p-3 bg-red-50 border border-red-200 rounded-md text-red-600">
          <AlertTriangle className="h-5 w-5 inline-block mr-2" />
          <span>{transitionError}</span>
        </div>
      )}

      {/* Horizontal Step Progress Bar */}
      <div className="flex items-center justify-between mb-6">
        {steps.map((step, index) => (
          <React.Fragment key={step}>
            <div className="flex flex-col items-center">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                // Validated step is green when reached
                step === WORKFLOW_STEPS.VALIDATED && currentStep === WORKFLOW_STEPS.VALIDATED
                  ? 'bg-green-500' :
                // Rejected step is red when reached
                step === WORKFLOW_STEPS.REJECTED && currentStep === WORKFLOW_STEPS.REJECTED
                  ? 'bg-red-500' :
                // Current step is blue
                index === getCurrentIndex()
                  ? 'bg-blue-500' :
                // Completed steps are blue
                index < getCurrentIndex()
                  ? 'bg-blue-500' :
                // Future steps are gray
                'bg-gray-300'
              }`}>
                <span className="text-white">{index + 1}</span>
              </div>
              <p className="mt-2 text-sm text-center">{t(`admin.incidents.workflow.states.${step.toLowerCase().replace(/ /g, '_')}`, step)}</p>
            </div>
            {index < steps.length - 1 && <div className="flex-1 h-1 bg-gray-300 mx-2"></div>}
          </React.Fragment>
        ))}
      </div>

      {/* Action Buttons - Based on available transitions */}
      <div className="flex justify-start space-x-4 mb-6">
        {Object.entries(availableTransitions).map(([action]) => {
          console.log(`Available transition: ${action}, Current Step: ${currentStep}`);

          // Skip the Reset action as we handle it via the Reject button
          if (action === 'Reset') return null;

          // Check permission for this specific action using the helper
          const isCreator = currentUser?.id === incidentCreator;
          let canPerformAction = false;

          if (action === 'Advance') {
            // Direct check for admin roles - always allow these roles to advance
            const hasAdminRole = currentUser?.roles?.some(r => {
              const roleName = (r.name || '').toLowerCase();
              const roleCode = (r.code || '').toLowerCase();

              return (
                roleCode === 'grc_manager' ||
                roleCode === 'grc_admin' ||
                roleCode === 'risk_manager' ||
                roleCode === 'admin' ||
                roleCode === 'super_admin' ||
                roleName.includes('manager') ||
                roleName.includes('admin')
              );
            });

            // Log role check for debugging
            console.log('User roles check for action:', {
              action,
              roles: currentUser?.roles?.map(r => ({name: r.name, code: r.code})),
              hasAdminRole,
              currentStep
            });

            if (hasAdminRole) {
              // Always allow admin roles to advance at any step
              canPerformAction = true;
            } else {
              // For non-admin roles, use the regular permission check
              canPerformAction = checkWorkflowPermission('advance', isCreator);
              // Special check for contributors who can't advance beyond "To Approve"
              if (isGrcContributor && currentStep === WORKFLOW_STEPS.TO_APPROVE) {
                canPerformAction = false;
              }
            }
          } else if (action === 'Validate' || action === 'Approve') {
            // Direct check for certain roles that can always validate/approve
            const canValidateRole = currentUser?.roles?.some(r => {
              const roleName = (r.name || '').toLowerCase();
              const roleCode = (r.code || '').toLowerCase();

              return (
                roleCode === 'grc_manager' ||
                roleCode === 'grc_admin' ||
                roleCode === 'risk_manager' ||
                roleCode === 'admin' ||
                roleCode === 'super_admin' ||
                roleName.includes('manager') ||
                roleName.includes('admin')
              );
            });

            canPerformAction = canValidateRole || checkWorkflowPermission('validate', isCreator);
          } else if (action === 'Reject') {
            canPerformAction = checkWorkflowPermission('reject', isCreator);
          } else if (action === 'Close') {
            canPerformAction = checkWorkflowPermission('close', isCreator);
          }

          // Determine if this button should be disabled
          const isDisabled = isLoading || !canPerformAction;

          // Get appropriate button text
          let buttonText = action;
          if (action === "Advance") {
            if (currentStep === WORKFLOW_STEPS.START) buttonText = "Submit";
            else if (currentStep === WORKFLOW_STEPS.TO_SUBMIT) buttonText = "Send for Approval";
            else if (currentStep === WORKFLOW_STEPS.TO_APPROVE) buttonText = "Send for Validation";
          } else if (action === "Approve") buttonText = "Approve";
          else if (action === "Validate") buttonText = "Validate Incident";
          else if (action === "Close") buttonText = "Close Incident";
          else if (action === "Reject") buttonText = "Reject";

          // Choose button style based on action type
          const buttonClass = action === "Reject"
            ? "bg-red-500 text-white hover:bg-red-600"
            : "border border-blue-500 text-blue-500 bg-white hover:bg-blue-50";

          return (
            <button
              key={action}
              className={`px-4 py-2 rounded ${buttonClass} ${isDisabled ? 'opacity-50 cursor-not-allowed' : ''}`}
              onClick={() => {
                if (action === "Reject") {
                  setShowRejectDialog(true);
                } else {
                  handleTransition(action);
                }
              }}
              disabled={isDisabled}
              title={isDisabled ? t('admin.incidents.workflow.you_dont_have_permission_to_perform_this_action', 'You don\'t have permission to perform this action') : ""}
            >
              {buttonText}
            </button>
          );
        })}

        {/* Special case for Reject in Validated state if they have permission */}
        {currentStep === WORKFLOW_STEPS.VALIDATED &&
         checkWorkflowPermission('reject', currentUser?.id === incidentCreator) && (
          <button
            className="px-4 py-2 rounded bg-red-500 text-white hover:bg-red-600"
            onClick={() => setShowRejectDialog(true)}
            disabled={isLoading}
          >
            Reject
          </button>
        )}
      </div>

      {/* Tab Buttons */}
      <div className="flex space-x-4 mb-6">
        <button
          className={`px-4 py-2 rounded ${
            activeTab === 'Activity' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700'
          }`}
          onClick={() => setActiveTab('Activity')}
        >
          {t('admin.incidents.workflow.buttons.activity', 'Activity')}
        </button>
        <button
          className={`px-4 py-2 rounded ${
            activeTab === 'Participants' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700'
          }`}
          onClick={() => setActiveTab('Participants')}
        >
          {t('admin.incidents.workflow.buttons.participants', 'Participants')}
        </button>
      </div>

      {/* Activity Timeline */}
      {activeTab === 'Activity' && (
        <div className="mt-4 space-y-6">
          {incidentCreator && (
            <div className="mb-4 p-3 bg-blue-50 rounded-md border border-blue-100">
              <p className="font-medium">{t('admin.incidents.workflow.incident_creator', 'Incident Creator:')} <span className="font-normal">{incidentCreator}</span></p>
            </div>
          )}

          {events.length === 0 ? (
            <div className="text-gray-500">{t('admin.incidents.workflow.no_activity_recorded_yet', 'No activity recorded yet.')}</div>
          ) : (
            <>
              {currentEvents.map((event, index) => (
                <div key={index} className="flex">
                  <div className="flex flex-col items-center mr-4">
                    <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                    {index < currentEvents.length - 1 && <div className="w-0.5 h-full bg-gray-300"></div>}
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">
                      {formatDate(event.timestamp)}
                    </p>
                    <p className="font-medium">{event.step} step reached</p>
                    <p className="text-sm">{t('admin.incidents.workflow.performed_by', 'Performed by')} {event.user}</p>
                    {event.transition && (
                      <p className="text-sm text-gray-500">{t('admin.incidents.workflow.using_transition', 'using transition')} '{event.transition}'</p>
                    )}
                    {event.message && (
                      <p className="text-sm mt-1 italic bg-gray-50 p-2 rounded">"{event.message}"</p>
                    )}
                  </div>
                </div>
              ))}

              {/* Pagination Controls */}
              {totalPages > 1 && (
                <div className="flex justify-between items-center mt-4 pt-4 border-t border-gray-200">
                  <button
                    onClick={prevPage}
                    disabled={currentPage === 1}
                    className={`flex items-center ${currentPage === 1 ? 'text-gray-300 cursor-not-allowed' : 'text-blue-600 hover:text-blue-800'}`}
                  >
                    <ChevronLeft className="h-5 w-5" />
                    <span>{t('admin.incidents.workflow.previous', 'Previous')}</span>
                  </button>

                  <span className="text-sm text-gray-600">
                    {t('admin.incidents.workflow.page', 'Page')} {currentPage} {t('admin.incidents.workflow.of', 'of')} {totalPages}
                  </span>

                  <button
                    onClick={nextPage}
                    disabled={currentPage === totalPages}
                    className={`flex items-center ${currentPage === totalPages ? 'text-gray-300 cursor-not-allowed' : 'text-blue-600 hover:text-blue-800'}`}
                  >
                    <span>{t('admin.incidents.workflow.next', 'Next')}</span>
                    <ChevronRight className="h-5 w-5" />
                  </button>
                </div>
              )}
            </>
          )}
        </div>
      )}

      {/* Participants tab content */}
      {activeTab === 'Participants' && (
        <div className="mt-4">
          {participants.length === 0 ? (
            <div className="text-gray-500">{t('admin.incidents.workflow.no_participants_recorded_yet', 'No participants recorded yet.')}</div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('admin.incidents.workflow.name', 'Name')}
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('admin.incidents.workflow.role', 'Role')}
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('admin.incidents.workflow.last_action', 'Last Action')}
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('admin.incidents.workflow.date', 'Date')}
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {participants.map((participant, index) => (
                    <tr key={index}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        <div className="flex items-center">
                          <User className="h-4 w-4 text-gray-400 mr-2" />
                          {participant.name}
                          {participant.name === incidentCreator && (
                            <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                              Creator
                            </span>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {getUserRoleDisplay(participant.name)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {participant.lastAction?.transition} ({participant.lastAction?.step})
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {participant.lastAction?.timestamp ? formatDate(participant.lastAction.timestamp) : ''}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      )}



      {/* Reject Confirmation Dialog */}
      {showRejectDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">{t('admin.incidents.workflow.reject_incident', 'Reject Incident')}</h3>

            <p className="mb-4">{t('admin.incidents.workflow.please_provide_reason_for_rejection', 'Please provide a reason for rejection:')}</p>

            <textarea
              className="w-full p-2 border border-gray-300 rounded mb-4"
              rows="4"
              value={rejectMessage}
              onChange={(e) => setRejectMessage(e.target.value)}
              placeholder={t('admin.incidents.workflow.rejection_reason_required', 'Rejection reason (required)')}
              disabled={isLoading}
            />

            <div className="flex justify-end space-x-2">
              <button
                className="px-4 py-2 border border-gray-300 rounded hover:bg-gray-100 disabled:bg-gray-200"
                onClick={() => {
                  setShowRejectDialog(false);
                  setRejectMessage('');
                }}
                disabled={isLoading}
              >
                {t('admin.incidents.workflow.cancel', 'Cancel')}
              </button>

              <button
                className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 disabled:bg-red-300 flex items-center"
                onClick={() => {
                  if (rejectMessage.trim()) {
                    handleReject();
                  } else {
                    toast.error(t('admin.incidents.workflow.please_provide_reason_for_rejection', 'Please provide a reason for rejection'));
                  }
                }}
                disabled={!rejectMessage.trim() || isLoading}
              >
                {isLoading && <Loader className="h-4 w-4 mr-2 animate-spin" />}
                {isLoading ? t('admin.incidents.workflow.rejecting', 'Rejecting...') : t('admin.incidents.workflow.reject', 'Reject')}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
