const { Risk, User, RiskContributor, sequelize } = require('../../models');
const activityController = require('./activity-controller');
const socketUtils = require('../../utils/socket-io'); // Import Socket.IO utility
const notificationController = require('../notifications/notification-controller'); // Import notification controller

/**
 * Get all contributors for a specific risk
 * 
 * @param {Object} req - Express request object with riskId parameter
 * @param {Object} res - Express response object
 */
exports.getRiskContributors = async (req, res) => {
  try {
    const { riskId } = req.params;
    
    // Validate risk exists
    const risk = await Risk.findByPk(riskId);
    if (!risk) {
      return res.status(404).json({
        success: false,
        message: 'Risk not found'
      });
    }
    
    // Get contributors with user details
    const contributors = await RiskContributor.findAll({
      where: { risk_id: riskId },
      include: [
        {
          model: User,
          as: 'contributor',
          attributes: ['id', 'username', 'email']
        },
        {
          model: User,
          as: 'assigner',
          attributes: ['id', 'username', 'email']
        }
      ]
    });
    
    // Log for debugging
    console.log('Risk contributors with assigners:', contributors.map(c => ({
      id: c.id,
      assigned_by: c.assigned_by,
      assigner: c.assigner ? {
        id: c.assigner.id,
        username: c.assigner.username
      } : null
    })));
    
    return res.status(200).json({
      success: true,
      data: contributors
    });
  } catch (error) {
    console.error('Error in getRiskContributors:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve risk contributors',
      error: error.message
    });
  }
};

/**
 * Assign a contributor to a risk
 * 
 * @param {Object} req - Express request object with riskId parameter and userId in body
 * @param {Object} res - Express response object
 */
exports.assignContributor = async (req, res) => {
  const transaction = await sequelize.transaction();
  try {
    const { riskId } = req.params;
    const { userId } = req.body;
    const assignerId = req.user.userId || req.user.id;

    console.log('[Risk Contributor] Assigning user:', userId, 'to risk:', riskId, 'by assigner:', assignerId);

    if (!riskId || !userId) {
      await transaction.rollback();
      return res.status(400).json({ success: false, message: 'Risk ID and User ID are required' });
    }

    const risk = await Risk.findByPk(riskId, { transaction });
    if (!risk) {
      await transaction.rollback();
      return res.status(404).json({ success: false, message: 'Risk not found' });
    }

    const user = await User.findByPk(userId, { transaction });
    if (!user) {
      await transaction.rollback();
      return res.status(404).json({ success: false, message: 'User to be assigned not found' });
    }

    const assignerUser = await User.findByPk(assignerId, { transaction });
    if (!assignerUser) {
      await transaction.rollback();
      return res.status(404).json({ success: false, message: 'Assigner user not found' });
    }

    const existingContributor = await RiskContributor.findOne({
      where: { risk_id: riskId, user_id: userId },
      transaction
    });

    if (existingContributor) {
      await transaction.rollback();
      return res.status(400).json({ success: false, message: 'User is already a contributor to this risk' });
    }

    const contributor = await RiskContributor.create({
      risk_id: riskId,
      user_id: userId,
      assigned_by: assignerId,
      status: 'pending', // Assuming new assignments are 'pending'
      // assigned_date: new Date() // Sequelize automatically adds createdAt
    }, { transaction });

    console.log('[Risk Contributor] Contributor record created:', contributor.id);

    await activityController.logActivity({
      userId: assignerId,
      action: 'ASSIGN_CONTRIBUTOR',
      entityType: 'Risk',
      entityId: riskId,
      details: `Assigned user ${user.username || user.email} (ID: ${userId}) as contributor to risk ${risk.name || risk.title} (ID: ${riskId})`
    }, { transaction });

    console.log('[Risk Contributor] Activity logged for risk assignment');

    // Create notification for the assigned user (NOW INSIDE TRANSACTION)
    const notificationData = {
      userId: userId, // The user being assigned
      type: 'risk_assignment',
      entityId: riskId,
      entityName: risk.name || risk.title || 'Untitled Risk',
      message: `You have been assigned to risk: ${risk.name || risk.title || 'Untitled Risk'}`,
      assignedBy: assignerId 
    };

    console.log('[Risk Contributor] Attempting to create notification (within transaction):', JSON.stringify(notificationData));
    const newNotification = await notificationController.createNotification(notificationData, { transaction });

    if (!newNotification) {
      // If notification creation fails, rollback the entire transaction
      console.error('[Risk Contributor] Failed to create notification. Rolling back transaction.');
      await transaction.rollback();
      return res.status(500).json({ success: false, message: 'Failed to create notification for contributor assignment.' });
    }
    console.log('[Risk Contributor] Notification created successfully (within transaction), ID:', newNotification.id);

    // All database operations successful, commit the transaction
    await transaction.commit();
    console.log('[Risk Contributor] Transaction committed successfully.');

    // Emit Socket.IO notification (AFTER successful commit)
    try {
      const io = socketUtils.getIo();
      if (io) {
        const targetRoom = `user-${userId.toString()}`;
        const socketData = {
          ...notificationData, // Use the original notificationData
          id: newNotification.id, // Add the actual database ID of the notification
          notificationId: newNotification.id.toString(), // For frontend compatibility if needed
          createdAt: newNotification.createdAt, // Add creation timestamp
          is_read: false // Notifications are initially unread
        };
        io.to(targetRoom).emit('notification', socketData);
        console.log(`[Risk Contributor] Socket notification sent to room ${targetRoom}:`, socketData);
      } else {
        console.warn('[Risk Contributor] Socket.IO instance not available. Notification not sent via socket.');
      }
    } catch (socketError) {
      // Log socket error but don't fail the request as DB operations were successful
      console.error('[Risk Contributor] Error emitting socket notification:', socketError);
    }
    
    return res.status(201).json({
      success: true,
      message: 'Contributor assigned and notification sent successfully',
      data: contributor
    });

  } catch (error) {
    // Ensure rollback if any error occurs before commit
    if (transaction && !transaction.finished) { // Check if transaction is still active
        try {
            await transaction.rollback();
            console.log('[Risk Contributor] Transaction rolled back due to error:', error.message);
        } catch (rollbackError) {
            console.error('[Risk Contributor] Error rolling back transaction:', rollbackError);
        }
    }
    console.error('[Risk Contributor] Error in assignContributor:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to assign contributor',
      error: error.message
    });
  }
};

/**
 * Remove a contributor from a risk
 * 
 * @param {Object} req - Express request object with riskId and contributorId parameters
 * @param {Object} res - Express response object
 */
exports.removeContributor = async (req, res) => {
  const transaction = await sequelize.transaction();
  
  try {
    const { riskId, contributorId } = req.params;
    const userId = req.user.userId || req.user.id;
    
    // Validate risk exists
    const risk = await Risk.findByPk(riskId, { transaction });
    if (!risk) {
      await transaction.rollback();
      return res.status(404).json({
        success: false,
        message: 'Risk not found'
      });
    }
    
    // Validate user exists
    const user = await User.findByPk(contributorId, { transaction });
    if (!user) {
      await transaction.rollback();
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }
    
    // Check if user is a contributor
    const contributor = await RiskContributor.findOne({
      where: {
        risk_id: riskId,
        user_id: contributorId
      },
      transaction
    });
    
    if (!contributor) {
      await transaction.rollback();
      return res.status(404).json({
        success: false,
        message: 'User is not a contributor to this risk'
      });
    }
    
    // Delete contributor assignment
    await contributor.destroy({ transaction });
    
    // Log activity using the risk activity controller instead of incident activity controller
    await activityController.logActivity({
      userId,
      action: 'REMOVE_CONTRIBUTOR',
      entityType: 'Risk',
      entityId: riskId,
      details: `Removed user ${user.username || user.email} (ID: ${contributorId}) as contributor from risk ${risk.name} (ID: ${riskId})`
    }, { transaction });
    
    await transaction.commit();
    
    return res.status(200).json({
      success: true,
      message: 'Contributor removed successfully'
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error in removeContributor:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to remove contributor',
      error: error.message
    });
  }
};

/**
 * Check if a user is a contributor to a risk
 * 
 * @param {Object} req - Express request object with riskId parameter and userId in query
 * @param {Object} res - Express response object
 */
exports.checkContributor = async (req, res) => {
  try {
    const { riskId } = req.params;
    const { userId } = req.query;
    
    // If no userId provided, use the authenticated user's ID
    const userIdToCheck = userId || req.user.userId || req.user.id;
    
    // Check if user is a contributor
    const contributor = await RiskContributor.findOne({
      where: {
        risk_id: riskId,
        user_id: userIdToCheck
      }
    });
    
    return res.status(200).json({
      success: true,
      isContributor: !!contributor
    });
  } catch (error) {
    console.error('Error in checkContributor:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to check contributor status',
      error: error.message
    });
  }
}; 