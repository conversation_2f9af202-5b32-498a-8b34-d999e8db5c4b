const { sequelize } = require('../../../models/index');
const AuditMissionRapport = require('../../../models/Audit/rapport/auditmissionrapport')(sequelize);
const { Document, Packer, Paragraph, TextRun, HeadingLevel, Table, TableRow, TableCell, WidthType, BorderStyle } = require('docx');
const fs = require('fs').promises;
const path = require('path');
const os = require('os');
const { exec } = require('child_process');
const util = require('util');

const execAsync = util.promisify(exec);

// Escape LaTeX special characters
const escapeLatex = (str) => {
  if (typeof str !== 'string') return str || 'N/A';
  return str
    .replace(/&/g, '\\&')
    .replace(/%/g, '\\%')
    .replace(/\$/g, '\\$')
    .replace(/#/g, '\\#')
    .replace(/_/g, '\\_')
    .replace(/{/g, '\\{')
    .replace(/}/g, '\\}')
    .replace(/~/g, '\\textasciitilde{}')
    .replace(/\^/g, '\\textasciicircum{}')
    .replace(/\\/g, '\\textbackslash{}')
    .replace(/@/g, '\\@')
    .replace(/\.\.\./g, '\\ldots');
};

exports.getMissionReport = async (req, res) => {
  const { missionId } = req.params;
  const { format } = req.query;

  try {
    const reportData = await AuditMissionRapport.getMissionReport(missionId);
    console.log('[AuditMissionRapport] Report data:', JSON.stringify(reportData, null, 2));

    if (!reportData || reportData.length === 0) {
      return res.status(404).json({ message: 'No data found for the specified mission ID' });
    }

    const autresParticipants = [
      ...new Set(
        reportData
          .flatMap(row => [row.activity_responsable, row.constat_responsable])
          .filter(id => id !== null && id !== undefined)
      ),
    ].join(', ');

    const auditedElements = [
      reportData[0].risk_names,
      reportData[0].entity_names,
      reportData[0].control_names,
      reportData[0].incident_names,
      reportData[0].organizational_process_names,
    ]
      .filter(name => name)
      .join(', ');

    if (format === 'pdf') {
      // Use a temporary directory without spaces
      const tempDir = 'C:\\Temp';
      // Ensure temp directory exists
      try {
        await fs.mkdir(tempDir, { recursive: true });
      } catch (mkdirError) {
        console.error('[AuditMissionRapport] Failed to create temp directory:', mkdirError);
        return res.status(500).json({ message: 'Failed to create temp directory', error: mkdirError.message });
      }
      // Sanitize missionId for filename
      const safeMissionId = missionId.replace(/[^a-zA-Z0-9]/g, '_');
      const latexFilePath = path.resolve(tempDir, `audit_report_${safeMissionId}.tex`);
      const pdfFilePath = path.resolve(tempDir, `audit_report_${safeMissionId}.pdf`);

      const latexContent = `
\\documentclass[a4paper,12pt]{article}
\\usepackage[utf8]{inputenc}
\\usepackage[T1]{fontenc}
\\usepackage{lmodern}
\\usepackage{geometry}
\\geometry{margin=1in}
\\usepackage{parskip}
\\usepackage{longtable}
\\usepackage{array}
\\usepackage{booktabs}
\\usepackage{caption}
\\begin{document}

\\textbf{\\large Rapport de Mission d'Audit}

\\section*{1. Diffusion}
\\begin{itemize}
  \\item \\textbf{Chef de mission:} ${escapeLatex(reportData[0].chefmission) || 'N/A'}
  \\item \\textbf{Directeur d'audit:} ${escapeLatex(reportData[0].directeuraudit) || 'N/A'}
  \\item \\textbf{Autre(s) Participant(s):} ${escapeLatex(autresParticipants) || 'N/A'}
\\end{itemize}

\\section*{2. Résumé}
\\textbf{Mission d'audit:} ${escapeLatex(reportData[0].mission_name) || 'N/A'}
\\textbf{Catégorie:} ${escapeLatex(reportData[0].categorie) || 'N/A'}
\\textbf{Évaluation:} ${escapeLatex(reportData[0].evaluation) || 'N/A'}

\\subsection*{2.1 Objectif de la mission d'audit}
${escapeLatex(reportData[0].objectif) || 'N/A'}

\\subsection*{2.2 Points forts}
La mission a permis de constater que : ${escapeLatex(reportData[0].pointfort) || 'N/A'}

\\subsection*{2.3 Points faibles}
Une faiblesse majeure a été relevée : ${escapeLatex(reportData[0].pointfaible) || 'N/A'}

\\section*{3. Contexte}
\\subsection*{3.1 Managers opérationnels audités}
\\begin{longtable}{p{0.5\\textwidth}p{0.5\\textwidth}}
  \\hline
  \\textbf{Élément audité} & \\textbf{Propriétaire} \\\\
  \\hline
  ${escapeLatex(auditedElements) || 'N/A'} & ${escapeLatex(reportData[0].recommendation_responsable) || 'N/A'} \\\\
  \\hline
\\end{longtable}

\\subsection*{3.2 Coûts de la mission d'audit}
\\begin{longtable}{p{0.5\\textwidth}p{0.5\\textwidth}}
  \\hline
  \\textbf{Description} & \\textbf{Valeur} \\\\
  \\hline
  Charge de travail estimée (Heures) & ${escapeLatex(String(reportData[0].chargedetravailestimee)) || 'N/A'} \\\\
  Charge de travail effective (Heures) & ${escapeLatex(String(reportData[0].chargedetravaileffective)) || 'N/A'} \\\\
  Total des dépenses & ${escapeLatex(String(reportData[0].depenses)) || 'N/A'} \\\\
  \\hline
\\end{longtable}

\\subsection*{3.3 Ressources de la mission d'audit}
\\begin{longtable}{p{0.5\\textwidth}p{0.5\\textwidth}}
  \\hline
  \\textbf{Rôle} & \\textbf{Nom} \\\\
  \\hline
  Chef de mission & ${escapeLatex(reportData[0].chefmission) || 'N/A'} \\\\
  Principal audité & ${escapeLatex(reportData[0].principalAudite) || 'N/A'} \\\\
  Audité(s) & ${escapeLatex(auditedElements) || 'N/A'} \\\\
  Autre(s) Participant(s) & ${escapeLatex(autresParticipants) || 'N/A'} \\\\
  \\hline
\\end{longtable}

\\section*{4. Avis concernant la mission d'audit}
Les objectifs de la mission d'audit sont : ${escapeLatex(reportData[0].objectif) || 'N/A'}
Les sections suivantes présentent les détails du résultat de la mission d'audit.

\\subsection*{4.1 Points forts}
La mission a permis de constater que : ${escapeLatex(reportData[0].pointfort) || 'N/A'}

\\subsection*{4.2 Points faibles}
Une faiblesse majeure a été relevée : ${escapeLatex(reportData[0].pointfaible) || 'N/A'}

\\section*{5. Constats}
\\subsection*{5.1 Constats urgents}
\\begin{longtable}{p{0.5\\textwidth}p{0.5\\textwidth}}
  \\hline
  \\textbf{Constat} & \\textbf{Impact} \\\\
  \\hline
  ${reportData
    .filter(row => row.constat_impact === "très fort")
    .map(row => `${escapeLatex(row.constat_name) || 'N/A'} & ${escapeLatex(row.constat_impact) || 'N/A'} \\\\`)
    .join('\n  \\hline\n  ') || 'Aucun constat urgent trouvé.'}
  \\hline
\\end{longtable}

\\subsection*{5.2 Détails des constats et recommandations}
\\begin{longtable}{p{0.17\\textwidth}p{0.17\\textwidth}p{0.17\\textwidth}p{0.17\\textwidth}p{0.16\\textwidth}p{0.16\\textwidth}}
  \\hline
  \\textbf{Constat} & \\textbf{Impact} & \\textbf{Recommandation} & \\textbf{Détails} & \\textbf{Propriétaire} & \\textbf{Date de fin} \\\\
  \\hline
  ${reportData
    .filter(row => row.constat_name)
    .map(
      row =>
        `${escapeLatex(row.constat_name) || 'N/A'} & ${escapeLatex(row.constat_impact) || 'N/A'} & ${escapeLatex(row.recommendation_name) || 'N/A'} & ${escapeLatex(row.recommendation_details) || 'N/A'} & ${escapeLatex(row.recommendation_responsable) || 'N/A'} & ${escapeLatex(row.datefin) || 'N/A'} \\\\`
    )
    .join('\n  mcg\n')}
  \\hline
\\end{longtable}

\\end{document}
      `;

      // Write the LaTeX file
      try {
        await fs.writeFile(latexFilePath, latexContent);
        console.log(`[AuditMissionRapport] LaTeX file created at: ${latexFilePath}`);
      } catch (writeError) {
        console.error('[AuditMissionRapport] Failed to write LaTeX file:', writeError);
        return res.status(500).json({ message: 'Failed to create LaTeX file', error: writeError.message });
      }

      // Compile the LaTeX file to PDF
      try {
        const latexmkPath = 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\MiKTeX\\miktex\\bin\\x64\\latexmk.exe';
        const perlPath = 'C:\\Strawberry\\perl\\bin';
        const perlExe = path.join(perlPath, 'perl.exe');

        // Check if Perl exists, log warning if not
        try {
          await fs.access(perlExe);
          console.log(`[AuditMissionRapport] Perl found at: ${perlExe}`);
        } catch {
          console.warn(`[AuditMissionRapport] Perl not found at ${perlExe}. Falling back to system PATH.`);
        }

        const env = {
          ...process.env,
          MIKTEX_PERL: perlExe,
          PATH: `${perlPath};${process.env.PATH}`
        };
        console.log(`[AuditMissionRapport] Environment PATH: ${env.PATH}`);
        const command = `"${latexmkPath}" -pdf -outdir="${path.resolve(tempDir)}" "${latexFilePath}"`;
        console.log(`[AuditMissionRapport] Running command: ${command}`);
        const { stdout, stderr } = await execAsync(command, { env });
        console.log(`[AuditMissionRapport] Compilation stdout: ${stdout}`);
        if (stderr) console.warn(`[AuditMissionRapport] Compilation stderr: ${stderr}`);
      } catch (compileError) {
        console.error('[AuditMissionRapport] Failed to compile PDF:', compileError);
        console.error('[AuditMissionRapport] Compile error details:', compileError.stderr || 'No stderr');
        await fs.unlink(latexFilePath).catch(() => {});
        return res.status(500).json({
          message: 'Failed to compile PDF',
          error: compileError.message,
          stderr: compileError.stderr || 'No stderr'
        });
      }

      // Check if PDF exists and send it
      try {
        await fs.access(pdfFilePath);
        res.setHeader('Content-Type', 'application/pdf');
        res.setHeader('Content-Disposition', `attachment; filename=audit_report_${safeMissionId}.pdf`);
        res.sendFile(pdfFilePath, async (err) => {
          if (err) {
            console.error('[AuditMissionRapport] Failed to send PDF:', err);
            return res.status(500).json({ message: 'Failed to send PDF', error: err.message });
          }
          // Clean up temporary files
          try {
            await fs.unlink(latexFilePath);
            await fs.unlink(pdfFilePath);
            const auxFiles = ['aux', 'log', 'fls', 'fdb_latexmk'].map(ext => 
              path.join(tempDir, `audit_report_${safeMissionId}.${ext}`)
            );
            for (const file of auxFiles) await fs.unlink(file).catch(() => {});
          } catch (cleanupError) {
            console.error('[AuditMissionRapport] Cleanup failed:', cleanupError);
          }
        });
      } catch (accessError) {
        console.error('[AuditMissionRapport] PDF not found:', accessError);
        await fs.unlink(latexFilePath).catch(() => {});
        return res.status(500).json({ message: 'PDF not generated', error: accessError.message });
      }
    } else if (format === 'docx') {
      const doc = new Document({
        sections: [
          {
            properties: {
              page: {
                margin: {
                  top: 720,
                  right: 720,
                  bottom: 720,
                  left: 720,
                },
              },
            },
            children: [
              new Paragraph({
                text: 'Rapport de Mission d’Audit',
                heading: HeadingLevel.TITLE,
              }),
              new Paragraph({
                text: '1. Diffusion',
                heading: HeadingLevel.HEADING_1,
              }),
              new Paragraph(`Chef de mission: ${reportData[0].chefmission || 'N/A'}`),
              new Paragraph(`Directeur d'audit: ${reportData[0].directeuraudit || 'N/A'}`),
              new Paragraph(`Autre(s) Participant(s): ${autresParticipants || 'N/A'}`),
              new Paragraph({
                text: '2. Résumé',
                heading: HeadingLevel.HEADING_1,
              }),
              new Paragraph(`Mission d'audit: ${reportData[0].mission_name || 'N/A'}`),
              new Paragraph(`Catégorie: ${reportData[0].categorie || 'N/A'}`),
              new Paragraph(`Évaluation: ${reportData[0].evaluation || 'N/A'}`),
              new Paragraph({
                text: '2.1 Objectif de la mission d’audit',
                heading: HeadingLevel.HEADING_2,
              }),
              new Paragraph(reportData[0].objectif || 'N/A'),
              new Paragraph({
                text: '2.2 Points forts',
                heading: HeadingLevel.HEADING_2,
              }),
              new Paragraph(`La mission a permis de constater que : ${reportData[0].pointfort || 'N/A'}`),
              new Paragraph({
                text: '2.3 Points faibles',
                heading: HeadingLevel.HEADING_2,
              }),
              new Paragraph(`Une faiblesse majeure a été relevée : ${reportData[0].pointfaible || 'N/A'}`),
              new Paragraph({
                text: '3. Contexte',
                heading: HeadingLevel.HEADING_1,
              }),
              new Paragraph({
                text: '3.1 Managers opérationnels audités',
                heading: HeadingLevel.HEADING_2,
              }),
              new Table({
                width: {
                  size: 100,
                  type: WidthType.PERCENTAGE,
                },
                borders: {
                  top: { style: BorderStyle.SINGLE, size: 1 },
                  bottom: { style: BorderStyle.SINGLE, size: 1 },
                  left: { style: BorderStyle.SINGLE, size: 1 },
                  right: { style: BorderStyle.SINGLE, size: 1 },
                },
                rows: [
                  new TableRow({
                    children: [
                      new TableCell({
                        width: { size: 50, type: WidthType.PERCENTAGE },
                        children: [new Paragraph({ text: 'Élément audité', bold: true })],
                      }),
                      new TableCell({
                        width: { size: 50, type: WidthType.PERCENTAGE },
                        children: [new Paragraph({ text: 'Propriétaire', bold: true })],
                      }),
                    ],
                  }),
                  new TableRow({
                    children: [
                      new TableCell({
                        children: [new Paragraph(auditedElements || 'N/A')],
                      }),
                      new TableCell({
                        children: [new Paragraph(reportData[0].recommendation_responsable || 'N/A')],
                      }),
                    ],
                  }),
                ],
              }),
              new Paragraph({
                text: '3.2 Coûts de la mission d’audit',
                heading: HeadingLevel.HEADING_2,
              }),
              new Table({
                width: {
                  size: 100,
                  type: WidthType.PERCENTAGE,
                },
                borders: {
                  top: { style: BorderStyle.SINGLE, size: 1 },
                  bottom: { style: BorderStyle.SINGLE, size: 1 },
                  left: { style: BorderStyle.SINGLE, size: 1 },
                  right: { style: BorderStyle.SINGLE, size: 1 },
                },
                rows: [
                  new TableRow({
                    children: [
                      new TableCell({
                        width: { size: 50, type: WidthType.PERCENTAGE },
                        children: [new Paragraph({ text: 'Description', bold: true })],
                      }),
                      new TableCell({
                        width: { size: 50, type: WidthType.PERCENTAGE },
                        children: [new Paragraph({ text: 'Valeur', bold: true })],
                      }),
                    ],
                  }),
                  new TableRow({
                    children: [
                      new TableCell({
                        children: [new Paragraph('Charge de travail estimée (Heures)')],
                      }),
                      new TableCell({
                        children: [new Paragraph(String(reportData[0].chargedetravailestimee || 'N/A'))],
                      }),
                    ],
                  }),
                  new TableRow({
                    children: [
                      new TableCell({
                        children: [new Paragraph('Charge de travail effective (Heures)')],
                      }),
                      new TableCell({
                        children: [new Paragraph(String(reportData[0].chargedetravaileffective || 'N/A'))],
                      }),
                    ],
                  }),
                  new TableRow({
                    children: [
                      new TableCell({
                        children: [new Paragraph('Total des dépenses')],
                      }),
                      new TableCell({
                        children: [new Paragraph(String(reportData[0].depenses || 'N/A'))],
                      }),
                    ],
                  }),
                ],
              }),
              new Paragraph({
                text: '3.3 Ressources de la mission d’audit',
                heading: HeadingLevel.HEADING_2,
              }),
              new Table({
                width: {
                  size: 100,
                  type: WidthType.PERCENTAGE,
                },
                borders: {
                  top: { style: BorderStyle.SINGLE, size: 1 },
                  bottom: { style: BorderStyle.SINGLE, size: 1 },
                  left: { style: BorderStyle.SINGLE, size: 1 },
                  right: { style: BorderStyle.SINGLE, size: 1 },
                },
                rows: [
                  new TableRow({
                    children: [
                      new TableCell({
                        width: { size: 50, type: WidthType.PERCENTAGE },
                        children: [new Paragraph({ text: 'Rôle', bold: true })],
                      }),
                      new TableCell({
                        width: { size: 50, type: WidthType.PERCENTAGE },
                        children: [new Paragraph({ text: 'Nom', bold: true })],
                      }),
                    ],
                  }),
                  new TableRow({
                    children: [
                      new TableCell({
                        children: [new Paragraph('Chef de mission')],
                      }),
                      new TableCell({
                        children: [new Paragraph(reportData[0].chefmission || 'N/A')],
                      }),
                    ],
                  }),
                  new TableRow({
                    children: [
                      new TableCell({
                        children: [new Paragraph('Principal audité')],
                      }),
                      new TableCell({
                        children: [new Paragraph(reportData[0].principalAudite || 'N/A')],
                      }),
                    ],
                  }),
                  new TableRow({
                    children: [
                      new TableCell({
                        children: [new Paragraph('Audité(s)')],
                      }),
                      new TableCell({
                        children: [new Paragraph(auditedElements || 'N/A')],
                      }),
                    ],
                  }),
                  new TableRow({
                    children: [
                      new TableCell({
                        children: [new Paragraph('Autre(s) Participant(s)')],
                      }),
                      new TableCell({
                        children: [new Paragraph(autresParticipants || 'N/A')],
                      }),
                    ],
                  }),
                ],
              }),
              new Paragraph({
                text: '4. Avis concernant la mission d’audit',
                heading: HeadingLevel.HEADING_1,
              }),
              new Paragraph(`Les objectifs de la mission d’audit sont : ${reportData[0].objectif || 'N/A'}`),
              new Paragraph('Les sections suivantes présentent les détails du résultat de la mission d’audit.'),
              new Paragraph({
                text: '4.1 Points forts',
                heading: HeadingLevel.HEADING_2,
              }),
              new Paragraph(`La mission a permis de constater que : ${reportData[0].pointfort || 'N/A'}`),
              new Paragraph({
                text: '4.2 Points faibles',
                heading: HeadingLevel.HEADING_2,
              }),
              new Paragraph(`Une faiblesse majeure a été relevée : ${reportData[0].pointfaible || 'N/A'}`),
              new Paragraph({
                text: '5. Constats',
                heading: HeadingLevel.HEADING_1,
              }),
              new Paragraph({
                text: '5.1 Constats urgents',
                heading: HeadingLevel.HEADING_2,
              }),
              new Table({
                width: {
                  size: 100,
                  type: WidthType.PERCENTAGE,
                },
                borders: {
                  top: { style: BorderStyle.SINGLE, size: 1 },
                  bottom: { style: BorderStyle.SINGLE, size: 1 },
                  left: { style: BorderStyle.SINGLE, size: 1 },
                  right: { style: BorderStyle.SINGLE, size: 1 },
                },
                rows: [
                  new TableRow({
                    children: [
                      new TableCell({
                        width: { size: 50, type: WidthType.PERCENTAGE },
                        children: [new Paragraph({ text: 'Constat', bold: true })],
                      }),
                      new TableCell({
                        width: { size: 50, type: WidthType.PERCENTAGE },
                        children: [new Paragraph({ text: 'Impact', bold: true })],
                      }),
                    ],
                  }),
                  ...reportData
                    .filter(row => row.constat_impact === 'très fort')
                    .map(
                      row =>
                        new TableRow({
                          children: [
                            new TableCell({
                              children: [new Paragraph(row.constat_name || 'N/A')],
                            }),
                            new TableCell({
                              children: [new Paragraph(row.constat_impact || 'N/A')],
                            }),
                          ],
                        })
                    ),
                  ...(reportData.filter(row => row.constat_impact === 'très fort').length === 0
                    ? [
                        new TableRow({
                          children: [
                            new TableCell({
                              children: [new Paragraph('Aucun constat urgent trouvé.')],
                              columnSpan: 2,
                            }),
                          ],
                        }),
                      ]
                    : []),
                ],
              }),
              new Paragraph({
                text: '5.2 Détails des constats et recommandations',
                heading: HeadingLevel.HEADING_2,
              }),
              new Table({
                width: {
                  size: 100,
                  type: WidthType.PERCENTAGE,
                },
                borders: {
                  top: { style: BorderStyle.SINGLE, size: 1 },
                  bottom: { style: BorderStyle.SINGLE, size: 1 },
                  left: { style: BorderStyle.SINGLE, size: 1 },
                  right: { style: BorderStyle.SINGLE, size: 1 },
                },
                rows: [
                  new TableRow({
                    children: [
                      new TableCell({
                        width: { size: 17, type: WidthType.PERCENTAGE },
                        children: [new Paragraph({ text: 'Constat', bold: true })],
                      }),
                      new TableCell({
                        width: { size: 17, type: WidthType.PERCENTAGE },
                        children: [new Paragraph({ text: 'Impact', bold: true })],
                      }),
                      new TableCell({
                        width: { size: 17, type: WidthType.PERCENTAGE },
                        children: [new Paragraph({ text: 'Recommandation', bold: true })],
                      }),
                      new TableCell({
                        width: { size: 17, type: WidthType.PERCENTAGE },
                        children: [new Paragraph({ text: 'Détails', bold: true })],
                      }),
                      new TableCell({
                        width: { size: 16, type: WidthType.PERCENTAGE },
                        children: [new Paragraph({ text: 'Propriétaire', bold: true })],
                      }),
                      new TableCell({
                        width: { size: 16, type: WidthType.PERCENTAGE },
                        children: [new Paragraph({ text: 'Date de fin', bold: true })],
                      }),
                    ],
                  }),
                  ...reportData
                    .filter(row => row.constat_name)
                    .map(
                      row =>
                        new TableRow({
                          children: [
                            new TableCell({
                              children: [new Paragraph(row.constat_name || 'N/A')],
                            }),
                            new TableCell({
                              children: [new Paragraph(row.constat_impact || 'N/A')],
                            }),
                            new TableCell({
                              children: [new Paragraph(row.recommendation_name || 'N/A')],
                            }),
                            new TableCell({
                              children: [new Paragraph(row.recommendation_details || 'N/A')],
                            }),
                            new TableCell({
                              children: [new Paragraph(row.recommendation_responsable || 'N/A')],
                            }),
                            new TableCell({
                              children: [new Paragraph(row.datefin || 'N/A')],
                            }),
                          ],
                        })
                    ),
                ],
              }),
            ],
          },
        ],
      });

      const buffer = await Packer.toBuffer(doc);
      console.log('[AuditMissionRapport] DOCX buffer size:', buffer.length);
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document');
      res.setHeader('Content-Disposition', `attachment; filename=audit_report_${missionId}.docx`);
      res.send(buffer);
    } else {
      res.status(400).json({ message: 'Invalid format specified. Use ?format=pdf or ?format=docx' });
    }
  } catch (error) {
    console.error('[AuditMissionRapport] Error generating report:', error);
    res.status(500).json({ message: 'Error generating report', error: error.message });
  }
};