import React, { useState } from 'react';

import { <PERSON>, Card<PERSON><PERSON>nt, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Plus, Check, ExternalLink, MoreHorizontal, ClipboardList, Calendar, ChevronRight, Save, CheckCircle2, RefreshCw, Clock, AlertCircle, XCircle, CheckSquare, Square } from "lucide-react";
import { toast } from "sonner";
import { useMissionAuditContext } from '@/utils/context-helpers';
import { OutletContext } from '@/pages/audit-view/missions-audits/edit-missions-audits';

function WorkflowTab(props) {
  const contextData = useMissionAuditContext();
  const missionAudit = props.missionAudit || contextData.missionAudit;
  const [workflowSteps, setWorkflowSteps] = useState([
    {
      id: 1,
      order: 1,
      name: "Planification",
      description: "Définir les objectifs, le périmètre et les ressources de l'audit",
      assignedTo: "Alice Durand",
      status: "Completed",
      dueDate: "2023-09-15",
      completedDate: "2023-09-12",
      completedBy: "Alice Durand",
      comments: "La planification a été terminée plus tôt que prévu",
      isExpanded: false
    },
    {
      id: 2,
      order: 2,
      name: "Collecte de données",
      description: "Recueillir les documents et informations nécessaires à l'audit",
      assignedTo: "Thomas Martin",
      status: "InProgress",
      dueDate: "2023-10-01",
      completedDate: null,
      completedBy: null,
      comments: "",
      isExpanded: false
    },
    {
      id: 3,
      order: 3,
      name: "Analyse des risques",
      description: "Identifier et évaluer les risques liés au périmètre d'audit",
      assignedTo: "Marie Petit",
      status: "Pending",
      dueDate: "2023-10-15",
      completedDate: null,
      completedBy: null,
      comments: "",
      isExpanded: false
    },
    {
      id: 4,
      order: 4,
      name: "Exécution des tests",
      description: "Réaliser les tests d'audit selon le programme défini",
      assignedTo: "Paul Dubois",
      status: "Pending",
      dueDate: "2023-10-30",
      completedDate: null,
      completedBy: null,
      comments: "",
      isExpanded: false
    },
    {
      id: 5,
      order: 5,
      name: "Rédaction du rapport",
      description: "Élaborer le rapport d'audit avec les constatations et recommandations",
      assignedTo: "Sophie Leroy",
      status: "Pending",
      dueDate: "2023-11-15",
      completedDate: null,
      completedBy: null,
      comments: "",
      isExpanded: false
    },
    {
      id: 6,
      order: 6,
      name: "Revue et validation",
      description: "Revue du rapport par la direction de l'audit",
      assignedTo: "Jean Moreau",
      status: "Pending",
      dueDate: "2023-11-30",
      completedDate: null,
      completedBy: null,
      comments: "",
      isExpanded: false
    },
    {
      id: 7,
      order: 7,
      name: "Présentation des résultats",
      description: "Présentation des résultats aux parties prenantes",
      assignedTo: "Alice Durand",
      status: "Pending",
      dueDate: "2023-12-15",
      completedDate: null,
      completedBy: null,
      comments: "",
      isExpanded: false
    }
  ]);

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingStep, setEditingStep] = useState(null);
  
  const [newStep, setNewStep] = useState({
    name: "",
    description: "",
    assignedTo: "",
    status: "Pending",
    dueDate: new Date().toISOString().split('T')[0],
    comments: ""
  });

  // If no mission data is available yet
  if (!missionAudit || !missionAudit.id) {
    return (
      <div className="flex items-center justify-center h-48">
        <p className="text-gray-500">Chargement du workflow de la mission...</p>
      </div>
    );
  }

  const handleSave = () => {
    toast.success("Workflow sauvegardé avec succès");
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewStep(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name, value) => {
    setNewStep(prev => ({ ...prev, [name]: value }));
  };

  const handleAddStep = () => {
    if (!newStep.name || !newStep.dueDate) {
      toast.error("Veuillez remplir au moins le nom et la date d'échéance");
      return;
    }
    
    const step = {
      ...newStep,
      id: editingStep ? editingStep.id : Date.now(),
      order: editingStep ? editingStep.order : workflowSteps.length + 1,
      completedDate: newStep.status === "Completed" ? new Date().toISOString().split('T')[0] : null,
      completedBy: newStep.status === "Completed" ? "Current User" : null,
      isExpanded: false
    };

    if (editingStep) {
      // Update existing step
      setWorkflowSteps(prev => prev.map(s => 
        s.id === editingStep.id ? step : s
      ));
      toast.success("Étape mise à jour avec succès");
    } else {
      // Add new step
      setWorkflowSteps(prev => [...prev, step]);
      toast.success("Nouvelle étape ajoutée avec succès");
    }

    // Reset form and close dialog
    setNewStep({
      name: "",
      description: "",
      assignedTo: "",
      status: "Pending",
      dueDate: new Date().toISOString().split('T')[0],
      comments: ""
    });
    setEditingStep(null);
    setIsDialogOpen(false);
  };

  const handleEditStep = (step) => {
    setEditingStep(step);
    setNewStep({
      name: step.name,
      description: step.description,
      assignedTo: step.assignedTo,
      status: step.status,
      dueDate: step.dueDate,
      comments: step.comments
    });
    setIsDialogOpen(true);
  };

  const handleDeleteStep = (id) => {
    setWorkflowSteps(prev => {
      const filtered = prev.filter(step => step.id !== id);
      // Reorder the remaining steps
      return filtered.map((step, index) => ({
        ...step,
        order: index + 1
      }));
    });
    toast.success("Étape supprimée avec succès");
  };

  const handleToggleExpand = (id) => {
    setWorkflowSteps(prev => prev.map(step => 
      step.id === id ? { ...step, isExpanded: !step.isExpanded } : step
    ));
  };

  const handleToggleComplete = (id) => {
    setWorkflowSteps(prev => prev.map(step => {
      if (step.id === id) {
        const newStatus = step.status === "Completed" ? "Pending" : "Completed";
        return { 
          ...step,
          status: newStatus,
          completedDate: newStatus === "Completed" ? new Date().toISOString().split('T')[0] : null,
          completedBy: newStatus === "Completed" ? "Current User" : null
        };
      }
      return step;
    }));
  };

  const handleUpdateStatus = (id, status) => {
    setWorkflowSteps(prev => prev.map(step => {
      if (step.id === id) {
        return {
          ...step,
          status,
          completedDate: status === "Completed" ? new Date().toISOString().split('T')[0] : null,
          completedBy: status === "Completed" ? "Current User" : null
        };
      }
      return step;
    }));
    toast.success(`Statut mis à jour: ${getStatusText(status)}`);
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case 'Completed':
        return <Badge className="bg-green-100 text-green-800">Terminée</Badge>;
      case 'InProgress':
        return <Badge className="bg-blue-100 text-blue-800">En cours</Badge>;
      case 'Pending':
        return <Badge className="bg-yellow-100 text-yellow-800">À faire</Badge>;
      case 'Blocked':
        return <Badge className="bg-red-100 text-red-800">Bloquée</Badge>;
      case 'Cancelled':
        return <Badge className="bg-gray-100 text-gray-800">Annulée</Badge>;
      default:
        return <Badge variant="outline">-</Badge>;
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'Completed': return 'Terminée';
      case 'InProgress': return 'En cours';
      case 'Pending': return 'À faire';
      case 'Blocked': return 'Bloquée';
      case 'Cancelled': return 'Annulée';
      default: return '-';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'Completed':
        return <CheckCircle2 className="h-5 w-5 text-green-600" />;
      case 'InProgress':
        return <RefreshCw className="h-5 w-5 text-blue-600" />;
      case 'Pending':
        return <Clock className="h-5 w-5 text-yellow-600" />;
      case 'Blocked':
        return <AlertCircle className="h-5 w-5 text-red-600" />;
      case 'Cancelled':
        return <XCircle className="h-5 w-5 text-gray-600" />;
      default:
        return <Clock className="h-5 w-5 text-gray-400" />;
    }
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return "-";
    return new Date(dateString).toLocaleDateString('fr-FR');
  };

  // Calculate progress
  const totalSteps = workflowSteps.length;
  const completedSteps = workflowSteps.filter(step => step.status === "Completed").length;
  const inProgressSteps = workflowSteps.filter(step => step.status === "InProgress").length;
  const progressPercentage = totalSteps > 0 ? Math.round((completedSteps / totalSteps) * 100) : 0;

  return (
    <div className="space-y-6 py-4">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-[#1A202C] flex items-center">
          <ClipboardList className="h-6 w-6 mr-3 text-[#F62D51]" />
          Workflow
        </h2>
        <div className="flex space-x-2">
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button className="bg-[#F62D51] hover:bg-[#F62D51]/90">
                <Plus className="h-4 w-4 mr-2" />
                Ajouter une Étape
        </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>{editingStep ? "Modifier l'Étape" : "Ajouter une Nouvelle Étape"}</DialogTitle>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Nom de l'étape *</Label>
                  <Input 
                    id="name" 
                    name="name"
                    value={newStep.name}
                    onChange={handleInputChange}
                    placeholder="Nom de l'étape"
                  />
      </div>

            <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea 
                    id="description" 
                    name="description"
                    value={newStep.description}
                    onChange={handleInputChange}
                    placeholder="Description détaillée de l'étape"
                    rows={2}
                  />
              </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="assignedTo">Assigné à</Label>
                    <Input 
                      id="assignedTo" 
                      name="assignedTo"
                      value={newStep.assignedTo}
                      onChange={handleInputChange}
                      placeholder="Personne responsable"
                    />
            </div>
            
                  <div className="space-y-2">
                    <Label htmlFor="dueDate">Date d'échéance *</Label>
                    <Input 
                      id="dueDate" 
                      name="dueDate"
                      type="date"
                      value={newStep.dueDate}
                      onChange={handleInputChange}
                    />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="status">Statut</Label>
                  <Select 
                    name="status"
                    value={newStep.status} 
                    onValueChange={(value) => handleSelectChange("status", value)}
                  >
                    <SelectTrigger id="status">
                      <SelectValue placeholder="Sélectionner un statut" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Pending">À faire</SelectItem>
                      <SelectItem value="InProgress">En cours</SelectItem>
                      <SelectItem value="Completed">Terminée</SelectItem>
                      <SelectItem value="Blocked">Bloquée</SelectItem>
                      <SelectItem value="Cancelled">Annulée</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="comments">Commentaires</Label>
                  <Textarea 
                    id="comments" 
                    name="comments"
                    value={newStep.comments}
                    onChange={handleInputChange}
                    placeholder="Commentaires additionnels"
                    rows={2}
                  />
                </div>
              </div>
              
              <div className="flex justify-end gap-2 mt-4">
                <Button variant="outline" onClick={() => {
                  setIsDialogOpen(false);
                  setEditingStep(null);
                  setNewStep({
                    name: "",
                    description: "",
                    assignedTo: "",
                    status: "Pending",
                    dueDate: new Date().toISOString().split('T')[0],
                    comments: ""
                  });
                }}>
                  Annuler
                </Button>
                <Button className="bg-[#F62D51] hover:bg-[#F62D51]/90" onClick={handleAddStep}>
                  {editingStep ? "Mettre à jour" : "Ajouter"}
                </Button>
              </div>
            </DialogContent>
          </Dialog>
          
          <Button onClick={handleSave} variant="outline" className="border-[#F62D51] text-[#F62D51]">
            <Save className="h-4 w-4 mr-2" />
            Sauvegarder
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Progression Globale</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-[#1A202C]">{progressPercentage}%</div>
            <div className="w-full bg-gray-200 rounded-full h-2.5 mt-2">
              <div 
                className="h-2.5 rounded-full bg-green-500"
                style={{ width: `${progressPercentage}%` }}
              ></div>
            </div>
            <div className="text-sm text-gray-500 mt-2">
              {completedSteps} sur {totalSteps} étapes terminées
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Étapes en Cours</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-blue-600">{inProgressSteps}</div>
            <div className="text-sm text-gray-500 mt-1">
              Étapes actuellement en cours de réalisation
          </div>
        </CardContent>
      </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Étapes Restantes</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-orange-600">{totalSteps - completedSteps}</div>
            <div className="text-sm text-gray-500 mt-1">
              Étapes à finaliser avant la fin de la mission
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Étapes du Workflow</CardTitle>
        </CardHeader>
        <CardContent>
          {workflowSteps.length === 0 ? (
            <div className="text-center py-8 border rounded-md">
              <ClipboardList className="h-12 w-12 mx-auto text-gray-300 mb-3" />
              <p className="text-gray-500">Aucune étape définie pour cette mission d'audit.</p>
              <p className="text-sm text-gray-400">Cliquez sur "Ajouter une Étape" pour commencer à définir le workflow.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {workflowSteps
                .sort((a, b) => a.order - b.order)
                .map((step) => (
                <div key={step.id} className="border rounded-md overflow-hidden">
                  <div 
                    className={`flex items-center justify-between p-4 cursor-pointer ${
                      step.status === 'Completed' ? 'bg-green-50' : 
                      step.status === 'InProgress' ? 'bg-blue-50' : 
                      step.status === 'Blocked' ? 'bg-red-50' : 
                      step.status === 'Cancelled' ? 'bg-gray-50' : 'bg-white'
                    }`}
                    onClick={() => handleToggleExpand(step.id)}
                  >
                    <div className="flex items-center space-x-3">
                      <button 
                        className="focus:outline-none"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleToggleComplete(step.id);
                        }}
                      >
                        {step.status === 'Completed' ? (
                          <CheckSquare className="h-5 w-5 text-green-600" />
                        ) : (
                          <Square className="h-5 w-5 text-gray-400" />
                        )}
                      </button>
                      
                      <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-sm font-medium">
                        {step.order}
                      </div>
                      
                      <div>
                        <h3 className="font-medium">{step.name}</h3>
                        <div className="flex items-center text-sm text-gray-500 space-x-3">
                          <span className="flex items-center">
                            <Calendar className="h-3.5 w-3.5 mr-1" />
                            {formatDate(step.dueDate)}
                          </span>
                          {step.assignedTo && (
                            <span>Assigné à: {step.assignedTo}</span>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                      {getStatusBadge(step.status)}
                      <ChevronRight className={`h-5 w-5 text-gray-400 transition-transform ${step.isExpanded ? 'rotate-90' : ''}`} />
                    </div>
                  </div>
                  
                  {step.isExpanded && (
                    <div className="p-4 border-t bg-white">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                          <h4 className="text-sm font-medium text-gray-500 mb-1">Description</h4>
                          <p className="text-sm">{step.description || "Aucune description fournie"}</p>
              </div>
              
                        <div>
                          <h4 className="text-sm font-medium text-gray-500 mb-1">Commentaires</h4>
                          <p className="text-sm">{step.comments || "Aucun commentaire"}</p>
              </div>
            </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                        <div>
                          <h4 className="text-sm font-medium text-gray-500 mb-1">Date d'échéance</h4>
                          <p className="text-sm">{formatDate(step.dueDate)}</p>
                        </div>
                        
                        <div>
                          <h4 className="text-sm font-medium text-gray-500 mb-1">Date de réalisation</h4>
                          <p className="text-sm">{formatDate(step.completedDate) || "-"}</p>
                        </div>
                        
                        <div>
                          <h4 className="text-sm font-medium text-gray-500 mb-1">Réalisé par</h4>
                          <p className="text-sm">{step.completedBy || "-"}</p>
                      </div>
                      </div>
                      
                      <div className="flex items-center justify-between pt-2 border-t">
                        <div className="flex items-center space-x-2">
                          <span className="text-sm font-medium text-gray-700">Mettre à jour le statut:</span>
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            className="text-xs h-8 px-2 text-yellow-700"
                            onClick={() => handleUpdateStatus(step.id, "Pending")}
                          >
                            <Clock className="h-3.5 w-3.5 mr-1" />
                            À faire
                          </Button>
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            className="text-xs h-8 px-2 text-blue-700"
                            onClick={() => handleUpdateStatus(step.id, "InProgress")}
                          >
                            <RefreshCw className="h-3.5 w-3.5 mr-1" />
                            En cours
                          </Button>
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            className="text-xs h-8 px-2 text-green-700"
                            onClick={() => handleUpdateStatus(step.id, "Completed")}
                          >
                            <CheckCircle2 className="h-3.5 w-3.5 mr-1" />
                            Terminée
                          </Button>
                        <Button 
                          variant="ghost" 
                          size="sm"
                            className="text-xs h-8 px-2 text-red-700"
                            onClick={() => handleUpdateStatus(step.id, "Blocked")}
                          >
                            <AlertCircle className="h-3.5 w-3.5 mr-1" />
                            Bloquée
                          </Button>
                        </div>
                        
                        <div className="flex items-center space-x-2">
                          <Button 
                            variant="outline" 
                            size="sm" 
                            className="text-xs h-8"
                            onClick={() => handleEditStep(step)}
                          >
                            Modifier
                          </Button>
                          <Button 
                            variant="outline" 
                            size="sm" 
                            className="text-xs h-8 text-red-600"
                            onClick={() => handleDeleteStep(step.id)}
                          >
                            Supprimer
                        </Button>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
                ))}
          </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

export default WorkflowTab; 