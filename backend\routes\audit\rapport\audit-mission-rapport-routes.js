const express = require('express');
const router = express.Router();
// const { verifyToken, authorizeRoles } = require('../../../middleware/auth');
const auditMissionRapportController = require('../../../controllers/audit/rapport/audit-mission-rapport-controller');

// Apply authentication middleware to all routes    
// router.use(verifyToken);

// Get mission report
router.get('/mission-report/:missionId' , auditMissionRapportController.getMissionReport);

module.exports = router;