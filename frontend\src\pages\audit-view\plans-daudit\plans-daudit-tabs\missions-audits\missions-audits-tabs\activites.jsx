import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import LoadingProgress from "@/components/ui/loading-progress";
import { Plus, ClipboardList, Save, Calendar, CheckCircle2, Edit, Trash2, Clock, AlertCircle } from "lucide-react";
import { toast } from "sonner";
import { useMissionAuditContext } from '@/utils/context-helpers';

function ActivitesTab(props) {
  // Accept direct prop or get from context
  const contextData = useMissionAuditContext();
  const missionAudit = props.missionAudit || contextData.missionAudit;
  const [activities, setActivities] = useState([]);
  
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingActivity, setEditingActivity] = useState(null);
  const [newActivity, setNewActivity] = useState({
    title: "",
    description: "",
    status: "Planned",
    assignedTo: "",
    startDate: "",
    endDate: "",
    hoursEstimated: 0,
    hoursSpent: 0,
    completionRate: 0,
    comments: ""
  });

  // If no mission data is available yet
  if (!missionAudit || !missionAudit.id) {
    return (
      <div className="flex items-center justify-center h-48">
        <p className="text-gray-500">Chargement des activités de la mission...</p>
      </div>
    );
  }

  const handleSave = () => {
    toast.success("Activités sauvegardées avec succès");
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewActivity(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name, value) => {
    setNewActivity(prev => ({ ...prev, [name]: value }));
  };

  const handleAddActivity = () => {
    if (!newActivity.title || !newActivity.startDate || !newActivity.endDate) {
      toast.error("Veuillez remplir au moins le titre, la date de début et la date de fin");
      return;
    }

    if (editingActivity) {
      // Update existing activity
      setActivities(prev => prev.map(activity => 
        activity.id === editingActivity.id ? { ...newActivity, id: activity.id } : activity
      ));
      toast.success("Activité mise à jour avec succès");
    } else {
      // Add new activity
      setActivities(prev => [...prev, { ...newActivity, id: Date.now() }]);
      toast.success("Nouvelle activité ajoutée avec succès");
    }

    // Reset form and close dialog
    setNewActivity({
      title: "",
      description: "",
      status: "Planned",
      assignedTo: "",
      startDate: "",
      endDate: "",
      hoursEstimated: 0,
      hoursSpent: 0,
      completionRate: 0,
      comments: ""
    });
    setEditingActivity(null);
    setIsDialogOpen(false);
  };

  const handleEditActivity = (activity) => {
    setEditingActivity(activity);
    setNewActivity(activity);
    setIsDialogOpen(true);
  };

  const handleDeleteActivity = (id) => {
    setActivities(prev => prev.filter(activity => activity.id !== id));
    toast.success("Activité supprimée avec succès");
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case 'Completed':
        return <Badge className="bg-green-100 text-green-800">Terminée</Badge>;
      case 'In Progress':
        return <Badge className="bg-blue-100 text-blue-800">En cours</Badge>;
      case 'Planned':
        return <Badge className="bg-yellow-100 text-yellow-800">Planifiée</Badge>;
      case 'Canceled':
        return <Badge className="bg-gray-100 text-gray-800">Annulée</Badge>;
      default:
        return <Badge variant="outline">-</Badge>;
    }
  };

  const getProgressColor = (rate) => {
    if (rate >= 100) return "bg-green-500";
    if (rate >= 50) return "bg-blue-500";
    if (rate > 0) return "bg-yellow-500";
    return "bg-gray-200";
  };

  const getTotalCompletionRate = () => {
    if (activities.length === 0) return 0;
    return Math.round(activities.reduce((sum, act) => sum + act.completionRate, 0) / activities.length);
  };

  const getTotalHoursSpent = () => {
    return activities.reduce((sum, act) => sum + (act.hoursSpent || 0), 0);
  };

  const getTotalHoursEstimated = () => {
    return activities.reduce((sum, act) => sum + (act.hoursEstimated || 0), 0);
  };

  return (
    <div className="space-y-6 py-4">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-[#1A202C] flex items-center">
          <ClipboardList className="h-6 w-6 mr-3 text-[#F62D51]" />
          Activités d'Audit
        </h2>
        <div className="flex space-x-2">
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button className="bg-[#F62D51] hover:bg-[#F62D51]/90">
                <Plus className="h-4 w-4 mr-2" />
                Ajouter une Activité
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>{editingActivity ? "Modifier l'Activité" : "Ajouter une Nouvelle Activité"}</DialogTitle>
                <DialogDescription>
                  Complétez les informations ci-dessous pour {editingActivity ? "modifier" : "ajouter"} une activité.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Titre *</Label>
                  <Input 
                    id="title" 
                    name="title"
                    value={newActivity.title}
                    onChange={handleInputChange}
                    placeholder="Titre de l'activité"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea 
                    id="description" 
                    name="description"
                    value={newActivity.description}
                    onChange={handleInputChange}
                    placeholder="Description détaillée de l'activité"
                    rows={2}
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="startDate">Date de début *</Label>
                    <Input 
                      id="startDate" 
                      name="startDate"
                      type="date"
                      value={newActivity.startDate}
                      onChange={handleInputChange}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="endDate">Date de fin *</Label>
                    <Input 
                      id="endDate" 
                      name="endDate"
                      type="date"
                      value={newActivity.endDate}
                      onChange={handleInputChange}
                    />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="assignedTo">Assignée à</Label>
                  <Input 
                    id="assignedTo" 
                    name="assignedTo"
                    value={newActivity.assignedTo}
                    onChange={handleInputChange}
                    placeholder="Nom de la personne responsable"
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="hoursEstimated">Heures estimées</Label>
                    <Input 
                      id="hoursEstimated" 
                      name="hoursEstimated"
                      type="number"
                      value={newActivity.hoursEstimated}
                      onChange={handleInputChange}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="hoursSpent">Heures passées</Label>
                    <Input 
                      id="hoursSpent" 
                      name="hoursSpent"
                      type="number"
                      value={newActivity.hoursSpent}
                      onChange={handleInputChange}
                    />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="status">Statut</Label>
                  <Select 
                    name="status"
                    value={newActivity.status} 
                    onValueChange={(value) => handleSelectChange("status", value)}
                  >
                    <SelectTrigger id="status">
                      <SelectValue placeholder="Sélectionner un statut" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Planned">Planifiée</SelectItem>
                      <SelectItem value="In Progress">En cours</SelectItem>
                      <SelectItem value="Completed">Terminée</SelectItem>
                      <SelectItem value="Canceled">Annulée</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="completionRate">Taux d'achèvement (%)</Label>
                  <Input 
                    id="completionRate" 
                    name="completionRate"
                    type="number"
                    min="0"
                    max="100"
                    value={newActivity.completionRate}
                    onChange={handleInputChange}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="comments">Commentaires</Label>
                  <Textarea 
                    id="comments" 
                    name="comments"
                    value={newActivity.comments}
                    onChange={handleInputChange}
                    placeholder="Notes, observations ou commentaires"
                    rows={2}
                  />
                </div>
              </div>
              
              <div className="flex justify-end gap-2 mt-4">
                <Button variant="outline" onClick={() => {
                  setIsDialogOpen(false);
                  setEditingActivity(null);
                  setNewActivity({
                    title: "",
                    description: "",
                    status: "Planned",
                    assignedTo: "",
                    startDate: "",
                    endDate: "",
                    hoursEstimated: 0,
                    hoursSpent: 0,
                    completionRate: 0,
                    comments: ""
                  });
                }}>
                  Annuler
                </Button>
                <Button className="bg-[#F62D51] hover:bg-[#F62D51]/90" onClick={handleAddActivity}>
                  {editingActivity ? "Mettre à jour" : "Ajouter"}
                </Button>
              </div>
            </DialogContent>
          </Dialog>
          
          <Button onClick={handleSave} variant="outline" className="border-[#F62D51] text-[#F62D51]">
            <Save className="h-4 w-4 mr-2" />
            Sauvegarder
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg">Résumé des Activités</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className="bg-gray-50 p-4 rounded-md">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-gray-600">Progression Globale</span>
                <span className="text-sm font-semibold">{getTotalCompletionRate()}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2.5">
                <div 
                  className={`h-2.5 rounded-full ${getProgressColor(getTotalCompletionRate())}`}
                  style={{ width: `${getTotalCompletionRate()}%` }}
                ></div>
              </div>
            </div>
            <div className="bg-gray-50 p-4 rounded-md">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Heures Passées</span>
                <span className="text-lg font-bold">{getTotalHoursSpent()}h</span>
              </div>
              <div className="text-xs text-gray-500 mt-1">sur {getTotalHoursEstimated()}h estimées</div>
            </div>
            <div className="bg-gray-50 p-4 rounded-md">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Statut des Activités</span>
              </div>
              <div className="flex gap-2 mt-2 flex-wrap">
                <Badge className="bg-green-100 text-green-800">
                  {activities.filter(a => a.status === 'Completed').length} terminées
                </Badge>
                <Badge className="bg-blue-100 text-blue-800">
                  {activities.filter(a => a.status === 'In Progress').length} en cours
                </Badge>
                <Badge className="bg-yellow-100 text-yellow-800">
                  {activities.filter(a => a.status === 'Planned').length} planifiées
                </Badge>
              </div>
            </div>
          </div>
          
          <div className="text-center py-8 border rounded-md">
            <AlertCircle className="h-12 w-12 mx-auto text-gray-300 mb-3" />
            <p className="text-gray-500">Aucune activité définie pour cette mission d'audit.</p>
            <p className="text-sm text-gray-400">Cliquez sur "Ajouter une Activité" pour commencer.</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default ActivitesTab; 