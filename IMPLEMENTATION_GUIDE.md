# Activity Feed Implementation Guide

## Overview

The Activity Feed feature tracks and displays comprehensive logs of all activities related to a risk, including:
- Risk creation events
- Workflow transitions (state changes)
- Field updates (changed values)

This guide provides details on all components implemented and the steps needed to deploy and test the feature.

## Completed Implementation

### 1. Data Models

- **Created ActivityLog Model**:
  - Located in `backend/models/ActivityLog.js`
  - Stores detailed activity data including:
    - Creation events
    - Field update events with old and new values
    - User information
    - Timestamps
    - Reference to risk ID
  
- **Created Migration Script**:
  - Located in `backend/migrations/20240620000000-create-activity-log.js`
  - Creates the ActivityLogs table with proper constraints and indexes

### 2. Backend Controllers & Routes

- **Activity Controller**:
  - Located in `backend/controllers/risks/activity-controller.js`
  - Implemented functions:
    - `getRiskActivities`: Fetches and returns all activities for a risk
    - `logCreationActivity`: Logs when a risk is created
    - `logUpdateActivities`: Logs field changes when a risk is updated
  
- **Updated Risk Controller**:
  - Modified `backend/controllers/risks/risk-controller.js`
  - Added activity logging to:
    - Risk creation process
    - Risk update process

- **Updated Risk Routes**:
  - Added activity endpoint to `backend/routes/risks/risk-routes.js`
  - New endpoint: `GET /api/risk/:id/activity`

### 3. Frontend Components

- **Activity Feed Component**:
  - Created at `frontend/src/pages/admin-view/risks/risks-tabs/activity-feed.jsx`
  - Features:
    - Timeline display of all activities
    - Color-coded activity types (creation, transition, update)
    - User attribution
    - Timestamp formatting
    - Field change details

- **Route Configuration**:
  - Updated `frontend/src/App.jsx` to include the Activity Feed tab
  - The EditRisk component already had the tab configuration

## Deployment Steps

1. **Run Migration**:
   ```bash
   npx sequelize-cli db:migrate
   ```

2. **Restart Backend**:
   ```bash
   cd backend
   npm run dev  # or whatever command you use to start your backend
   ```

3. **Restart Frontend**:
   ```bash
   cd frontend
   npm run dev  # or your frontend start command
   ```

## Testing

1. **Create a New Risk**:
   - Create a new risk through the UI
   - Check the Activity Feed tab to see the creation event logged

2. **Update Risk Fields**:
   - Edit various risk fields (name, description, impact, etc.)
   - Verify that each change appears in the Activity Feed with old and new values

3. **Workflow Transitions**:
   - Advance the risk through different workflow states
   - Verify that each transition appears in the Activity Feed

## Troubleshooting

### Common Issues

- **Database Connection**: If migrations fail, check database connection settings in `.env`
- **Missing Activity Data**: Verify that user information is being passed correctly in API requests
- **Feed Not Updating**: Check that the `fetchActivities` function in the frontend component is working correctly

### Database Rollback

If needed, you can rollback the migration with:
```bash
npx sequelize-cli db:migrate:undo --name 20240620000000-create-activity-log.js
``` 