import React, { useState, useEffect, createContext, useContext } from 'react';
import { useParams, useNavigate, useLocation, Outlet } from 'react-router-dom';
import { Button } from "@/components/ui/button";
import {
  ChevronLeft,
  FileText,
  ClipboardList,
  ListFilter,
  AlertTriangle,
  CheckSquare,
  File,
  DollarSign,
  FileBarChart,
  GitBranch,
  ArrowLeft,
  CheckCircle,
  Clock,
  Calendar,
  Info,
  Target,
  BarChart,
  ShieldCheck,
  Laptop,
  Settings,
  Tag,
  Building,
  CalendarDays,
  CalendarRange,
  CalendarCheck,
  User,
  UserCircle,
  ThumbsUp,
  CircleAlert,
  Badge
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import DetailHeader from "@/components/ui/detail-header";
import { TabsContent } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { toast } from 'sonner';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import axios from 'axios';
import { getApiBaseUrl } from '@/utils/api-config';

// Import tab components directly from the same folder structure
import CaracteristiquesTab from './missions-audits-tabs/caracteristiques';
import PerimetreProgrammeTab from './missions-audits-tabs/perimetre-programme';

// Create a placeholder tab component
const PlaceholderTab = ({ title }) => (
  <div className="p-6 bg-gray-50 rounded-md border border-gray-200">
    <h3 className="text-xl font-medium text-gray-800 mb-4">{title}</h3>
    <p className="text-gray-600 mb-4">
      Cette section est en cours de développement. Le contenu sera disponible prochainement.
    </p>
    <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-md">
      <p className="text-blue-700 text-sm">
        <AlertTriangle className="h-4 w-4 inline-block mr-2" />
        Fonctionnalité à venir dans la prochaine mise à jour
      </p>
    </div>
  </div>
);

// Add KPI helpers from caracteristiques.jsx
const getProgressionColor = (progress) => {
  if (progress < 30) return "from-red-500 to-red-600";
  if (progress < 70) return "from-yellow-500 to-yellow-600";
  return "from-green-500 to-green-600";
};
const getEvaluationColor = (evaluation) => {
  switch(evaluation) {
    case "Critique": return "from-red-500 to-red-600";
    case "Insuffisant": return "from-orange-500 to-orange-600";
    case "Moyen": return "from-yellow-500 to-yellow-600";
    case "Bon": return "from-blue-500 to-blue-600";
    case "Excellent": return "from-green-500 to-green-600";
    default: return "from-gray-500 to-gray-600";
  }
};
const getRecommendationsColor = (count) => {
  if (count > 15) return "from-red-500 to-red-600";
  if (count > 8) return "from-orange-500 to-orange-600";
  if (count > 3) return "from-yellow-500 to-yellow-600";
  return "from-green-500 to-green-600";
};

// Replace MissionDetailsTab with a new version matching the risks overview style (no KPI boxes)
const MissionDetailsTab = ({ mission }) => {
  // Helper function to format date
  const formatDate = (dateString) => {
    if (!dateString) return "Non définie";
    try {
      return new Date(dateString).toLocaleDateString("fr-FR", {
        year: "numeric",
        month: "long",
        day: "numeric",
      });
    } catch {
      return dateString;
    }
  };

  return (
    <div className="space-y-8">
      {/* KPI Boxes */}
      <div className="flex flex-wrap gap-4 mb-6">
        {/* Progression Card */}
        <div className={`rounded-lg shadow-md overflow-hidden bg-gradient-to-br ${getProgressionColor(mission?.progress || 0)} w-52`}>
          <div className="p-2 text-white">
            <div className="flex items-center gap-2">
              <BarChart className="h-4 w-4" />
              <h3 className="text-xs font-medium">Avancement</h3>
            </div>
            <div className="mt-1 text-xl font-bold">{mission?.progress || 0}%</div>
            <div className="w-full bg-white/30 h-1.5 rounded-full mt-1 mb-1">
              <div
                className="h-full bg-white rounded-full"
                style={{ width: `${mission?.progress || 0}%` }}
              ></div>
            </div>
          </div>
        </div>
        {/* Evaluation Card */}
        <div className={`rounded-lg shadow-md overflow-hidden bg-gradient-to-br ${getEvaluationColor(mission?.evaluation)} w-52`}>
          <div className="p-2 text-white">
            <div className="flex items-center gap-2">
              <Target className="h-4 w-4" />
              <h3 className="text-xs font-medium">Évaluation</h3>
            </div>
            <div className="mt-1 text-xl font-bold">{mission?.evaluation || 'N/A'}</div>
            <div className="flex items-center mt-1 mb-1">
              {mission?.evaluation === "Critique" && <CircleAlert className="h-4 w-4 text-white" />}
              {mission?.evaluation === "Insuffisant" && <AlertTriangle className="h-4 w-4 text-white" />}
              {mission?.evaluation === "Moyen" && <BarChart className="h-4 w-4 text-white" />}
              {mission?.evaluation === "Bon" && <Target className="h-4 w-4 text-white" />}
              {mission?.evaluation === "Excellent" && <CheckCircle className="h-4 w-4 text-white" />}
              <span className="ml-1 text-xs">{mission?.evaluation === "Critique" ? "À corriger" :
               mission?.evaluation === "Insuffisant" ? "À améliorer" :
               mission?.evaluation === "Moyen" ? "Acceptable" :
               mission?.evaluation === "Bon" ? "Bien" : "Excellent"}</span>
            </div>
          </div>
        </div>
        {/* Recommendations Card */}
        <div className={`rounded-lg shadow-md overflow-hidden bg-gradient-to-br ${getRecommendationsColor(mission?.recommendations || 0)} w-52`}>
          <div className="p-2 text-white">
            <div className="flex items-center gap-2">
              <CheckSquare className="h-4 w-4" />
              <h3 className="text-xs font-medium">Recommandations</h3>
            </div>
            <div className="mt-1 text-xl font-bold">{mission?.recommendations || 0}</div>
            <div className="flex items-center mt-1 mb-1">
              <Badge className="bg-white/30 text-white border-0 text-xs">
                {mission?.recommendations > 15 ? "Critique" :
                 mission?.recommendations > 8 ? "Élevé" :
                 mission?.recommendations > 3 ? "Moyen" : "Faible"}
              </Badge>
            </div>
          </div>
        </div>
      </div>
      {/* General Information Card */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
        <div className="bg-gray-50 px-4 py-3 border-b">
          <h3 className="text-lg font-medium text-gray-800 flex items-center">
            <Info className="h-5 w-5 mr-2 text-blue-500" />
            Informations générales
          </h3>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-4">
          <div className="flex items-start space-x-3">
            <div className="bg-purple-50 p-2 rounded-full"><FileText className="h-5 w-5 text-purple-500" /></div>
            <div>
              <p className="text-sm font-medium text-gray-500">Nom</p>
              <p className="text-base font-semibold">{mission?.name || 'N/A'}</p>
            </div>
          </div>
          <div className="flex items-start space-x-3">
            <div className="bg-emerald-50 p-2 rounded-full"><Tag className="h-5 w-5 text-emerald-500" /></div>
            <div>
              <p className="text-sm font-medium text-gray-500">Code</p>
              <p className="text-base font-semibold">{mission?.code || 'N/A'}</p>
            </div>
          </div>
          <div className="flex items-start space-x-3">
            <div className="bg-pink-50 p-2 rounded-full"><UserCircle className="h-5 w-5 text-pink-500" /></div>
            <div>
              <p className="text-sm font-medium text-gray-500">Chef de mission</p>
              <p className="text-base font-semibold">{mission?.chefmission ? `ID: ${mission.chefmission}` : 'N/A'}</p>
            </div>
          </div>
          <div className="flex items-start space-x-3">
            <div className="bg-yellow-50 p-2 rounded-full"><User className="h-5 w-5 text-yellow-500" /></div>
            <div>
              <p className="text-sm font-medium text-gray-500">Auditeur</p>
              <p className="text-base font-semibold">{mission?.auditor || 'N/A'}</p>
            </div>
          </div>
          <div className="flex items-start space-x-3">
            <div className="bg-gray-50 p-2 rounded-full"><CalendarCheck className="h-5 w-5 text-gray-500" /></div>
            <div>
              <p className="text-sm font-medium text-gray-500">Planifié initialement</p>
              <p className="text-base font-semibold">{mission?.initiallyPlanned ? 'Oui' : 'Non'}</p>
            </div>
          </div>
          <div className="flex items-start space-x-3">
            <div className="bg-indigo-50 p-2 rounded-full"><Tag className="h-5 w-5 text-indigo-500" /></div>
            <div>
              <p className="text-sm font-medium text-gray-500">Catégorie</p>
              <p className="text-base font-semibold">{mission?.category || 'N/A'}</p>
            </div>
          </div>
          <div className="flex items-start space-x-3">
            <div className="bg-green-50 p-2 rounded-full"><CheckCircle className="h-5 w-5 text-green-500" /></div>
            <div>
              <p className="text-sm font-medium text-gray-500">Statut</p>
              <p className="text-base font-semibold">{mission?.status || 'N/A'}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Calendar Card */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
        <div className="bg-gray-50 px-4 py-3 border-b">
          <h3 className="text-lg font-medium text-gray-800 flex items-center">
            <Calendar className="h-5 w-5 mr-2 text-purple-500" />
            Calendrier
          </h3>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 p-4">
          <div className="flex items-start space-x-3">
            <div className="bg-blue-50 p-2 rounded-full"><CalendarDays className="h-5 w-5 text-blue-500" /></div>
            <div>
              <p className="text-sm font-medium text-gray-500">Date de début</p>
              <p className="text-base font-semibold">{formatDate(mission?.startDate)}</p>
            </div>
          </div>
          <div className="flex items-start space-x-3">
            <div className="bg-pink-50 p-2 rounded-full"><CalendarRange className="h-5 w-5 text-pink-500" /></div>
            <div>
              <p className="text-sm font-medium text-gray-500">Date de fin</p>
              <p className="text-base font-semibold">{formatDate(mission?.endDate)}</p>
            </div>
          </div>
          <div className="flex items-start space-x-3">
            <div className="bg-gray-50 p-2 rounded-full"><Clock className="h-5 w-5 text-gray-500" /></div>
            <div>
              <p className="text-sm font-medium text-gray-500">Durée</p>
              <p className="text-base font-semibold">
                {mission?.startDate && mission?.endDate ?
                  `${Math.ceil((new Date(mission.endDate) - new Date(mission.startDate)) / (1000 * 60 * 60 * 24))} jours` :
                  "Non définie"}
              </p>
            </div>
          </div>
          <div className="flex items-start space-x-3">
            <div className="bg-red-50 p-2 rounded-full">
              {mission?.isDelayed ? <AlertTriangle className="h-5 w-5 text-red-500" /> : <CheckCircle className="h-5 w-5 text-green-500" />}
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">En retard</p>
              <p className={`text-base font-semibold ${mission?.isDelayed ? "text-red-600" : "text-green-600"}`}>
                {mission?.isDelayed ? "Oui" : "Non"}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Description and Objectives Card */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
        <div className="bg-gray-50 px-4 py-3 border-b">
          <h3 className="text-lg font-medium text-gray-800 flex items-center">
            <FileText className="h-5 w-5 mr-2 text-amber-500" />
            Description et objectifs
          </h3>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 p-4">
          <div>
            <h4 className="text-sm font-medium text-gray-500 mb-2 flex items-center">
              <FileText className="h-4 w-4 mr-1 text-gray-400" />
              Description
            </h4>
            <div className="p-4 bg-amber-50 border border-amber-100 rounded-lg">
              <p className="text-gray-800">{mission?.description || "Aucune description disponible"}</p>
            </div>
          </div>
          <div>
            <h4 className="text-sm font-medium text-gray-500 mb-2 flex items-center">
              <Target className="h-4 w-4 mr-1 text-gray-400" />
              Objectifs
            </h4>
            <div className="p-4 bg-blue-50 border border-blue-100 rounded-lg">
              <p className="text-gray-800">{mission?.objectives || "Aucun objectif défini"}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Create a context to simulate the outlet context
export const OutletContext = createContext(null);

// Custom hook to access the outlet context data
export const useCustomOutletContext = () => {
  const context = useContext(OutletContext);
  if (context === undefined) {
    throw new Error('useCustomOutletContext must be used within an OutletContextProvider');
  }
  return context;
};

// Main component for editing missions audits
function EditMissionsAudits() {
  const { missionAuditId } = useParams();
  const { planId } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const [activeTab, setActiveTab] = useState(() => {
    if (location.pathname.includes('/activites/')) {
      return "activites";
    }
    return "mission-details";
  });
  const [missionAudit, setMissionAudit] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  // Add function to update mission data
  const updateMissionData = async () => {
    try {
      const response = await axios.get(`${getApiBaseUrl()}/audit-missions/${missionAuditId}`);
      
      if (response.data.success) {
        setMissionAudit({
          ...response.data.data,
          planId: planId
        });
      }
    } catch (error) {
      console.error("Erreur lors de la mise à jour de la mission", error);
      toast.error("Erreur lors de la mise à jour de la mission");
    }
  };

  useEffect(() => {
    const fetchMissionData = async () => {
      try {
        setIsLoading(true);
        const response = await axios.get(`${getApiBaseUrl()}/audit-missions/${missionAuditId}`);
        
        if (response.data.success) {
          setMissionAudit({
            ...response.data.data,
            planId: planId
          });
        } else {
          toast.error("Mission d'audit non trouvée");
          navigate(-1);
        }
      } catch (error) {
        console.error("Erreur lors du chargement de la mission", error);
        toast.error("Erreur lors du chargement de la mission");
        navigate(-1);
      } finally {
        setIsLoading(false);
      }
    };

    if (missionAuditId) {
      fetchMissionData();
    }
  }, [missionAuditId, planId, navigate]);

  useEffect(() => {
    if (location.pathname.includes('/activites/')) {
      setActiveTab("activites");
    }
  }, [location.pathname]);

  const handleGoBack = () => {
    // Navigate back to the audit plan missions list if planId is available
    if (missionAudit?.planId) {
      navigate(`/audit/plans-daudit/${missionAudit.planId}/missions-audits`);
    } else {
      navigate('/audit/missions-audits');
    }
  };

  // Define tabs configuration with icons
  const tabs = [
    { id: "mission-details", label: "Détails de la mission", icon: <FileText className="h-4 w-4 mr-2" /> },
    { id: "caracteristiques", label: "Caractéristiques", icon: <FileText className="h-4 w-4 mr-2" /> },
    { id: "perimetre", label: "Périmètre", icon: <ListFilter className="h-4 w-4 mr-2" /> },
    { id: "evaluation-risques", label: "Évaluation Risques", icon: <AlertTriangle className="h-4 w-4 mr-2" /> },
    { id: "activites", label: "Activités", icon: <CheckSquare className="h-4 w-4 mr-2" /> },
    { id: "recommandations", label: "Recommandations", icon: <ClipboardList className="h-4 w-4 mr-2" /> },
    { id: "documents", label: "Documents", icon: <File className="h-4 w-4 mr-2" /> },
    { id: "depenses", label: "Dépenses", icon: <DollarSign className="h-4 w-4 mr-2" /> },
    { id: "rapport", label: "Rapport", icon: <FileBarChart className="h-4 w-4 mr-2" /> },
    { id: "workflow", label: "Workflow", icon: <GitBranch className="h-4 w-4 mr-2" /> },
  ];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!missionAudit) {
    return (
      <div className="p-4">
        <Button variant="outline" onClick={handleGoBack} className="mb-4">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Retour
        </Button>
        <div className="text-center py-8">
          <h2 className="text-2xl font-semibold text-gray-800 mb-2">Mission non trouvée</h2>
          <p className="text-gray-600">La mission d'audit demandée n'existe pas ou a été supprimée.</p>
        </div>
      </div>
    );
  }

  // Get tab content based on active tab
  const getTabContent = () => {
    switch (activeTab) {
      case "mission-details":
        return <MissionDetailsTab mission={missionAudit} />;
      case "caracteristiques":
        return <CaracteristiquesTab missionAudit={missionAudit} />;
      case "perimetre":
        return <PerimetreProgrammeTab missionAudit={missionAudit} />;
      case "evaluation-risques":
        return <PlaceholderTab title="Évaluation des Risques" />;
      case "activites":
        // Check if we're already on an activites route
        if (!location.pathname.includes('/activites/')) {
          // Navigate to the perimetre-programme tab which contains the activities list
          navigate(planId
            ? `/audit/plans-daudit/${planId}/missions-audits/${missionAuditId}/perimetre`
            : `/audit/missions-audits/${missionAuditId}/perimetre`);
        }
        return <PerimetreProgrammeTab missionAudit={missionAudit} />;
      case "recommandations":
        return <PlaceholderTab title="Recommandations" />;
      case "documents":
        return <PlaceholderTab title="Documents" />;
      case "depenses":
        return <PlaceholderTab title="Dépenses" />;
      case "rapport":
        return <PlaceholderTab title="Rapport" />;
      case "workflow":
        return <PlaceholderTab title="Workflow" />;
      default:
        return <div>Tab content not found</div>;
    }
  };

  // Create context value with mission audit data and parent IDs
  const contextValue = {
    missionAudit,
    planId,
    updateMissionData
  };

  // Get status badge details
  const getStatusBadgeInfo = () => {
    switch (missionAudit.status) {
      case 'In Progress':
        return { label: 'En cours', variant: 'default', color: 'bg-blue-100 text-blue-800' };
      case 'Completed':
        return { label: 'Terminée', variant: 'default', color: 'bg-green-100 text-green-800' };
      case 'Planned':
        return { label: 'Planifiée', variant: 'default', color: 'bg-yellow-100 text-yellow-800' };
      default:
        return { label: missionAudit.status, variant: 'outline', color: '' };
    }
  };

  // Generate metadata
  const metadata = [
    `${missionAudit.code || ''}`,
    `${missionAudit.department || ''}`,
    `${missionAudit.startDate || ''} - ${missionAudit.endDate || ''}`
  ];

  return (
    <div className="p-6 max-w-[1200px] mx-auto space-y-6">
      {/* Header with mission information and breadcrumb */}
      <DetailHeader
        title={missionAudit.name}
        icon={<FileText className="h-6 w-6 text-[#F62D51]" />}
        badges={[getStatusBadgeInfo()]}
        metadata={metadata}
        onBack={handleGoBack}
        backLabel="Retour à la liste des missions"
        breadcrumb={
          <Breadcrumb>
            <BreadcrumbList className="text-sm">
              <BreadcrumbItem>
                <BreadcrumbLink href="/audit" className="text-gray-500 font-medium">Audit</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbLink href={`/audit/plans-daudit`} className="text-gray-800 hover:text-gray-600 font-medium">
                  Plans d'audit
                </BreadcrumbLink>
              </BreadcrumbItem>
              {planId && (
                <>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbLink href={`/audit/plans-daudit/${planId}`} className="text-gray-800 hover:text-gray-600 font-medium">
                      Plan {planId}
                    </BreadcrumbLink>
                  </BreadcrumbItem>
                </>
              )}
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage className="text-gray-800 font-medium">Mission d'audit</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        }
      />

      <OutletContext.Provider value={contextValue}>
        {/* Custom tab navigation */}
        <div className="bg-white rounded-lg shadow-sm mb-6">
          <div className="border-b border-gray-200">
            <nav className="flex overflow-x-auto">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`
                    px-4 py-4 text-sm font-medium whitespace-nowrap border-b-2 flex items-center
                    ${activeTab === tab.id
                      ? "border-[#F62D51] text-[#F62D51]"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}
                  `}
                >
                  {tab.icon}
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Tab content or child routes */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          {/* Check if we're on a child route (activites) */}
          {location.pathname.includes('/activites/') ? (
            <Outlet context={contextValue} />
          ) : (
            getTabContent()
          )}
        </div>
      </OutletContext.Provider>
    </div>
  );
}

// Export the component both as default and named export for different routes
export { EditMissionsAudits as default, EditMissionsAudits as EditMissionsAuditsStandalone };