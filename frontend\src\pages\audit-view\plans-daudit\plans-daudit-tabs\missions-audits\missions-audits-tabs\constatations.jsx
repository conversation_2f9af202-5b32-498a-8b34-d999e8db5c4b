import React, { useState, useContext } from 'react';

import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Plus, Search, Save, Edit, Trash2, ClipboardList, AlertCircle } from "lucide-react";
import { toast } from "sonner";
import { useMissionAuditContext } from '@/utils/context-helpers';
import { OutletContext } from '@/pages/audit-view/missions-audits/edit-missions-audits';
import { <PERSON><PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";

function ConstatationsTab(props) {
  const contextData = useMissionAuditContext();
  const missionAudit = props.missionAudit || contextData.missionAudit;
  const [findings, setFindings] = useState([]);
  
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingFinding, setEditingFinding] = useState(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [newFinding, setNewFinding] = useState({
    title: "",
    description: "",
    domain: "",
    severity: "Medium",
    evidence: "",
    impact: "",
    responsibleDepartment: "",
    relatedRisks: []
  });

  // If no mission data is available yet
  if (!missionAudit || !missionAudit.id) {
    return (
      <div className="flex items-center justify-center h-48">
        <p className="text-gray-500">Chargement des constatations...</p>
      </div>
    );
  }

  const handleSave = () => {
    toast.success("Constatations sauvegardées avec succès");
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewFinding(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name, value) => {
    setNewFinding(prev => ({ ...prev, [name]: value }));
  };

  const handleRelatedRisksChange = (e) => {
    const value = e.target.value;
    setNewFinding(prev => ({ 
      ...prev, 
      relatedRisks: value.split(',').map(risk => risk.trim()).filter(risk => risk !== '')
    }));
  };

  const handleAddFinding = () => {
    if (!newFinding.title || !newFinding.description) {
      toast.error("Veuillez remplir au moins le titre et la description");
      return;
    }

    if (editingFinding) {
      // Update existing finding
      setFindings(prev => prev.map(finding => 
        finding.id === editingFinding.id ? { ...newFinding, id: finding.id } : finding
      ));
      toast.success("Constatation mise à jour avec succès");
    } else {
      // Add new finding
      setFindings(prev => [...prev, { ...newFinding, id: Date.now() }]);
      toast.success("Nouvelle constatation ajoutée avec succès");
    }

    // Reset form and close dialog
    setNewFinding({
      title: "",
      description: "",
      domain: "",
      severity: "Medium",
      evidence: "",
      impact: "",
      responsibleDepartment: "",
      relatedRisks: []
    });
    setEditingFinding(null);
    setIsDialogOpen(false);
  };

  const handleEditFinding = (finding) => {
    setEditingFinding(finding);
    setNewFinding(finding);
    setIsDialogOpen(true);
  };

  const handleDeleteFinding = (id) => {
    setFindings(prev => prev.filter(finding => finding.id !== id));
    toast.success("Constatation supprimée avec succès");
  };

  const getSeverityBadge = (severity) => {
    switch (severity) {
      case 'Low':
        return <Badge className="bg-green-100 text-green-800">Faible</Badge>;
      case 'Medium':
        return <Badge className="bg-yellow-100 text-yellow-800">Moyenne</Badge>;
      case 'High':
        return <Badge className="bg-red-100 text-red-800">Élevée</Badge>;
      default:
        return <Badge variant="outline">-</Badge>;
    }
  };

  const filteredFindings = findings.filter(finding => 
    finding.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    finding.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    finding.domain.toLowerCase().includes(searchQuery.toLowerCase()) ||
    finding.responsibleDepartment.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="space-y-6 py-4">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-[#1A202C] flex items-center">
          <ClipboardList className="h-6 w-6 mr-3 text-[#F62D51]" />
          Constatations d'Audit
        </h2>
        <div className="flex space-x-2">
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button className="bg-[#F62D51] hover:bg-[#F62D51]/90">
                <Plus className="h-4 w-4 mr-2" />
                Ajouter une Constatation
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-xl">
              <DialogHeader>
                <DialogTitle>{editingFinding ? "Modifier la Constatation" : "Ajouter une Nouvelle Constatation"}</DialogTitle>
                <DialogDescription>
                  Complétez les informations ci-dessous pour {editingFinding ? "modifier" : "ajouter"} une constatation d'audit.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Titre *</Label>
                  <Input 
                    id="title" 
                    name="title"
                    value={newFinding.title}
                    onChange={handleInputChange}
                    placeholder="Titre de la constatation"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="description">Description *</Label>
                  <Textarea 
                    id="description" 
                    name="description"
                    value={newFinding.description}
                    onChange={handleInputChange}
                    placeholder="Description détaillée de la constatation"
                    rows={3}
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="domain">Domaine</Label>
                    <Input 
                      id="domain" 
                      name="domain"
                      value={newFinding.domain}
                      onChange={handleInputChange}
                      placeholder="Ex: Finance, Conformité, IT"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="severity">Sévérité</Label>
                    <Select 
                      name="severity"
                      value={newFinding.severity} 
                      onValueChange={(value) => handleSelectChange("severity", value)}
                    >
                      <SelectTrigger id="severity">
                        <SelectValue placeholder="Niveau de sévérité" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Low">Faible</SelectItem>
                        <SelectItem value="Medium">Moyenne</SelectItem>
                        <SelectItem value="High">Élevée</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="evidence">Éléments Probants</Label>
                  <Textarea 
                    id="evidence" 
                    name="evidence"
                    value={newFinding.evidence}
                    onChange={handleInputChange}
                    placeholder="Décrivez les preuves ou tests justifiant cette constatation"
                    rows={2}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="impact">Impact</Label>
                  <Textarea 
                    id="impact" 
                    name="impact"
                    value={newFinding.impact}
                    onChange={handleInputChange}
                    placeholder="Impact potentiel de cette constatation pour l'organisation"
                    rows={2}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="responsibleDepartment">Département Concerné</Label>
                  <Input 
                    id="responsibleDepartment" 
                    name="responsibleDepartment"
                    value={newFinding.responsibleDepartment}
                    onChange={handleInputChange}
                    placeholder="Département responsable de traiter cette constatation"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="relatedRisks">Risques Associés</Label>
                  <Textarea 
                    id="relatedRisks" 
                    name="relatedRisks"
                    value={newFinding.relatedRisks.join(', ')}
                    onChange={handleRelatedRisksChange}
                    placeholder="Liste des risques associés, séparés par des virgules"
                    rows={2}
                  />
                </div>
              </div>
              
              <div className="flex justify-end gap-2 mt-4">
                <Button variant="outline" onClick={() => {
                  setIsDialogOpen(false);
                  setEditingFinding(null);
                  setNewFinding({
                    title: "",
                    description: "",
                    domain: "",
                    severity: "Medium",
                    evidence: "",
                    impact: "",
                    responsibleDepartment: "",
                    relatedRisks: []
                  });
                }}>
                  Annuler
                </Button>
                <Button className="bg-[#F62D51] hover:bg-[#F62D51]/90" onClick={handleAddFinding}>
                  {editingFinding ? "Mettre à jour" : "Ajouter"}
                </Button>
              </div>
            </DialogContent>
          </Dialog>
          
          <Button onClick={handleSave} variant="outline" className="border-[#F62D51] text-[#F62D51]">
            <Save className="h-4 w-4 mr-2" />
            Sauvegarder
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg">Résumé des Constatations</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className="bg-gray-50 p-3 rounded-md">
              <p className="text-sm text-gray-500">Total</p>
              <p className="text-2xl font-bold">{findings.length}</p>
            </div>
            <div className="bg-gray-50 p-3 rounded-md">
              <p className="text-sm text-gray-500">Constatations Critiques</p>
              <p className="text-2xl font-bold text-red-600">
                {findings.filter(finding => finding.severity === 'High').length}
              </p>
            </div>
            <div className="bg-gray-50 p-3 rounded-md">
              <p className="text-sm text-gray-500">Constatations par Domaine</p>
              <div className="flex flex-wrap gap-2 mt-2">
                {Array.from(new Set(findings.map(f => f.domain))).map(domain => (
                  <Badge key={domain} variant="secondary">{domain}</Badge>
                ))}
              </div>
            </div>
          </div>
          
          <div className="text-center py-8 border rounded-md">
            <AlertCircle className="h-12 w-12 mx-auto text-gray-300 mb-3" />
            <p className="text-gray-500">Aucune constatation d'audit formulée.</p>
            <p className="text-sm text-gray-400">Cliquez sur "Ajouter une Constatation" pour commencer.</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default ConstatationsTab; 