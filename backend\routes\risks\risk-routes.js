const express = require('express');
const router = express.Router();
const { verifyToken, authorizeRoles } = require('../../middleware/auth');
const { Risk } = require('../../models');
const {
  getAllRisks,
  createRisk,
  getRiskById,
  updateRisk,
  deleteRisk,
  deleteMultipleRisks
} = require('../../controllers/risks/risk-controller');
const { getRiskActivities } = require('../../controllers/risks/activity-controller');
const { getRisksByResidualLevel } = require('../../models/rapport/ResidualRiskRapport.js');

// Apply authentication middleware to all routes
router.use(verifyToken);

// Get all risks
router.get('/', getAllRisks);

// Create new risk
router.post('/', createRisk);

// Get risk by ID
router.get('/:id', getRiskById);

// Get risk activities
router.get('/:id/activity', getRiskActivities);

// Update risk
router.put('/:id', updateRisk);

// Delete risk
router.delete('/:id', authorizeRoles(['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager']), deleteRisk);

// Delete multiple risks
router.post('/delete-multiple', authorizeRoles(['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager']), deleteMultipleRisks);

// Get risks by probability and impact for matrix
router.get('/matrix/:probability/:impact', verifyToken, async (req, res) => {
  try {
    const { probability, impact } = req.params;

    // Validate parameters
    const prob = parseInt(probability);
    const imp = parseInt(impact);
    if (isNaN(prob) || isNaN(imp) || prob < 1 || prob > 5 || imp < 1 || imp > 5) {
      return res.status(400).json({ message: 'Invalid probability or impact value' });
    }

    // Fetch risks using Sequelize
    const risks = await Risk.findAll({
      where: {
        probability: prob,
        impact: imp
      },
      attributes: ['riskID', 'name', 'comment', 'probability', 'impact']
    });

    if (!risks || risks.length === 0) {
      return res.status(404).json({ message: 'No risks found for this probability and impact' });
    }

    // Map risks to match frontend field names
    const formattedRisks = risks.map(risk => ({
      riskID: risk.riskID,
      name: risk.name,
      description: risk.comment, // Map comment to description for frontend
      probability: risk.probability,
      impact: risk.impact
    }));

    res.json(formattedRisks);
  } catch (error) {
    console.error('Error fetching matrix risks:', error.stack);
    res.status(500).json({ message: 'Error fetching risks for matrix cell', error: error.message });
  }
});

// Route for residual risks
router.get('/residual/:inherentRiskLevel/:DMR', verifyToken, async (req, res) => {
  try {
    const { inherentRiskLevel, DMR } = req.params;
    const inherentRiskLevelInt = parseInt(inherentRiskLevel);
    const DMRInt = parseInt(DMR);

    // Validate parameters
    if (
      isNaN(inherentRiskLevelInt) || isNaN(DMRInt) ||
      inherentRiskLevelInt < 1 || inherentRiskLevelInt > 5 ||
      ![1, 4, 9, 16, 25].includes(DMRInt)
    ) {
      return res.status(400).json({ message: 'Invalid inherentRiskLevel or DMR value' });
    }

    // Fetch risks
    const risks = await getRisksByResidualLevel(inherentRiskLevelInt, DMRInt);
    res.json(risks);
  } catch (error) {
    console.error('Error fetching residual risks:', error.stack);
    res.status(500).json({ message: 'Error fetching residual risks', error: error.message });
  }
});

module.exports = router;