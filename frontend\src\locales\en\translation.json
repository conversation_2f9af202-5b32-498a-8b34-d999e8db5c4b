{"common": {"creating": "Creating...", "saving": "Saving...", "header": {"search": "Search...", "notifications": "Notifications", "mark_all_read": "Mark all as read", "no_notifications": "No notifications.", "show_more": "Show more notifications", "more": "more", "check_profile": "Check Profile", "help": "Help", "logout": "Logout", "language": "Language"}, "sidebar": {"toggle_menu": "Toggle <PERSON>"}, "tabs": {"overview": "Overview", "features": "Features", "details": "Details", "actions": "Actions", "activity": "Activity", "evaluation": "Evaluation"}, "buttons": {"save": "Save", "cancel": "Cancel", "add": "Add", "edit": "Edit", "delete": "Delete", "create": "Create", "update": "Update", "back": "Back", "next": "Next", "previous": "Previous", "submit": "Submit", "confirm": "Confirm", "search": "Search", "view": "View", "retry": "Retry"}, "status": {"active": "Active", "inactive": "Inactive", "pending": "Pending", "completed": "Completed", "in_progress": "In Progress", "cancelled": "Cancelled", "delayed": "Delayed", "all": "All Status", "open": "Open", "resolved": "Resolved"}, "priority": {"all": "All Priorities", "high": "High", "medium": "Medium", "low": "Low"}, "fields": {"name": "Name", "description": "Description", "status": "Status", "type": "Type", "category": "Category", "date": "Date", "start_date": "Start Date", "end_date": "End Date", "created_at": "Created At", "updated_at": "Updated At", "created_by": "Created By", "updated_by": "Updated By", "comments": "Comments", "actions": "Actions", "title": "Title", "priority": "Priority", "reported_date": "Reported Date", "last_updated": "Last Updated"}}, "admin": {"action_plans": {"title": "Action Plans Management", "description": "Define and manage action plans within your organization.", "loading": "Loading action plans...", "error": "Error: {{message}}", "not_found": "Action plan not found", "back_to_list": "Back to Action Plans", "delete_confirm": "Are you sure you want to delete {{count}} action plan(s)?", "error_creating": "Error creating action plan: {{error}}", "search_placeholder": "Search action plans...", "filters": {"approver": "Approver", "assignee": "Assignee", "category": "Category", "nature": "Nature", "origin": "Origin", "priority": "Priority", "organizational_level": "Organizational Level", "means": "Means", "select_approver": "Select approver", "select_assignee": "Select assignee", "select_category": "Select category", "select_nature": "Select nature", "select_origin": "Select origin", "select_priority": "Select priority", "select_organizational_level": "Select organizational level", "select_means": "Select means"}, "columns": {"name": "Name", "approver": "Approver", "assignee": "Assignee", "category": "Category", "nature": "Nature", "origin": "Origin", "priority": "Priority", "organizational_level": "Organizational Level", "means": "Means", "comment": "Comment", "planned_begin_date": "Planned Begin Date", "planned_end_date": "Planned End Date"}, "form": {"name": "Name", "nature": "Nature", "comment": "Comment", "name_placeholder": "Action plan name", "nature_placeholder": "Action plan nature", "comment_placeholder": "Action plan comment"}, "tabs": {"overview": "Overview", "features": "Features", "actions": "Actions", "progress": "Progress Report", "activity": "Activity", "workflow": "Workflow"}, "overview": {"title": "Action Plan Overview", "details": "Details", "timeline": "Timeline", "description": "Description", "category": "Category", "nature": "Nature", "origin": "Origin", "organizational_level": "Organizational Level", "approver": "Approver", "assignee": "Assignee", "means": "Means", "planned_begin_date": "Planned Begin Date", "planned_end_date": "Planned End Date", "duration": "Duration", "days": "days", "not_set": "Not set", "not_specified": "Not specified", "not_assigned": "Not assigned", "not_available": "Not available", "no_description": "No description provided."}, "actions": {"title": "Actions", "add_action": "Add Action", "no_actions": "No actions found for this action plan.", "create_first": "Create your first action by clicking the button above.", "action_name": "Action Name", "action_name_placeholder": "Enter action name", "status": "Status", "assignee": "Assignee", "due_date": "Due Date", "progress": "Progress", "add_new_action": "Add New Action", "edit_action": "Edit Action", "action_details": "Action Details", "description": "Description", "description_placeholder": "Enter action description", "save_action": "Save Action", "update_action": "Update Action", "delete_action_confirm": "Are you sure you want to delete this action?", "link_actions": "Link Existing Actions", "link_actions_desc": "Select existing actions to link to this action plan.", "link_action": "Link Existing Action", "link_selected": "Link Selected", "search_actions": "Search actions...", "no_actions_to_link": "No actions available to link", "add_action_desc": "Add a new action to this action plan.", "update_action_desc": "Update the details of this action."}, "features": {"coming_soon": "Features Coming Soon", "description": "This section will show detailed features and characteristics of this action plan.", "update_success": "Action plan updated successfully", "update_error": "Failed to update action plan", "update_action_plan": "Update Action Plan"}, "activity": {"coming_soon": "Activity Tracking Coming Soon", "description": "This section will show activity logs and history for this action plan."}, "workflow": {"coming_soon": "Workflow Management Coming Soon", "description": "This section will allow you to manage workflows and processes related to this action plan."}, "nature": {"preventive": "Preventive", "corrective": "Corrective"}, "origin": {"audit": "Audit", "compliance": "Compliance", "event": "Event", "risk": "Risk", "rfc": "RFC", "other": "Other"}, "organizational_level": {"local": "Local", "global": "Global"}}, "control_types": {"title": "Control Types Management", "description": "Define and manage control types within your organization.", "search_placeholder": "Search control types...", "back_to_list": "Back to Control Types", "buttons": {"add": "Add Control Type", "create": "Create Control Type", "update": "Update Control Type"}, "columns": {"name": "Name", "code": "Code", "comment": "Comment", "parent": "Parent Control Type"}, "form": {"name": "Name", "name_placeholder": "Enter control type name", "code": "Code", "code_placeholder": "Enter code", "comment": "Comment", "comment_placeholder": "Enter comment", "parent": "Parent Control Type", "parent_placeholder": "Select parent control type", "none": "None"}, "filters": {"parent": "Parent Control Type", "select_parent": "Select parent control type"}, "dialog": {"title": "Add New Control Type", "description": "Fill in the details to create a new control type.", "edit_title": "Edit Control Type", "edit_description": "Update the control type details."}, "overview": {"title": "Control Type Overview", "details": "Details", "not_available": "N/A", "unknown": "Unknown", "no_code": "No code", "no_description": "No description", "created_at": "Created At"}, "features": {"title": "Edit Control Type"}, "items_found": "{{count}} control type(s) found", "confirm": {"delete": "Are you sure you want to delete {{count}} selected control type(s)?"}, "success": {"created": "Control type created successfully", "updated": "Control type updated successfully", "deleted": "Successfully deleted {{count}} control type(s)"}, "error": {"fetch_failed": "Error fetching control types:", "fetch_failed_toast": "Failed to fetch control types", "create_failed": "Error creating control type:", "create_failed_toast": "Failed to create control type", "update_failed": "Error updating control type:", "update_failed_toast": "Failed to update control type", "delete_failed": "Error deleting control types:", "delete_unexpected": "An unexpected error occurred while deleting control types", "delete_parent": "Cannot delete \"{{id}}\": It is referenced by other control types as a parent. You must first delete or reassign all child control types before deleting this parent.", "delete_in_use": "Cannot delete \"{{id}}\": It is being used by one or more controls. You must first update or delete all controls using this control type before deleting it.", "delete_failed_item": "Failed to delete {{id}}: {{error}}", "loading": "Error: {{message}}", "not_found": "Control type not found", "self_parent": "A control type cannot be its own parent", "invalid_parent": "Invalid parent control type. Please select a valid parent or 'None'.", "server_error": "Server error. Please check the console for details."}}, "business_lines": {"title": "Business Lines Management", "description": "Define and manage business lines within your organization.", "search_placeholder": "Search business lines...", "back_to_list": "Back to Business Lines", "buttons": {"add": "Add Business Line", "create": "Create Business Line", "update": "Update Business Line"}, "columns": {"name": "Name", "description": "Description"}, "form": {"name": "Name", "name_placeholder": "Enter business line name", "description": "Description", "description_placeholder": "Enter description"}, "dialog": {"title": "Add New Business Line", "description": "Fill in the details to create a new business line.", "edit_title": "Edit Business Line", "edit_description": "Update the business line details."}, "overview": {"title": "Business Line Overview", "details": "Details", "no_description": "No description provided"}, "features": {"title": "Edit Business Line"}, "confirm": {"delete": "Are you sure you want to delete {{count}} selected business line(s)?"}, "error": {"fetch_failed": "Error fetching business lines:", "create_failed": "Error creating business line:", "update_failed": "Failed to update business line", "delete_failed": "Error deleting business lines:", "loading": "Error: {{message}}", "not_found": "Business line not found"}}, "applications": {"title": "Applications Management", "description": "Define and manage applications used by your organization.", "search_placeholder": "Search applications...", "back_to_list": "Back to Applications", "buttons": {"add": "Add Application", "create": "Create Application"}, "columns": {"name": "Name", "comment": "Comment"}, "form": {"name": "Name", "name_placeholder": "Enter application name", "comment": "Comment", "comment_placeholder": "Enter comment"}, "dialog": {"title": "Add New Application", "description": "Fill in the details to create a new application."}, "overview": {"title": "Application Overview", "details": "Details", "no_comment": "No comment provided"}, "features": {"title": "Edit Application"}, "version": "Version", "vendor": "<PERSON><PERSON><PERSON>", "status": {"active": "Active", "inactive": "Inactive"}, "confirm": {"delete": "Are you sure you want to delete {{count}} selected application(s)?"}, "success": {"created": "Application created successfully", "deleted": "All selected applications deleted successfully"}, "error": {"fetch_failed": "Failed to fetch applications", "create_failed": "Failed to create application", "update_failed": "Failed to update application", "none_selected": "No applications selected", "delete_referenced": "Cannot delete application {{id}}: It is referenced by other records. Please reassign or remove dependent records first.", "delete_multiple_failed": "Failed to delete {{count}} application(s): {{message}}", "delete_process": "An error occurred during the deletion process", "loading": "Error loading application data. Please try again.", "not_found": "No application found with ID: {{id}}"}}, "dashboard": {"title": "Dashboard", "welcome": "Welcome to Vitalis GRC", "summary": "Summary", "recent_activity": "Recent Activity", "statistics": "Statistics", "risks": "Risks", "incidents": "Incidents", "controls": "Controls", "processes": "Processes", "loading": "Loading dashboard data...", "total_risks": "Total Risks", "total_incidents": "Total Incidents", "high_priority": "High Priority", "action_plans": "Action Plans", "active_risk_monitoring": "Active risk monitoring", "incident_management": "Incident management", "critical_attention": "Critical attention needed", "last_update": "Last update {{time}}", "no_updates": "No recent updates", "recent_items": "Recent Risks & Incidents", "no_recent_items": "No recent risks or incidents found.", "showing_items": "Showing {{count}} recent items", "charts": {"incidents_overview": "Incidents Overview", "incidents_over_time": "Number of Incidents Over Time", "incidents_by_type": "Incidents by Incident Type", "losses_over_time": "Losses Over Time", "losses_by_type": "Losses by Incident Type", "net_loss_by_risk": "Net Loss by Risk Type"}}, "risks": {"title": "Risks", "list_title": "Risk List", "create_title": "Create Risk", "edit_title": "Edit Risk", "details": "Risk Details", "no_risks": "No risks found", "management": {"title": "Risk Management", "description": "Identify, assess, and manage risks across your organization. Track risk status and implement mitigation strategies.", "search_placeholder": "Search risks...", "loading": "Loading risks...", "loading_reference_data": "Loading reference data...", "error": "Error: {{message}}", "no_risks": "No risks found", "delete_confirm": "Are you sure you want to delete {{count}} selected risk(s)?", "delete_success": "Successfully deleted {{count}} risk(s)", "delete_error": "An unexpected error occurred during deletion", "delete_failed": "Failed to delete risk {{id}}: {{error}}", "create_dialog": {"title": "Create New Risk", "description": "Fill in the details below to create a new risk. Fields marked with * are required.", "name": "Name *", "code": "Code", "comment": "Comment", "name_placeholder": "Enter risk name", "code_placeholder": "Enter risk code", "comment_placeholder": "Enter comment", "required": "required"}, "validation": {"name_required": "Name is required"}, "buttons": {"add": "Add Risk", "delete": "Delete", "deleting": "Deleting...", "create": "Create Risk", "creating": "Creating...", "cancel": "Cancel"}, "filters": {"impact": "Impact", "control_level": "Control Level", "probability": "Probability", "appetite": "Appetite", "mitigating_action_plan": "Mitigating Action Plan", "business_process": "Business Process", "organizational_process": "Organizational Process", "operation": "Operation", "application": "Application", "entity": "Entity", "risk_type": "Risk Type", "control": "Control", "select_impact": "Select impact", "select_control_level": "Select Control Level", "select_probability": "Select probability", "select_appetite": "Select appetite", "search_action_plan": "Search action plan...", "select_business_process": "Select business process", "select_org_process": "Select org. process", "select_operation": "Select operation", "select_application": "Select application", "select_entity": "Select entity", "select_risk_type": "Select risk type", "select_control": "Select control", "all": "All", "very_low": "Very Low", "low": "Low", "medium": "Medium", "high": "High", "very_high": "Very High", "very_strong": "Very Strong", "strong": "Strong", "weak": "Weak", "very_weak": "Very Weak"}, "evaluation": {"title": "Risk Evaluation", "refresh": "Refresh", "add_evaluation": "Add Evaluation", "current_assessment": "Current Assessment", "impact_label": "Impact", "probability_label": "Probability", "not_assessed": "Not assessed", "last_evaluated": "Last evaluated {{date}} by {{user}}", "history_title": "Evaluation History", "no_history_title": "No evaluation history", "no_history_description": "Add evaluations to track how this risk changes over time", "table": {"date": "Date", "user": "User", "impact": "Impact", "probability": "Probability", "notes": "Notes"}, "impact": {"very_low": "Very Low", "low": "Low", "medium": "Medium", "high": "High", "very_high": "Very High", "unknown": "Unknown"}, "probability": {"very_low": "Very Low", "low": "Low", "medium": "Medium", "high": "High", "very_high": "Very High", "unknown": "Unknown"}, "modal": {"title": "New Risk Evaluation", "description": "Update the risk assessment with current information", "current_user": "Current User", "impact_label": "Impact", "probability_label": "Probability", "select_impact": "Select impact level", "select_probability": "Select probability level", "notes_label": "Notes (Optional)", "notes_placeholder": "Add any relevant information about this evaluation", "validation_required": "Impact and Probability are required", "cancel": "Cancel", "save": "Save Evaluation", "saving": "Saving..."}, "error_loading_data": "Failed to load risk evaluation data", "validation_required": "Please select both Impact and Probability", "risk_not_available": "Risk data not available. Please try again.", "success_added": "Evaluation added successfully", "error_adding": "Failed to add evaluation"}, "mitigation": {"title": "Mitigation", "strategy_title": "Strategy", "residual_risk_label": "Residual Risk", "appetite_label": "Appetite", "select_appetite": "Select appetite level", "treatment_options": "Treatment Options", "save_strategy": "Save Strategy", "saving": "Saving...", "controls_title": "Controls", "unlink": "Unlink", "control_key": "Control Key", "organizational_level": "Organizational Level", "org_level_short": "Org Level", "yes": "Yes", "no": "No", "na": "N/A", "no_control_linked": "No control linked to this risk.", "create_or_link": "Create a new control or link an existing one.", "create": "Create", "link": "Link", "appetite": {"very_low": "Very Low", "low": "Low", "medium": "Medium", "high": "High", "very_high": "Very High"}, "residual_risk": {"na": "N/A", "critical": "Critical", "high": "High", "medium": "Medium", "low": "Low", "very_low": "Very Low"}, "treatment": {"acceptance": "Acceptance", "avoidance": "Avoidance", "insurance": "Insurance", "reduction": "Reduction"}, "create_modal": {"title": "Create New Control", "description": "Fill in the details below to create a new control.", "name_label": "Name *", "name_placeholder": "Enter control name", "control_key_label": "Control Key", "select_control_key": "Select control key", "cancel": "Cancel", "create_control": "Create Control", "creating": "Creating..."}, "link_modal": {"title": "Link Existing Control", "description": "Select a control to link to this risk.", "search_placeholder": "Search controls...", "no_controls_found": "No controls found. Try a different search or create a new control.", "cancel": "Cancel"}, "strategy_updated": "Mitigation strategy updated successfully", "strategy_update_failed": "Failed to update mitigation strategy", "control_created_linked": "Control created and linked successfully", "control_create_failed": "Failed to create control", "control_linked": "Control linked successfully", "control_link_failed": "Failed to link control", "confirm_unlink": "Are you sure you want to unlink this control?", "control_unlinked": "Control unlinked successfully", "control_unlink_failed": "Failed to unlink control"}, "action_plan": {"title": "Risk Action Plan", "create_action_plan": "Create Action Plan", "loading": "Loading action plans...", "created_linked_success": "Action plan created and linked successfully", "create_failed": "Failed to create action plan", "linked_success": "Action plan linked successfully", "link_failed": "Failed to link action plan", "confirm_unlink": "Are you sure you want to unlink this action plan?", "unlinked_success": "Action plan unlinked successfully", "unlink_failed": "Failed to unlink action plan", "nature": "Nature", "no_nature": "No nature specified", "comment": "Comment", "unlink": "Unlink", "no_linked_title": "No Action Plan Linked", "no_linked_description": "Link an existing action plan or create a new one to help mitigate this risk.", "create_modal": {"title": "Create New Action Plan", "description": "Fill in the details below to create a new action plan.", "name_label": "Name *", "name_placeholder": "Enter action plan name", "nature_label": "Nature", "select_nature": "Select nature", "preventive": "Preventive", "corrective": "Corrective", "comment_label": "Comment", "comment_placeholder": "Enter comment", "cancel": "Cancel", "create_action_plan": "Create Action Plan", "creating": "Creating..."}, "link_modal": {"title": "Link Existing Action Plan", "description": "Select an action plan to link to this risk.", "search_placeholder": "Search action plans...", "linking": "Linking action plan...", "no_match": "No action plans match your search", "no_available": "No action plans available"}}, "columns": {"name": "Name", "code": "Code", "impact": "Impact", "control_level": "Control Level", "probability": "Probability", "appetite": "Appetite", "method_of_identification": "Method of Identification", "comment": "Comment", "mitigating_action_plan": "Mitigating Action Plan", "business_process": "Business Process", "organizational_process": "Organizational Process", "operation": "Operation", "application": "Application", "entity": "Entity", "risk_type": "Risk Type", "control": "Control", "workflow": "Workflow"}}, "edit": {"back_to_risks": "Back to Risks", "risk_not_found": "Risk not found", "error_loading": "Failed to load risk data. Please try again.", "error_refresh": "Failed to refresh risk data", "error_tab_change": "Error changing tabs. Please try again.", "permission_error": "You don't have permission to access this page", "unnamed": "Unnamed Risk", "delete_confirm": "Are you sure you want to delete risk \"{{name}}\"?", "delete_success": "Risk deleted successfully", "delete_error": "Failed to delete risk", "delete_permission": "You don't have permission to delete risks", "metadata": {"code": "Code: {{value}}", "code_na": "Code: N/A", "impact": "Impact: {{value}}", "control_level": "Control Level: {{value}}"}, "tabs": {"overview": "Overview", "features": "Features", "evaluation": "Evaluation", "mitigation": "Mitigation", "action_plan": "Action Plan", "reports": "Reports", "activity_feed": "Activity Feed", "workflow": "Workflow"}}, "overview": {"title": "Risk Overview", "key_information": "Key Information", "name": "Name", "code": "Code", "status": "Status", "major_risk": "Major Risk", "contributors": "Contributors", "loading_contributors": "Loading contributors...", "unknown_user": "Unknown User", "method_of_identification": "Method of Identification", "risk_assessment": "Risk Assessment", "impact": "Impact", "probability": "Probability", "control_level": "Control Level (DMR)", "inherent_risk": "Inherent Risk", "residual_risk": "Residual Risk", "mitigation": "Mitigation", "appetite": "Appetite", "treatment_options": "Treatment Options", "acceptance": "Acceptance", "avoidance": "Avoidance", "insurance": "Insurance", "reduction": "Reduction", "no_treatment_options": "No treatment options specified", "comment": "Comment", "related_information": "Related Information", "business_process": "Business Process", "organizational_process": "Organizational Process", "operation": "Operation", "application": "Application", "entity": "Entity", "risk_type": "Risk Type", "control": "Control", "mitigating_action_plan": "Mitigating Action Plan", "attachments": "Attachments", "none": "N/A"}, "features": {"title": "Edit Risk Details", "create_title": "Create Risk", "loading": "Loading risk data...", "basic_information": "Basic Information", "name": "Name", "code": "Code", "method_of_identification": "Method of Identification", "select_method": "Select method", "major_risk": "Major Risk", "risk_assessment": "Risk Assessment", "control_level": "Control Level", "select_control_level": "Select Control Level", "contributors": "Contributors", "save_first": "Save the risk first to add contributors", "related_information": "Related Information", "business_process": "Business Process", "organizational_process": "Organizational Process", "operation": "Operation", "application": "Application", "entity": "Entity", "risk_type": "Risk Type", "control": "Control", "mitigating_action_plan": "Mitigating Action Plan", "select_business_process": "Select business process", "search_business_processes": "Search business processes...", "select_organizational_process": "Select organizational process", "search_organizational_processes": "Search organizational processes...", "select_operation": "Select operation", "search_operations": "Search operations...", "select_application": "Select application", "search_applications": "Search applications...", "select_entity": "Select entity", "search_entities": "Search entities...", "select_risk_type": "Select risk type", "search_risk_types": "Search risk types...", "select_control": "Select control", "search_controls": "Search controls...", "select_action_plan": "Select action plan", "search_action_plans": "Search action plans...", "none": "None", "success": {"updated": "Risk updated successfully"}, "error": {"update": "Failed to update risk"}, "methods": {"survey": "Survey", "incident_database": "Incident Database", "audit_mission": "Audit Mission", "workshop": "Workshop"}, "survey": "Survey", "incident_database": "Incident Database", "audit_mission": "Audit Mission", "workshop": "Workshop", "comment": "Comment", "comment_placeholder": "Add your comments here...", "cancel": "Cancel", "save_changes": "Save Changes", "saving": "Saving..."}, "contributors": {"error_loading": "Failed to load contributors", "error_loading_users": "Failed to load users", "select_user": "Please select a user to assign", "no_risk": "No risk selected", "invalid_user": "Invalid user selected", "error_assign": "Failed to assign contributor", "invalid_contributor": "Invalid contributor", "remove_confirm": "Are you sure you want to remove this contributor?", "error_remove": "Failed to remove contributor", "loading": "Loading contributors...", "no_contributors": "No contributors assigned to this risk.", "unknown_user": "Unknown User", "added_by": "Added by", "no_permission_remove": "You don't have permission to remove contributors", "assign_contributor": "Assign Contributor", "assign_title": "Assign Risk Contributor", "assign_description": "Select a user to assign as a contributor to this risk:", "no_users": "No available users to assign", "cancel": "Cancel", "assign": "Assign", "assigning": "Assigning...", "no_permission": "You need update permission to manage contributors"}, "attachments": {"error_loading": "Failed to load attachments", "error_loading_description": "Please try refreshing the page", "business_documents": "Business Documents", "external_references": "External References", "uploading": "Uploading...", "upload_document": "Upload Document", "upload_reference": "Upload Reference", "drop_files": "Drop files here", "no_business_docs": "No business documents uploaded yet", "drag_drop_hint": "Drag & drop files here or click the upload button", "file_restrictions": "Max file size: 50MB. Allowed file types: PDF, Office documents, images, archives.", "drop_to_upload": "Drop files to upload", "name": "Name", "size": "Size", "date": "Date", "actions": "Actions", "download": "Download", "delete": "Delete"}, "fields": {"name": "Name", "description": "Description", "impact": "Impact", "probability": "Probability", "control_level": "Control Level", "inherent_risk": "Inherent Risk", "residual_risk": "Residual Risk", "risk_owner": "Risk Owner", "risk_type": "Risk Type", "risk_category": "Risk Category", "status": "Status"}, "tabs": {"overview": "Overview", "features": "Features", "evaluation": "Evaluation", "mitigation": "Mitigation", "action_plan": "Action Plan", "reports": "Reports", "activity_feed": "Activity Feed", "workflow": "Workflow"}, "reports": {"title": "Reports", "risk_reports": "Risk Reports", "placeholder": "Risk reports and analytics will be displayed here."}, "activity_feed": {"title": "Activity Feed", "loading": "Loading activities...", "error_loading": "Failed to load activities", "no_activities": "No activities recorded yet.", "created_risk": "created this risk", "updated_risk": "updated the risk", "changed_workflow": "changed the workflow state", "performed_action": "performed an action", "previous": "Previous", "next": "Next", "page_info": "Page {{current}} of {{total}}"}, "workflow": {"loading_workflow_data": "Loading workflow data...", "loading_roles": "Loading...", "unknown_role": "Unknown Role", "updating": "Updating...", "error_label": "Error", "retry": "Retry", "cancel": "Cancel", "error_missing_id": "Risk ID is missing", "error_transitions": "Failed to load workflow transitions", "error_workflow_data": "Failed to load workflow data", "error_unauthorized": "Unauthorized: Please log in again", "error_not_found": "Risk not found or workflow not initialized for this risk", "error_user_missing": "User information is missing. Please log in again.", "error_no_permission": "You do not have permission to perform this action", "status": {"risk_created": "Risk Created", "to_submit": "To Submit", "to_validate": "To Validate", "validated": "Validated", "rejected": "Rejected"}, "tabs": {"activity": "Activity", "participants": "Participants"}, "activity": {"risk_creator": "Risk Creator", "no_activity": "No activity recorded yet.", "step_reached": "step reached", "performed_by": "Performed by", "using_transition": "using transition", "previous": "Previous", "next": "Next", "page_of": "Page {{current}} of {{total}}"}, "participants": {"no_participants": "No participants recorded yet.", "table": {"name": "Name", "role": "Role", "last_action": "Last Action", "date": "Date"}, "creator_badge": "Creator"}, "reject_dialog": {"title": "Confirm Rejection", "message": "Are you sure you want to reject this risk? It will be reset to the first step. Please provide a reason:", "placeholder": "Enter rejection reason...", "cancel": "Cancel", "reject": "Reject"}, "buttons": {"submit": "Submit", "submit_for_validation": "Submit for Validation", "validate_risk": "Validate Risk", "reject": "Reject"}, "permissions": {"no_reject_permission": "You do not have permission to reject risks", "no_validate_permission": "You do not have permission to validate risks", "no_advance_permission": "You do not have permission to advance the workflow"}, "validation": {"rejection_reason_required": "Please enter a rejection reason"}}}, "incidents": {"title": "Incidents", "list_title": "Incident List", "create_title": "Create Incident", "edit_title": "Edit Incident", "details": "Incident Details", "no_incidents": "No incidents found", "fields": {"name": "Name", "description": "Description", "impact": "Impact", "priority": "Priority", "status": "Status", "incident_type": "Incident Type", "incident_owner": "Incident Owner", "detection_date": "Detection Date", "occurrence_date": "Occurrence Date", "declaration_date": "Declaration Date", "declarant_entity": "Declarant Entity", "near_miss": "Near Miss"}, "tabs": {"overview": "Overview", "attachments": "Attachments", "features": "Features", "financial": "Financial Analysis", "action_plan": "Action Plan", "activity": "Activity Feed", "workflow": "Workflow"}, "ai": {"prompt_required": "Please enter a prompt", "failed_to_process": "Failed to process your prompt. Please try again.", "initializing": "Initializing AI Assistant...", "title_section": "Incident", "title_section_ai": "AI Assistant", "use_ai": "Use artificial intelligence to help create and analyze incidents based on your descriptions.", "describe_incident": "Describe the Incident", "describe_incident_placeholder": "Describe the incident in detail. For example: 'There was a system outage in the payment processing system yesterday at 3 PM that lasted for 2 hours...'", "processing": "Processing...", "generate_incident_details": "Generate Incident Details", "ai_analysis": "AI Analysis", "ai_response_will_appear": "AI response will appear here after you submit your description.", "create_incident": "Create Incident", "name": "Name *", "name_placeholder": "Incident name", "declared_by": "Declared By *", "declared_by_placeholder": "Declarant name", "declaration_date": "Declaration Date *", "detection_date": "Detection Date", "occurrence_date": "Occurrence Date", "impact": "Impact", "select_impact_level": "Select impact level", "very_low": "Very Low", "low": "Low", "medium": "Medium", "high": "High", "very_high": "Very High", "priority": "Priority", "select_priority_level": "Select priority level", "nature": "Nature", "nature_placeholder": "Incident nature", "description": "Description", "description_placeholder": "Incident description", "cancel": "Cancel"}, "add": {"failed_to_fetch_entity_data": "Failed to fetch entity data. Please refresh the page.", "missing_fields": "Missing required fields: {{fields}}", "incident_created_successfully": "Incident created successfully", "failed_to_create_incident": "Failed to create incident", "title": "Add New Incident", "description": "Create a new incident record with essential information", "back_to_incidents": "Back to Incidents", "name": "Name *", "incident_name": "Incident name", "entity": "Entity *", "select_entity": "Select entity", "no_entities_found": "No entities found.", "search_entities": "Search entities...", "detection_date": "Detection Date", "occurrence_date": "Occurrence Date", "incident_description": "Incident description", "cancel": "Cancel", "add_incident": "Add Incident"}, "edit": {"no_permission": "You don't have permission to access this tab", "delete_success": "Incident deleted successfully", "delete_error": "Failed to delete incident", "delete_error_details": "Error deleting incident: {0}", "delete_confirm": "Are you sure you want to delete this incident? This action cannot be undone.", "reference_data_error": "Failed to load reference data", "load_data_error": "Failed to load incident data. Please refresh the page or contact support.", "financial_entries_updated": "Financial entries updated successfully", "financial_entries_update_error": "Failed to update financial entries: {0}", "financial_entries_update_error_details": "Incident was updated but there was an error updating financial entries: {0}", "incident_updated": "Incident updated successfully", "incident_update_error": "Failed to update incident", "error_loading_incident": "Error Loading Incident", "back_to_incidents": "Back to Incidents", "incident_overview": "Incident Overview", "impact": "Impact", "priority": "Priority", "key_information": "Key Information", "name": "Name", "status": "Status", "contributors": "Contributors", "loading_contributors": "Loading contributors...", "unknown_user": "Unknown User", "incident_details": "Incident Details", "declared_by": "Declared By", "declaration_date": "Declaration Date", "declarant_entity": "Declarant Entity", "detection_date": "Detection Date", "occurrence_date": "Occurrence Date", "near_miss": "Near Miss", "nature": "Nature", "financial_information": "Financial Information", "currency": "<PERSON><PERSON><PERSON><PERSON>", "gross_loss": "Gross Loss", "recoveries": "Recoveries", "provisions": "Provisions", "references": "References", "action_plan": "Action Plan", "risk": "Risk", "control": "Control", "entity": "Entity", "business_line": "Business Line", "incident_type": "Incident Type", "business_process": "Business Process", "organizational_process": "Organizational Process", "product": "Product", "application": "Application", "description": "Description", "attachments": "Attachments", "no_description": "No description provided", "none": "None"}, "attachments": {"title": "Attachments", "businessDocuments": "Business Documents", "externalReferences": "External References", "uploadDocument": "Upload Document", "uploading": "Uploading...", "dropFilesHere": "Drop files here", "noBusinessDocumentsUploadedYet": "No business documents uploaded yet", "dragDropFiles": "Drag & drop files here or click the upload button", "maxFileSize": "Max file size: 50MB. Allowed file types: PDF, Office documents, images, archives.", "name": "Name", "size": "Size", "date": "Date", "actions": "Actions", "download": "Download", "delete": "Delete", "addReference": "Add Reference", "noExternalReferencesAddedYet": "No external references added yet", "clickAddReference": "Click the 'Add Reference' button to add a link", "description": "Description", "addExternalReference": "Add External Reference", "addLink": "Add a link to an external resource related to this incident.", "url": "URL", "brieflyDescribe": "Briefly describe this reference", "cancel": "Cancel", "failedToLoadAttachments": "Failed to load attachments", "filesTooLarge": "Files too large", "unsupportedFileTypes": "Unsupported file types", "uploadSuccessful": "Upload successful", "uploadTimedOut": "Upload timed out", "fileTooLarge": "File too large", "unsupportedFileType": "Unsupported file type", "uploadFailed": "Upload failed", "urlRequired": "URL is required", "referenceAddedSuccessfully": "Reference added successfully", "failedToAddReference": "Failed to add reference", "downloadFailed": "Download failed", "confirmDeleteDocument": "Are you sure you want to delete this document?", "documentDeleted": "Document deleted", "deleteFailed": "Delete failed", "confirmDeleteReference": "Are you sure you want to delete this reference?", "referenceDeleted": "Reference deleted"}, "action_plan": {"title": "Action Plan", "action_plan": "Action Plan", "create_action_plan": "Create Action Plan", "create_new_action_plan": "Create New Action Plan", "fill_in_the_details_below_to_create_a_new_action_plan": "Fill in the details below to create a new action plan.", "name": "Name", "enter_action_plan_name": "Enter action plan name", "nature": "Nature", "select_nature": "Select nature", "preventive": "Preventive", "corrective": "Corrective", "comment": "Comment", "enter_comment": "Enter comment", "cancel": "Cancel", "creating": "Creating...", "link_existing": "Link Existing", "link_existing_action_plan": "Link Existing Action Plan", "select_an_action_plan_to_link_to_this_incident": "Select an action plan to link to this incident.", "search_action_plans": "Search action plans...", "linking_action_plan": "Linking action plan...", "no_action_plans_match_your_search": "No action plans match your search", "no_action_plans_available": "No action plans available", "no_nature_specified": "No nature specified", "unlink": "Unlink", "no_action_plan_linked": "No Action Plan Linked", "link_an_existing_action_plan_or_create_a_new_one_to_help_manage_this_incident": "Link an existing action plan or create a new one to help manage this incident.", "created_and_linked_successfully": "Action plan created and linked successfully", "linked_successfully": "Action plan linked successfully", "unlinked_successfully": "Action plan unlinked successfully", "failed_to_create_action_plan": "Failed to create action plan", "failed_to_link_action_plan": "Failed to link action plan", "failed_to_unlink_action_plan": "Failed to unlink action plan", "are_you_sure_you_want_to_unlink_this_action_plan": "Are you sure you want to unlink this action plan?", "loading_action_plans": "Loading action plans..."}, "activity_feed": {"title": "Activity Feed", "loading_activities": "Loading activities...", "no_activities": "No activities found", "activity_details": "Activity Details", "date": "Date", "user": "User", "action": "Action", "details": "Details", "previous": "Previous", "next": "Next", "created_this_incident": "created this incident", "updated_the_incident": "updated the incident", "changed_the_workflow_state": "changed the workflow state", "performed_an_action": "performed an action", "activity_feed": "Activity Feed", "no_activities_recorded_yet": "No activities recorded yet.", "page": "Page", "of": "of", "date_changed_from_to": "{{field}} changed from {{oldValue}} to {{newValue}}", "removed_contributor": "Removed user with ID {{userId}} as contributor from incident {{incidentId}}", "added_contributor": "Added user with ID {{userId}} as contributor to incident {{incidentId}}", "updated_contributor_role": "Updated role for user with ID {{userId}} to {{role}} in incident {{incidentId}}", "fields": {"occurrence_date": "Occurrence date", "detection_date": "Detection date", "declaration_date": "Declaration date", "name": "Name", "description": "Description", "impact": "Impact", "priority": "Priority", "nature": "Nature", "near_miss": "Near miss"}}, "characteristics": {"characteristics": "Characteristics", "basicInformation": "Basic Information", "name": "Name", "incidentName": "Incident name", "declaredBy": "Declared By", "declarantName": "Declarant name", "declarantEntity": "Declarant entity", "declarationDate": "Declaration Date", "detectionDate": "Detection Date", "occurrenceDate": "Occurrence Date", "contributors": "Contributors", "incidentEvaluation": "Incident Evaluation", "nearMiss": "Near Miss", "selectNearMiss": "Select near miss", "nature": "Nature", "selectNature": "Select nature", "impact": "Impact", "selectImpact": "Select impact", "priority": "Priority", "selectPriority": "Select priority", "referenceData": "Reference Data", "entity": "Entity", "selectEntity": "Select entity", "businessLine": "Business Line", "selectBusinessLine": "Select business line", "noBusinessLinesFound": "No business lines found.", "searchBusinessLines": "Search business lines...", "businessProcess": "Business Process", "selectBusinessProcess": "Select business process", "noBusinessProcessesFound": "No business processes found.", "searchBusinessProcesses": "Search business processes...", "organizationalProcess": "Organizational Process", "selectOrganizationalProcess": "Select organizational process", "noOrganizationalProcessesFound": "No organizational processes found.", "searchOrganizationalProcesses": "Search organizational processes...", "product": "Product", "selectProduct": "Select product", "noProductsFound": "No products found.", "searchProducts": "Search products...", "application": "Application", "selectApplication": "Select application", "noApplicationsFound": "No applications found.", "searchApplications": "Search applications...", "description": "Description", "incidentDescription": "Incident description", "qualitativeAnalysis": "Qualitative Analysis", "assignedRisk": "Assigned Risk", "noRiskAssigned": "No risk assigned", "addNewRisk": "Add new risk", "linkToExistingRisk": "Link to existing risk", "control": "Control", "noControlAssigned": "No control assigned", "addNewControl": "Add new control", "linkToExistingControl": "Link to existing control", "incidentType": "Incident Type", "noIncidentTypeAssigned": "No incident type assigned", "addNewIncidentType": "Add new incident type", "linkToExistingIncidentType": "Link to existing incident type", "riskNameRequired": "Risk name is required", "riskCreated": "Risk created and assigned successfully", "failedToCreateRisk": "Failed to create risk", "riskAssigned": "Risk assigned successfully", "invalidRiskSelected": "Invalid risk selected", "failedToAssignRisk": "Failed to assign risk", "loadingRisks": "Loading risks...", "unnamedRisk": "Unnamed Risk", "riskID": "Risk ID: {riskID}", "controlNameRequired": "Control name is required", "controlCreated": "Control created and assigned successfully", "failedToCreateControl": "Failed to create control", "controlAssigned": "Control assigned successfully", "invalidControlSelected": "Invalid control selected", "failedToAssignControl": "Failed to assign control", "loadingControls": "Loading controls...", "unnamedControl": "Unnamed Control", "controlID": "Control ID: {controlID}", "incidentTypeNameRequired": "Incident type name is required", "incidentTypeCreated": "Incident type created and assigned successfully", "failedToCreateIncidentType": "Failed to create incident type", "incidentTypeAssigned": "Incident type assigned successfully", "invalidIncidentTypeSelected": "Invalid incident type selected", "failedToAssignIncidentType": "Failed to assign incident type", "loadingIncidentTypes": "Loading incident types...", "unnamedIncidentType": "Unnamed Incident Type", "incidentTypeID": "Incident Type ID: {incidentTypeID}"}, "financial": {"title": "Financial Analysis", "summary": "Financial Summary", "currency": "<PERSON><PERSON><PERSON><PERSON>", "grossLoss": "Gross Loss", "recoveries": "Recoveries", "provisions": "Provisions", "analysisTitle": "Financial Analysis", "newEntry": "New Entry", "addNewEntry": "Add New Entry", "name": "Name", "amount": "Amount", "localAmount": "Local Amount", "entryNamePlaceholder": "Enter entry name", "amountPlaceholder": "Enter amount", "localAmountPlaceholder": "Enter local amount", "cancel": "Cancel", "add": "Add", "noEntriesFound": "No entries found", "total": "Total", "actions": "Actions", "loading": "Loading financial data...", "error": "Error loading financial data", "save_success": "Financial data saved successfully", "save_error": "Failed to save financial data"}, "workflow": {"title": "Workflow", "loading_workflow_data": "Loading workflow data...", "error_loading_workflow": "Error loading workflow data", "current_state": "Current State", "next_actions": "Next Actions", "history": "History", "date": "Date", "user": "User", "action": "Action", "comment": "Comment", "no_history": "No history available", "no_actions": "No actions available", "confirm_action": "Confirm Action", "reject": "Reject", "approve": "Approve", "cancel": "Cancel", "submit": "Submit", "action_success": "Action completed successfully", "action_error": "Error completing action", "comment_required": "Comment is required", "confirm_reject": "Are you sure you want to reject this incident?", "confirm_approve": "Are you sure you want to approve this incident?", "incident_id_missing": "Incident ID is missing", "incident_state": "Incident State", "failed_to_load_workflow_transitions": "Failed to load workflow transitions", "error_loading_workflow_transitions": "Error loading workflow transitions", "failed_to_load_workflow_data": "Failed to load workflow data", "unknown_error_occurred": "Unknown error occurred", "workflow_transitioned_successfully": "Workflow transitioned successfully!", "failed_to_transition_workflow": "Failed to transition workflow", "permission_denied": "Permission denied:", "role_configuration_issue": " This may be due to a role configuration issue.", "updating": "Updating...", "processing_transition": "Processing transition...", "you_dont_have_permission_to_perform_this_action": "You don't have permission to perform this action", "incident_created": "Incident Created", "validated": "Validated [Incident Declaration]", "rejected": "Rejected", "rejected_ready_to_restart": "Rejected (Ready to Restart)", "to_submit": "To Submit", "pending_approval": "Pending Approval", "to_validate": "To Validate", "error": "Error:", "retry": "Retry", "reject_incident": "Reject Incident", "please_provide_reason_for_rejection": "Please provide a reason for rejection:", "rejection_reason_required": "Rejection reason (required)", "rejecting": "Rejecting...", "incident_creator": "Incident Creator:", "no_activity_recorded_yet": "No activity recorded yet.", "performed_by": "Performed by", "using_transition": "using transition", "previous": "Previous", "page": "Page", "of": "of", "next": "Next", "no_participants_recorded_yet": "No participants recorded yet.", "name": "Name", "role": "Role", "last_action": "Last Action", "user_information_missing": "User information is missing", "states": {"start": "Start", "to_submit": "To Submit", "to_approve": "To Approve", "to_validate": "To Validate", "rejected": "Rejected", "validated": "Validated", "closed": "Closed"}, "buttons": {"approve": "Approve", "reject": "Reject", "activity": "Activity", "participants": "Participants", "submit": "Submit", "send_for_approval": "Send for A<PERSON>roval", "send_for_validation": "Send for Validation", "validate_incident": "Validate Incident", "close_incident": "Close Incident"}}, "incident_activity_feed": {"created_this_incident": "created this incident", "updated_the_incident": "updated the incident", "changed_the_workflow_state": "changed the workflow state", "performed_an_action": "performed an action", "activity_feed": "Activity Feed", "loading_activities": "Loading activities...", "no_activities_recorded_yet": "No activities recorded yet.", "previous": "Previous", "page": "Page", "of": "of", "next": "Next", "date_changed_from_to": "{{field}} changed from {{oldValue}} to {{newValue}}", "removed_contributor": "Removed user with ID {{userId}} as contributor from incident {{incidentId}}", "added_contributor": "Added user with ID {{userId}} as contributor to incident {{incidentId}}", "updated_contributor_role": "Updated role for user with ID {{userId}} to {{role}} in incident {{incidentId}}", "occurrence_date_changed": "Occurrence date changed from {{oldValue}} to {{newValue}}", "detection_date_changed": "Detection date changed from {{oldValue}} to {{newValue}}", "declaration_date_changed": "Declaration date changed from {{oldValue}} to {{newValue}}", "name_changed": "Name changed from {{oldValue}} to {{newValue}}", "description_changed": "Description changed from {{oldValue}} to {{newValue}}", "impact_changed": "Impact changed from {{oldValue}} to {{newValue}}", "priority_changed": "Priority changed from {{oldValue}} to {{newValue}}", "nature_changed": "Nature changed from {{oldValue}} to {{newValue}}", "near_miss_changed": "Near miss changed from {{oldValue}} to {{newValue}}", "fields": {"occurrence_date": "Occurrence date", "detection_date": "Detection date", "declaration_date": "Declaration date", "name": "Name", "description": "Description", "impact": "Impact", "priority": "Priority", "nature": "Nature", "near_miss": "Near miss"}}, "contributors": {"title": "Contributors", "loading": "Loading contributors...", "no_contributors": "No contributors assigned", "add_contributor": "Add Contributor", "assign_contributor": "Assign Contributor", "select_contributor": "Select contributor", "search_contributors": "Search contributors...", "no_contributors_found": "No contributors found", "added_by": "Added by", "added_on": "Added on", "role": "Role", "select_role": "Select role", "remove_contributor": "Remove Contributor", "confirm_remove": "Are you sure you want to remove this contributor?", "contributor_added": "Contributor added successfully", "contributor_removed": "Contributor removed successfully", "failed_to_add": "Failed to add contributor", "failed_to_remove": "Failed to remove contributor", "roles": {"owner": "Owner", "manager": "Manager", "analyst": "Analyst", "reviewer": "Reviewer", "observer": "Observer"}, "assign_incident_contributor": "Assign Incident Contributor", "select_user": "Select User", "search_users": "Search users...", "no_users_found": "No users found", "cancel": "Cancel", "assign": "Assign", "assigning": "Assigning...", "modal": {"title": "Assign Contributor", "description": "Select a user to assign as a contributor to this incident", "user_required": "Please select a user", "role_required": "Please select a role"}}, "new_risk": {"title": "Add New Risk", "name": "Risk Name", "impact": "Impact", "dmr": "DMR", "probability": "Probability", "comment": "Comment", "comment_placeholder": "Add any additional comments about this risk", "cancel": "Cancel", "add_risk": "Add Risk"}, "risk_selection": {"select_risk": "Select Risk", "select_a_risk_to_associate_with_this_incident": "Select a risk to associate with this incident", "search_risks": "Search risks...", "impact": "Impact", "dmr": "DMR", "probability": "Probability", "cancel": "Cancel", "no_risks_found_matching_your_search_criteria": "No risks found matching your search criteria"}, "new_control": {"title": "Add New Control", "name": "Control Name", "code": "Code", "code_placeholder": "Control code (optional)", "comment": "Comment", "comment_placeholder": "Add any additional comments about this control", "cancel": "Cancel", "add_control": "Add Control"}, "control_selection": {"select_control": "Select Control", "select_a_control_to_associate_with_this_incident": "Select a control to associate with this incident", "search_controls": "Search controls...", "code": "Code", "cancel": "Cancel", "no_description": "No description available", "no_controls_found_matching_your_search_criteria": "No controls found matching your search criteria"}, "new_incident_type": {"title": "Add New Incident Type", "name": "Incident Type Name", "code": "Code", "code_placeholder": "Incident type code (optional)", "comment": "Comment", "comment_placeholder": "Add any additional comments about this incident type", "cancel": "Cancel", "add_incident_type": "Add Incident Type"}, "incident_type_selection": {"select_incident_type": "Select Incident Type", "select_an_incident_type_to_associate_with_this_incident": "Select an incident type to associate with this incident", "search_incident_types": "Search incident types...", "code": "Code", "cancel": "Cancel", "no_description": "No description available", "no_incident_types_found_matching_your_search_criteria": "No incident types found matching your search criteria"}}, "controls": {"features": {"title": "Edit Control", "create_title": "Create Control", "id": "ID", "sections": {"basic": "Basic Information", "details": "Control Details", "testing": "Testing Details", "reference": "Reference Data", "descriptions": "Descriptions"}, "form": {"objective": "Objective", "execution_procedure": "Execution Procedure", "comment": "Comment", "name": "Name", "code": "Code", "control_key": "Control Key", "control_execution_method": "Control Execution Method", "operational_cost": "Operational Cost", "organizational_level": "Organizational Level", "sample_type": "Sample Type", "testing_frequency": "Testing Frequency", "testing_method": "Testing Method", "testing_population_size": "Testing Population Size", "testing_procedure": "Testing Procedure", "select_control_type": "Select control type", "search_control_types": "Search control types...", "select_business_process": "Select business process", "search_business_processes": "Search business processes...", "select_organizational_process": "Select organizational process", "search_organizational_processes": "Search organizational processes...", "select_operation": "Select operation", "search_operations": "Search operations...", "select_application": "Select application", "search_applications": "Search applications...", "select_entity": "Select entity", "search_entities": "Search entities...", "select_risk": "Select risk", "search_risks": "Search risks..."}, "buttons": {"save": "Save Changes", "create": "Create Control"}, "success": {"created": "Control created successfully", "updated": "Control updated successfully"}, "error": {"name_required": "Name is required", "update": "Error in control update:", "foreign_key": "The {{fieldName}} with ID {{fieldValue}} does not exist in the database.", "foreign_key_constraint": "Foreign key constraint error: One of the selected references does not exist.", "retry": "Error in retry:", "retry_failed": "Failed to save control even after retry", "save_failed": "Failed to save control", "reference_data": "Error loading reference data:", "reference_data_refresh": "Failed to load some reference data. Please refresh the page."}}, "overview": {"title": "Control Overview", "basic_information": "Basic Information", "testing_details": "Testing Details", "reference_data": "Reference Data", "descriptions": "Descriptions", "name": "Name", "code": "Code", "control_key": "Control Key", "operational_cost": "Operational Cost", "organizational_level": "Organizational Level", "control_execution_method": "Control Execution Method", "sample_type": "Sample Type", "testing_frequency": "Testing Frequency", "testing_method": "Testing Method", "testing_population_size": "Testing Population Size", "testing_procedure": "Testing Procedure", "control_type": "Control Type", "business_process": "Business Process", "organizational_process": "Organizational Process", "operation": "Operation", "application": "Application", "entity": "Entity", "associated_risk": "Associated Risk", "action_plan": "Action Plan", "objective": "Objective", "execution_procedure": "Execution Procedure", "comment": "Comment"}, "evaluation": {"title": "Control Evaluation", "assessment": "Control Assessment", "permission_error": "You don't have permission to update this control", "success": {"updated": "Control evaluation updated successfully"}, "error": {"update": "Failed to update control evaluation"}, "design_quality": {"question": "What is the quality level of control design?", "satisfactory": "Satisfactory", "unsatisfactory": "Unsatisfactory"}, "effectiveness": {"question": "What is the effectiveness level of the control?", "satisfactory": "Satisfactory", "unsatisfactory": "Unsatisfactory", "auto_set": "Effectiveness is automatically set to Unsatisfactory when Design Quality is Unsatisfactory"}}, "action_plan": {"title": "Control Action Plan", "loading": "Loading action plans...", "linking": "Linking action plan...", "no_linked": "No Action Plan Linked", "no_linked_description": "Link an existing action plan or create a new one to help implement this control.", "no_nature": "No nature specified", "no_plans": "No action plans available", "no_search_results": "No action plans match your search", "id": "ID", "search_placeholder": "Search action plans...", "buttons": {"create": "Create Action Plan", "link": "Link Action Plan", "unlink": "Unlink"}, "form": {"name": "Name", "name_placeholder": "Enter action plan name", "nature": "Nature", "nature_placeholder": "Select nature", "preventive": "Preventive", "corrective": "Corrective", "comment": "Comment", "comment_placeholder": "Enter comment"}, "dialog": {"create_title": "Create New Action Plan", "create_description": "Fill in the details below to create a new action plan.", "link_title": "Link Existing Action Plan", "link_description": "Select an action plan to link to this control."}, "success": {"created_linked": "Action plan created and linked successfully", "linked": "Action plan linked successfully", "unlinked": "Action plan unlinked successfully"}, "error": {"create": "Error creating action plan:", "create_failed": "Failed to create action plan", "link": "Error linking action plan:", "link_failed": "Failed to link action plan", "unlink": "Error unlinking action plan:", "unlink_failed": "Failed to unlink action plan", "control_missing": "Control not available or missing controlID", "control_refresh": "Control not available. Please try refreshing the page.", "data_not_available": "Control data not available", "data_refresh": "The control data is not properly loaded. Please try refreshing the page."}, "confirm": {"unlink": "Are you sure you want to unlink this action plan?"}}, "title": "Controls", "list_title": "Control List", "create_title": "Create Control", "edit_title": "Edit Control", "details": "Control Details", "no_controls": "No controls found", "management": {"title": "Controls Management", "description": "Define and manage controls within your organization.", "search_placeholder": "Search controls...", "loading": "Loading controls...", "error": "Error: {{message}}", "no_controls": "No controls found", "delete_confirm": "Are you sure you want to delete {{count}} selected control(s)?", "delete_success": "Selected controls deleted successfully", "delete_error": "An error occurred during the deletion process", "delete_failed": "Failed to delete control {{id}}: {{error}}", "create_dialog": {"title": "Create New Control", "name": "Name", "code": "Code", "control_key": "Control Key", "execution_method": "Execution Method", "name_placeholder": "Enter control name", "code_placeholder": "Enter control code", "control_key_placeholder": "Select control key", "execution_method_placeholder": "Select execution method", "required": "required"}, "validation": {"name_required": "Name is required", "control_key_required": "Control Key is required", "execution_method_required": "Execution Method is required", "control_key_integer": "Control Key must be 0 or 1."}, "buttons": {"add": "Add Control", "delete": "Delete", "deleting": "Deleting...", "create": "Create Control", "creating": "Creating...", "cancel": "Cancel"}, "filters": {"testing_frequency": "Testing Frequency", "sample_type": "Sample Type", "testing_method": "Testing Method", "select_frequency": "Select frequency", "select_type": "Select type", "select_method": "Select method"}, "columns": {"name": "Name", "code": "Code", "control_key": "Control Key", "testing_frequency": "Testing Frequency", "sample_type": "Sample Type", "testing_method": "Testing Method"}}, "edit": {"back_to_controls": "Back to Controls", "control_not_found": "Control not found", "error_loading": "Failed to load control data. Please try again.", "error_refresh": "Failed to refresh control data", "error_tab_change": "Error changing tabs. Please try again.", "permission_error": "You don't have permission to access this page", "unnamed": "Unnamed Control", "metadata": {"code": "Code: {{value}}", "code_na": "Code: N/A", "control_key": "Control Key: {{value}}", "organizational_level": "Organizational Level: {{value}}", "organizational_level_na": "Organizational Level: N/A"}, "tabs": {"overview": "Overview", "features": "Features", "action_plan": "Action Plan", "evaluation": "Evaluation"}}, "fields": {"name": "Name", "description": "Description", "type": "Type", "frequency": "Frequency", "effectiveness": "Effectiveness", "control_owner": "Control Owner", "implementation_date": "Implementation Date", "last_test_date": "Last Test Date", "next_test_date": "Next Test Date"}}, "processes": {"title": "Processes", "business_processes": "Business Processes", "organizational_processes": "Organizational Processes", "operations": "Operations", "tree_view": "Tree View"}, "entities": {"title": "Entities", "description": "Manage your entities here", "back": "Back to Entities", "not_found": "Entity not found", "error": "Error", "loading": "Loading entities...", "confirm_delete": "Are you sure you want to delete {{count}} selected entity(s)?", "success": {"delete": "All selected entities deleted successfully"}, "errors": {"no_selection": "No entities selected", "cannot_delete_parent": "Cannot delete \"{{name}}\" (ID: {{id}}) because it is a parent of: {{dependents}}.", "unknown": "Unknown error", "referenced": "Cannot delete entity {{id}}: It is referenced by other records. Please reassign or remove dependent records first.", "delete_failed": "Failed to delete {{count}} entity(s): {{error}}", "delete_failed_generic": "Failed to delete entities", "refresh_failed": "Failed to refresh entity data"}, "badges": {"external": "External", "internal": "Internal"}, "metadata": {"type": "Type", "no_type": "No type", "no_comment": "No comment"}, "tabs": {"overview": "Overview", "features": "Features"}, "features": {"title": "Edit En<PERSON>", "form": {"name": "Name", "name_placeholder": "Enter entity name", "code": "Code", "code_placeholder": "Enter entity code", "type": "Type", "type_placeholder": "Select entity type", "branch": "Branch", "subsidiary": "Subsidiary", "head_office": "Head Office", "internal_external": "Internal/External", "internal_external_placeholder": "Select internal/external", "internal": "Internal", "external": "External", "local_currency": "Local Currency", "local_currency_placeholder": "e.g., USD, EUR", "parent_entity": "Parent Entity", "parent_entity_placeholder": "Select parent entity", "comment": "Comment", "comment_placeholder": "Enter comment", "none": "None"}, "buttons": {"saving": "Saving...", "save": "Save Changes"}}, "overview": {"title": "Entity Details", "fields": {"name": "Name", "code": "Code", "type": "Type", "internal_external": "Internal/External", "internal": "Internal", "external": "External", "local_currency": "Local Currency", "parent_entity": "Parent Entity", "comment": "Comment", "created_at": "Created At", "none": "N/A"}}, "buttons": {"add": "Add Entity", "delete": "Delete", "deleting": "Deleting...", "create": "Create Entity", "creating": "Creating...", "update": "Update Entity", "updating": "Updating..."}, "dialog": {"create_title": "Create New Entity", "create_description": "Fill in the details below to create a new entity. Fields marked with * are required.", "update_title": "Update Entity", "update_description": "Modify the fields below to update the entity details."}}}, "audit": {"sidebar": {"home": "Home", "dashboard": "Dashboard", "audit": "Audit", "plans_daudit": "Audit Plans", "missions_audits": "Audit Missions", "incidents": "Incidents", "incidents_list": "Incidents List", "create_incident": "Create Incident", "risks": "Risks", "risks_list": "Risks List", "controls": "Controls", "processes": "Processes", "tree_view": "Tree View", "business_processes": "Business Processes", "organizational_processes": "Org. Processes", "operations": "Operations", "reports": "Reports"}, "breadcrumb": {"audit": "Audit", "plans_daudit": "Audit Plans", "mission_daudit": "Audit Mission"}, "page_titles": {"edit_mission_audit": "Edit Audit Mission", "plans_daudit": "Audit Plans", "create_plan_daudit": "Create Audit Plan", "edit_plan_daudit": "Edit Audit Plan"}, "buttons": {"back": "Back", "save": "Save", "cancel": "Cancel", "add": "Add", "edit": "Edit", "delete": "Delete", "create": "Create", "update": "Update"}, "tabs": {"mission_details": "Mission Details", "caracteristiques": "Characteristics", "perimetre": "<PERSON><PERSON>", "evaluation_risques": "Risk Assessment", "activites": "Activities", "recommandations": "Recommendations", "documents": "Documents", "depenses": "Expenses", "rapport": "Report", "workflow": "Workflow", "overview": "Overview", "analyse_financiere": "Financial Analysis", "plan_daction": "Action Plan", "fil_dactivite": "Activity Feed"}, "fields": {"name": "Name", "code": "Code", "status": "Status", "department": "Department", "start_date": "Start Date", "end_date": "End Date", "duration": "Duration", "description": "Description", "objectives": "Objectives", "scope": "<PERSON><PERSON>", "resources": "Resources", "progress": "Progress", "findings": "Findings", "recommendations": "Recommendations"}, "status": {"in_progress": "In Progress", "completed": "Completed", "planned": "Planned", "delayed": "Delayed", "cancelled": "Cancelled"}}, "risks.management.evaluation": {"title": "Risk Evaluation", "refresh": "Refresh", "add_evaluation": "Add Evaluation", "current_assessment": "Current Assessment", "impact_label": "Impact", "probability_label": "Probability", "impact": {"low": "Low", "medium": "Medium", "high": "High"}, "probability": {"low": "Low", "medium": "Medium", "high": "High"}, "last_evaluated": "Last evaluated {{date}} by {{user}}"}}