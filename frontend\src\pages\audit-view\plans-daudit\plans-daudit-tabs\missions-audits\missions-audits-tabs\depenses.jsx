import React, { useState, useContext } from 'react';

import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Plus, DollarSign, Save, Edit, Trash2, Calendar, ArrowUpDown } from "lucide-react";
import { toast } from "sonner";
import { useMissionAuditContext } from '@/utils/context-helpers';
import { OutletContext } from '@/pages/audit-view/missions-audits/edit-missions-audits';

function DepensesTab(props) {
  const contextData = useMissionAuditContext();
  const missionAudit = props.missionAudit || contextData.missionAudit;
  const [expenses, setExpenses] = useState([]);
  
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingExpense, setEditingExpense] = useState(null);
  const [sortConfig, setSortConfig] = useState({ key: 'date', direction: 'desc' });
  
  const [newExpense, setNewExpense] = useState({
    description: "",
    category: "Services",
    amount: "",
    date: new Date().toISOString().split('T')[0],
    vendor: "",
    paymentMethod: "Virement",
    notes: "",
    status: "Pending"
  });

  // Budget limits
  const [budget, setBudget] = useState({
    total: 30000,
    remaining: 30000
  });

  // If no mission data is available yet
  if (!missionAudit || !missionAudit.id) {
    return (
      <div className="flex items-center justify-center h-48">
        <p className="text-gray-500">Chargement des dépenses de la mission...</p>
      </div>
    );
  }

  const handleSave = () => {
    toast.success("Dépenses sauvegardées avec succès");
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewExpense(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name, value) => {
    setNewExpense(prev => ({ ...prev, [name]: value }));
  };

  const handleAddExpense = () => {
    if (!newExpense.description || !newExpense.amount || !newExpense.date) {
      toast.error("Veuillez remplir au moins la description, le montant et la date");
      return;
    }

    const expense = {
      ...newExpense,
      amount: parseFloat(newExpense.amount),
      id: editingExpense ? editingExpense.id : Date.now()
    };

    if (editingExpense) {
      // Update existing expense
      setExpenses(prev => prev.map(exp => 
        exp.id === editingExpense.id ? expense : exp
      ));
      toast.success("Dépense mise à jour avec succès");
    } else {
      // Add new expense
      setExpenses(prev => [...prev, expense]);
      toast.success("Nouvelle dépense ajoutée avec succès");
      
      // Update budget
      const newRemainingBudget = budget.remaining - parseFloat(newExpense.amount);
      setBudget(prev => ({
        ...prev,
        remaining: newRemainingBudget
      }));
    }

    // Reset form and close dialog
    setNewExpense({
      description: "",
      category: "Services",
      amount: "",
      date: new Date().toISOString().split('T')[0],
      vendor: "",
      paymentMethod: "Virement",
      notes: "",
      status: "Pending"
    });
    setEditingExpense(null);
    setIsDialogOpen(false);
  };

  const handleEditExpense = (expense) => {
    setEditingExpense(expense);
    setNewExpense({
      ...expense,
      amount: expense.amount.toString()
    });
    setIsDialogOpen(true);
  };

  const handleDeleteExpense = (id) => {
    const expenseToDelete = expenses.find(exp => exp.id === id);
    if (expenseToDelete) {
      // Update budget
      const newRemainingBudget = budget.remaining + expenseToDelete.amount;
      setBudget(prev => ({
        ...prev,
        remaining: newRemainingBudget
      }));
      
      // Remove expense
      setExpenses(prev => prev.filter(exp => exp.id !== id));
      toast.success("Dépense supprimée avec succès");
    }
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case 'Approved':
        return <Badge className="bg-green-100 text-green-800">Approuvée</Badge>;
      case 'Pending':
        return <Badge className="bg-yellow-100 text-yellow-800">En attente</Badge>;
      case 'Rejected':
        return <Badge className="bg-red-100 text-red-800">Rejetée</Badge>;
      case 'Paid':
        return <Badge className="bg-blue-100 text-blue-800">Payée</Badge>;
      default:
        return <Badge variant="outline">-</Badge>;
    }
  };

  // Calculate expense statistics
  const getTotalExpenses = () => {
    return expenses.reduce((sum, exp) => sum + exp.amount, 0);
  };

  // Sort expenses
  const handleSort = (key) => {
    setSortConfig(prevConfig => ({
      key,
      direction: prevConfig.key === key && prevConfig.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const sortedExpenses = [...expenses].sort((a, b) => {
    if (sortConfig.key === 'amount') {
      return sortConfig.direction === 'asc' ? a.amount - b.amount : b.amount - a.amount;
    } else if (sortConfig.key === 'date') {
      return sortConfig.direction === 'asc' 
        ? new Date(a.date) - new Date(b.date)
        : new Date(b.date) - new Date(a.date);
    }
    return 0;
  });

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(amount);
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return "-";
    return new Date(dateString).toLocaleDateString('fr-FR');
  };

  // Calculate budget usage percentage
  const budgetUsagePercentage = Math.round((getTotalExpenses() / budget.total) * 100);

  return (
    <div className="space-y-6 py-4">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-[#1A202C] flex items-center">
          <DollarSign className="h-6 w-6 mr-3 text-[#F62D51]" />
          Dépenses
        </h2>
        <div className="flex space-x-2">
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button className="bg-[#F62D51] hover:bg-[#F62D51]/90">
                <Plus className="h-4 w-4 mr-2" />
                Ajouter une Dépense
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>{editingExpense ? "Modifier la Dépense" : "Ajouter une Nouvelle Dépense"}</DialogTitle>
                <DialogDescription>
                  Complétez les informations ci-dessous pour {editingExpense ? "modifier" : "ajouter"} une dépense.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="description">Description *</Label>
                  <Input 
                    id="description" 
                    name="description"
                    value={newExpense.description}
                    onChange={handleInputChange}
                    placeholder="Description de la dépense"
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="amount">Montant (€) *</Label>
                    <Input 
                      id="amount" 
                      name="amount"
                      type="number"
                      step="0.01"
                      value={newExpense.amount}
                      onChange={handleInputChange}
                      placeholder="0.00"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="date">Date *</Label>
                    <Input 
                      id="date" 
                      name="date"
                      type="date"
                      value={newExpense.date}
                      onChange={handleInputChange}
                    />
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="category">Catégorie</Label>
                    <Select 
                      name="category"
                      value={newExpense.category} 
                      onValueChange={(value) => handleSelectChange("category", value)}
                    >
                      <SelectTrigger id="category">
                        <SelectValue placeholder="Sélectionner une catégorie" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Services">Services</SelectItem>
                        <SelectItem value="Matériel">Matériel</SelectItem>
                        <SelectItem value="Déplacement">Déplacement</SelectItem>
                        <SelectItem value="Hébergement">Hébergement</SelectItem>
                        <SelectItem value="Repas">Repas</SelectItem>
                        <SelectItem value="Formation">Formation</SelectItem>
                        <SelectItem value="Autres">Autres</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="paymentMethod">Méthode de Paiement</Label>
                    <Select 
                      name="paymentMethod"
                      value={newExpense.paymentMethod} 
                      onValueChange={(value) => handleSelectChange("paymentMethod", value)}
                    >
                      <SelectTrigger id="paymentMethod">
                        <SelectValue placeholder="Sélectionner un mode" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Carte">Carte Bancaire</SelectItem>
                        <SelectItem value="Virement">Virement</SelectItem>
                        <SelectItem value="Espèces">Espèces</SelectItem>
                        <SelectItem value="Chèque">Chèque</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="vendor">Fournisseur</Label>
                  <Input 
                    id="vendor" 
                    name="vendor"
                    value={newExpense.vendor}
                    onChange={handleInputChange}
                    placeholder="Nom du fournisseur"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="status">Statut</Label>
                  <Select 
                    name="status"
                    value={newExpense.status} 
                    onValueChange={(value) => handleSelectChange("status", value)}
                  >
                    <SelectTrigger id="status">
                      <SelectValue placeholder="Sélectionner un statut" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Pending">En attente</SelectItem>
                      <SelectItem value="Approved">Approuvée</SelectItem>
                      <SelectItem value="Rejected">Rejetée</SelectItem>
                      <SelectItem value="Paid">Payée</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="notes">Notes</Label>
                  <Textarea 
                    id="notes" 
                    name="notes"
                    value={newExpense.notes}
                    onChange={handleInputChange}
                    placeholder="Informations complémentaires"
                    rows={2}
                  />
                </div>
              </div>
              
              <div className="flex justify-end gap-2 mt-4">
                <Button variant="outline" onClick={() => {
                  setIsDialogOpen(false);
                  setEditingExpense(null);
                  setNewExpense({
                    description: "",
                    category: "Services",
                    amount: "",
                    date: new Date().toISOString().split('T')[0],
                    vendor: "",
                    paymentMethod: "Virement",
                    notes: "",
                    status: "Pending"
                  });
                }}>
                  Annuler
                </Button>
                <Button className="bg-[#F62D51] hover:bg-[#F62D51]/90" onClick={handleAddExpense}>
                  {editingExpense ? "Mettre à jour" : "Ajouter"}
                </Button>
              </div>
            </DialogContent>
          </Dialog>
          
          <Button onClick={handleSave} variant="outline" className="border-[#F62D51] text-[#F62D51]">
            <Save className="h-4 w-4 mr-2" />
            Sauvegarder
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Budget Total</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-[#1A202C]">{formatCurrency(budget.total)}</div>
            <div className="text-sm text-gray-500 mt-1">Montant alloué pour cette mission</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Dépenses</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-[#F62D51]">{formatCurrency(getTotalExpenses())}</div>
            <div className="text-sm text-gray-500 mt-1">{expenses.length} dépense(s) enregistrée(s)</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Budget Restant</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-green-600">{formatCurrency(budget.remaining)}</div>
            <div className="flex items-center mt-1">
              <div className="w-full bg-gray-200 rounded-full h-2.5 mr-2">
                <div 
                  className={`h-2.5 rounded-full ${budgetUsagePercentage > 90 ? 'bg-red-500' : 'bg-blue-500'}`}
                  style={{ width: `${budgetUsagePercentage}%` }}
                ></div>
              </div>
              <span className="text-xs text-gray-500">{budgetUsagePercentage}% utilisé</span>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Liste des Dépenses</CardTitle>
        </CardHeader>
        <CardContent>
          {expenses.length === 0 ? (
            <div className="text-center py-8 border rounded-md">
              <DollarSign className="h-12 w-12 mx-auto text-gray-300 mb-3" />
              <p className="text-gray-500">Aucune dépense enregistrée pour cette mission d'audit.</p>
              <p className="text-sm text-gray-400">Cliquez sur "Ajouter une Dépense" pour commencer.</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="cursor-pointer" onClick={() => handleSort('date')}>
                    <div className="flex items-center">
                      Date
                      {sortConfig.key === 'date' && (
                        <ArrowUpDown className="h-4 w-4 ml-1" />
                      )}
                    </div>
                  </TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Catégorie</TableHead>
                  <TableHead>Fournisseur</TableHead>
                  <TableHead>Statut</TableHead>
                  <TableHead className="cursor-pointer" onClick={() => handleSort('amount')}>
                    <div className="flex items-center">
                      Montant
                      {sortConfig.key === 'amount' && (
                        <ArrowUpDown className="h-4 w-4 ml-1" />
                      )}
                    </div>
                  </TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {sortedExpenses.map((expense) => (
                  <TableRow key={expense.id}>
                    <TableCell className="whitespace-nowrap">
                      {formatDate(expense.date)}
                    </TableCell>
                    <TableCell className="font-medium">
                      {expense.description}
                      {expense.notes && <div className="text-xs text-gray-500 mt-1">{expense.notes}</div>}
                    </TableCell>
                    <TableCell>{expense.category}</TableCell>
                    <TableCell>{expense.vendor || "-"}</TableCell>
                    <TableCell>{getStatusBadge(expense.status)}</TableCell>
                    <TableCell className="font-semibold">{formatCurrency(expense.amount)}</TableCell>
                    <TableCell>
                      <div className="flex justify-end space-x-2">
                        <Button 
                          variant="ghost" 
                          size="icon" 
                          onClick={() => handleEditExpense(expense)}
                          className="h-8 w-8 text-gray-500"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="icon" 
                          onClick={() => handleDeleteExpense(expense.id)}
                          className="h-8 w-8 text-red-500"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

export default DepensesTab; 