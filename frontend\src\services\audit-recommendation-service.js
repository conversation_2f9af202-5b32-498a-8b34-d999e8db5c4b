import axios from 'axios';
import { getApiEndpointUrl, getAuthHeaders } from '@/utils/api-config';

// Get recommendation by ID
export const getRecommendationById = async (id, signal) => {
  try {
    const response = await axios.get(
      getApiEndpointUrl(`audit-recommendations/${id}`),
      { 
        headers: getAuthHeaders(),
        signal 
      }
    );
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    throw error;
  }
};

// Get all recommendations
export const getAllRecommendations = async (signal) => {
  try {
    const response = await axios.get(
      getApiEndpointUrl('audit-recommendations'),
      { 
        headers: getAuthHeaders(),
        signal 
      }
    );
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    throw error;
  }
};

// Get recommendations by constat ID
export const getRecommendationsByConstatId = async (constatId, signal) => {
  try {
    const response = await axios.get(
      getApiEndpointUrl(`audit-recommendations/constat/${constatId}`),
      { 
        headers: getAuthHeaders(),
        signal 
      }
    );
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    throw error;
  }
};

// Create new recommendation
export const createRecommendation = async (recommendationData, signal) => {
  try {
    const response = await axios.post(
      getApiEndpointUrl('audit-recommendations'),
      recommendationData,
      { 
        headers: getAuthHeaders(),
        signal 
      }
    );
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    throw error;
  }
};

// Update recommendation
export const updateRecommendation = async (id, recommendationData, signal) => {
  try {
    const response = await axios.put(
      getApiEndpointUrl(`audit-recommendations/${id}`),
      recommendationData,
      { 
        headers: getAuthHeaders(),
        signal 
      }
    );
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    throw error;
  }
};

// Delete recommendation
export const deleteRecommendation = async (id, signal) => {
  try {
    const response = await axios.delete(
      getApiEndpointUrl(`audit-recommendations/${id}`),
      { 
        headers: getAuthHeaders(),
        signal 
      }
    );
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    throw error;
  }
};

// Link recommendation to constat
export const linkRecommendationToConstat = async (recommendationId, constatId, signal) => {
  try {
    const response = await axios.put(
      getApiEndpointUrl(`audit-recommendations/${recommendationId}/link-constat`),
      { linkToConstat: constatId },
      { 
        headers: getAuthHeaders(),
        signal 
      }
    );
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    throw error;
  }
};

// Unlink recommendation from constat
export const unlinkRecommendationFromConstat = async (recommendationId, constatId, signal) => {
  try {
    const response = await axios.put(
      getApiEndpointUrl(`audit-recommendations/${recommendationId}/unlink-constat`),
      { constatId },
      { 
        headers: getAuthHeaders(),
        signal 
      }
    );
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    throw error;
  }
}; 