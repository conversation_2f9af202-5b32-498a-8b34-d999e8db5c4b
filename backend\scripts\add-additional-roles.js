// backend/scripts/add-additional-roles.js
require('dotenv').config({ path: 'backend/.env' });
const { sequelize, Role } = require('../models');

async function ensureAllRoles() {
  console.log('Starting to ensure all 9 roles exist...');
  
  try {
    // Define all 9 roles
    const allApplicationRoles = [
      {
        name: 'GRC Administrator',
        description: 'Highest level of access, manages system settings and all GRC modules.',
        code: 'grc_admin' // Typically roleId: 1
      },
      {
        name: 'GRC Manager',
        description: 'Manages GRC processes, users, and configurations within assigned areas.',
        code: 'grc_manager' // Typically roleId: 2
      },
      {
        name: 'Risk Manager',
        description: 'Responsible for identifying, assessing, and mitigating risks.',
        code: 'risk_manager' // Typically roleId: 3
      },
      {
        name: 'GRC Contributor',
        description: 'Contributes to GRC processes, inputs data, and responds to tasks.',
        code: 'grc_contributor' // Typically roleId: 4
      },
      {
        name: 'Incident Manager',
        description: 'Manages incidents, incident response, and related processes.',
        code: 'incident_manager' // Typically roleId: (e.g., 5 or 6 if IDs are sequential)
      },
      {
        name: 'Internal Controller',
        description: 'Responsible for internal control design, implementation, and monitoring.',
        code: 'internal_controller'
      },
      {
        name: 'Compliance Manager',
        description: 'Manages compliance programs, policies, and regulatory requirements.',
        code: 'compliance_manager'
      },
      {
        name: 'Audit Director',
        description: 'Directs internal/external audit activities and oversees audit teams.',
        code: 'audit_director'
      },
      {
        name: 'Auditor',
        description: 'Conducts audits, tests controls, and reports findings.',
        code: 'auditor'
      }
    ];
    
    const transaction = await sequelize.transaction();
    
    try {
      for (const roleData of allApplicationRoles) {
        const [role, created] = await Role.findOrCreate({
          where: { code: roleData.code }, // Use code as the unique constraint for finding
          defaults: {
            name: roleData.name,
            description: roleData.description,
            // createdAt and updatedAt are handled by Sequelize by default if timestamps: true in model
          },
          transaction
        });
        
        if (created) {
          console.log(`Created role: ${role.name} (Code: ${role.code}, ID: ${role.id})`);
        } else {
          console.log(`Role already exists: ${role.name} (Code: ${role.code}, ID: ${role.id})`);
        }
      }
      
      await transaction.commit();
      console.log('All 9 roles processed successfully!');
    } catch (error) {
      await transaction.rollback();
      console.error('Error during role processing transaction:', error);
      throw error; // Re-throw to be caught by outer catch
    }
  } catch (error) {
    console.error('Error ensuring all roles:', error);
  } finally {
    await sequelize.close();
    console.log('Database connection closed.');
  }
}

ensureAllRoles()
  .then(() => {
    console.log('Role script completed.');
    process.exit(0);
  })
  .catch(error => {
    console.error('Role script failed overall:', error);
    process.exit(1);
  });
