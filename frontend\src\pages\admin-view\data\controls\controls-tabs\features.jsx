import React, { useState, useEffect, useCallback, useMemo } from "react";
import { useOutletContext, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { Loader2, Save, X, RotateCcw, Database, Gauge, FileText, Hash, Code, DollarSign, Building, Layers, Activity } from "lucide-react";
import { updateControl } from "@/store/slices/controlSlice";
import { getAllControlTypes } from "@/store/slices/controlTypeSlice";
import { getAllRisks } from "@/store/slices/riskSlice";
import { getAllBusinessProcesses } from "@/store/slices/businessProcessSlice";
import { getAllOrganizationalProcesses } from "@/store/slices/organizationalProcessSlice";
import { getAllOperations } from "@/store/slices/operationSlice";
import { getAllApplications } from "@/store/slices/applicationSlice";
import { getAllEntities } from "@/store/slices/entitySlice";
import { getAllActionPlans } from "@/store/slices/actionPlanSlice";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Combobox } from "@/components/ui/combobox";
import { toast } from "sonner";
import { useTranslation } from "react-i18next";

// Options for select fields
const organizationalLevelOptions = [
  { id: "Global", name: "Global" },
  { id: "Local", name: "Local" }
];
const sampleTypeOptions = [
  { id: "Command", name: "Command" },
  { id: "Bill", name: "Bill" },
  { id: "Contract", name: "Contract" }
];
const testingFrequencyOptions = [
  { id: "Quarterly", name: "Quarterly" },
  { id: "Bi-yearly", name: "Bi-yearly" },
  { id: "Yearly", name: "Yearly" }
];
const testingMethodOptions = [
  { id: "Observation", name: "Observation" },
  { id: "Inquiry", name: "Inquiry" },
  { id: "Inspection", name: "Inspection" },
  { id: "Re-performance", name: "Re-performance" }
];

// Memoized Input Field Component
const InputField = React.memo(({ label, name, value, onChange, disabled = false, icon }) => {
  return (
    <div className="mb-4">
      <label className="block text-gray-700 text-sm font-bold mb-2 flex items-center">
        {icon && <span className="mr-2 text-blue-500">{icon}</span>}
        {label}
      </label>
      <Input
        id={name}
        name={name}
        type="text"
        value={value || ""}
        onChange={onChange}
        disabled={disabled}
        placeholder={disabled ? "None" : ""}
      />
    </div>
  );
});

// Memoized Select Field Component
const SelectField = React.memo(
  ({
    label,
    name,
    value,
    onChange,
    options,
    isLoading = false,
    disabled = false,
    icon,
  }) => {
    const handleValueChange = (newValue) => {
      const event = {
        target: {
          name: name,
          value: newValue
        }
      };
      onChange(event);
    };

    // Convert string array options to the object format expected by the component
    const normalizedOptions = Array.isArray(options)
      ? options.map(opt => typeof opt === 'string' ? { id: opt, name: opt } : opt)
      : [];

    return (
      <div className="mb-4">
        <label className="block text-gray-700 text-sm font-bold mb-2 flex items-center">
          {icon && <span className="mr-2 text-blue-500">{icon}</span>}
          {label}
        </label>
        {isLoading ? (
          <div className="animate-pulse bg-gray-200 h-10 rounded"></div>
        ) : normalizedOptions && normalizedOptions.length > 0 ? (
          <Select
            value={value || "none"}
            onValueChange={handleValueChange}
            disabled={disabled}
          >
            <SelectTrigger>
              <SelectValue placeholder={`Select ${label}`} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem key={`none-option-${name}`} value="none">None</SelectItem>
              {normalizedOptions.map((option) => (
                <SelectItem
                  key={`${name}-${option.id}`}
                  value={option.id.toString()} // Ensure value is a string and not empty
                >
                  {option.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        ) : (
          <Input disabled placeholder="No options available" />
        )}
      </div>
    );
  }
);

// Memoized Textarea Component
const TextareaField = React.memo(({ label, name, value, onChange, disabled = false, icon }) => {
  return (
    <div className="mb-4">
      <label className="block text-gray-700 text-sm font-bold mb-2 flex items-center">
        {icon && <span className="mr-2 text-blue-500">{icon}</span>}
        {label}
      </label>
      <Textarea
        id={name}
        name={name}
        value={value || ""}
        onChange={onChange}
        disabled={disabled}
        rows="4"
        placeholder={disabled ? "None" : ""}
      />
    </div>
  );
});

function ControlFeatures() {
  const { control } = useOutletContext();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { t } = useTranslation();

  // Get state from Redux store with memoization to prevent unnecessary re-renders
  const { isLoading } = useSelector((state) => state.control);
  const controlTypes = useSelector((state) => state.controlType?.controlTypes || []);
  const risks = useSelector((state) => state.risk?.risks || []);
  const businessProcesses = useSelector((state) => state.businessProcess?.businessProcesses || []);
  const organizationalProcesses = useSelector((state) => state.organizationalProcess?.organizationalProcesses || []);
  const operations = useSelector((state) => state.operation?.operations || []);
  const applications = useSelector((state) => state.application?.applications || []);
  const entities = useSelector((state) => state.entity?.entities || []);
  const actionPlans = useSelector((state) => state.actionPlan?.actionPlans || []);

  // Loading states
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [dataLoaded, setDataLoaded] = useState(false);

  // Form data state
  const [formData, setFormData] = useState({
    name: "",
    code: "",
    controlKey: "",
    controlExecutionMethod: "",
    objective: "",
    executionProcedure: "",
    operationalCost: "",
    organizationalLevel: "Global",
    sampleType: "Command",
    testingFrequency: "Quarterly",
    testingMethod: "Observation",
    testingPopulationSize: "",
    testingProcedure: "",
    implementingActionPlan: "none",
    businessProcessID: "none",
    organizationalProcessID: "none",
    operationID: "none",
    applicationID: "none",
    entityID: "none",
    controlTypeID: "none",
    riskID: "none",
    comment: "",
  });

  // Load all reference data only once when component mounts
  useEffect(() => {
    let isMounted = true;

    const loadAllReferenceData = async () => {
      if (!isMounted) return;
      setIsLoadingData(true);

      try {
        // Batch reference data fetching to improve performance
        const fetchPromises = [];

        if (controlTypes.length === 0) fetchPromises.push(dispatch(getAllControlTypes()));
        if (risks.length === 0) fetchPromises.push(dispatch(getAllRisks()));
        if (businessProcesses.length === 0) fetchPromises.push(dispatch(getAllBusinessProcesses()));
        if (organizationalProcesses.length === 0) fetchPromises.push(dispatch(getAllOrganizationalProcesses()));
        if (operations.length === 0) fetchPromises.push(dispatch(getAllOperations()));
        if (applications.length === 0) fetchPromises.push(dispatch(getAllApplications()));
        if (entities.length === 0) fetchPromises.push(dispatch(getAllEntities()));
        if (actionPlans.length === 0) fetchPromises.push(dispatch(getAllActionPlans()));

        // Wait for all data to load in parallel
        if (fetchPromises.length > 0) {
          await Promise.all(fetchPromises);
        }

        if (isMounted) {
          setDataLoaded(true);
        }
      } catch (error) {
        console.error(t('admin.controls.features.error.reference_data', 'Error loading reference data:'), error);
        if (isMounted) {
          toast.error(t('admin.controls.features.error.reference_data_refresh', 'Failed to load some reference data. Please refresh the page.'));
        }
      } finally {
        if (isMounted) {
          setIsLoadingData(false);
        }
      }
    };

    loadAllReferenceData();

    // Cleanup function to prevent state updates after unmount
    return () => {
      isMounted = false;
    };
  }, [
    dispatch,
    controlTypes.length,
    risks.length,
    businessProcesses.length,
    organizationalProcesses.length,
    operations.length,
    applications.length,
    entities.length,
    actionPlans.length
  ]);

  // Set form data when control data is available and reference data is loaded
  useEffect(() => {
    if (control && dataLoaded) {
      // Set default values from control data
      const defaultOrganizationalLevel = control.organizationalLevel || "Global";
      const defaultSampleType = control.sampleType || "Command";
      const defaultTestingFrequency = control.testingFrequency || "Quarterly";
      const defaultTestingMethod = control.testingMethod || "Observation";

      setFormData({
        name: control.name || "",
        code: control.code || "",
        controlKey: control.controlKey !== null ? control.controlKey.toString() : "",
        controlExecutionMethod: control.controlExecutionMethod || "",
        objective: control.objective || "",
        executionProcedure: control.executionProcedure || "",
        operationalCost: control.operationalCost !== null ? control.operationalCost.toString() : "",
        organizationalLevel: defaultOrganizationalLevel,
        sampleType: defaultSampleType,
        testingFrequency: defaultTestingFrequency,
        testingMethod: defaultTestingMethod,
        testingPopulationSize: control.testingPopulationSize !== null ? control.testingPopulationSize.toString() : "",
        testingProcedure: control.testingProcedure || "",
        implementingActionPlan: control.implementingActionPlan ? control.implementingActionPlan.toString() : "none",
        businessProcessID: control.businessProcess ? control.businessProcess.toString() : "none",
        organizationalProcessID: control.organizationalProcess ? control.organizationalProcess.toString() : "none",
        operationID: control.operation ? control.operation.toString() : "none",
        applicationID: control.application ? control.application.toString() : "none",
        entityID: control.entity ? control.entity.toString() : "none",
        controlTypeID: control.controlType ? control.controlType.toString() : "none",
        riskID: control.risk ? control.risk.toString() : "none",
        comment: control.comment || "",
      });

      // Handle missing operations (add temporary items for display)
      if (control.operation && control.operationName) {
        const matchingOperation = operations.find(
          (op) => op.operationID.toString() === control.operation.toString()
        );

        if (!matchingOperation) {
          // Create a temporary operation for display purposes without mutating the array
          const tempOperations = [...operations];
          tempOperations.push({
              operationID: control.operation,
              name: `${control.operationName} (current value)`,
              isTemporary: true
          });

          // Update operations in Redux store
          dispatch({
            type: "operation/getAllOperations/fulfilled",
            payload: { success: true, data: tempOperations }
          });
        }
      }
    }
  }, [control, dataLoaded, operations, dispatch]);

  // Process options for select fields using memoization to prevent recalculation
  const selectOptions = useMemo(() => ({
    businessProcesses: [
      { id: "none", name: "None" },
      ...(businessProcesses.length > 0
        ? businessProcesses.map(process => ({ id: process.businessProcessID, name: process.name }))
        : [{ id: "no-options", name: "No business processes available", disabled: true }])
    ],
    organizationalProcesses: [
      { id: "none", name: "None" },
      ...(organizationalProcesses.length > 0
        ? organizationalProcesses.map(process => ({ id: process.organizationalProcessID, name: process.name }))
        : [{ id: "no-options", name: "No organizational processes available", disabled: true }])
    ],
    operations: [
      { id: "none", name: "None" },
      ...(operations.length > 0
        ? operations.map(op => ({
            id: op.operationID,
            name: op.name,
            isTemporary: op.isTemporary,
            disabled: op.isTemporary,
            className: op.isTemporary ? "text-yellow-500 font-semibold" : ""
          }))
        : [{ id: "no-options", name: "No operations available", disabled: true }])
    ],
    applications: [
      { id: "none", name: "None" },
      ...(applications.length > 0
        ? applications.map(app => ({ id: app.applicationID, name: app.name }))
        : [{ id: "no-options", name: "No applications available", disabled: true }])
    ],
    entities: [
      { id: "none", name: "None" },
      ...(entities.length > 0
        ? entities.map(entity => ({ id: entity.entityID, name: entity.name }))
        : [{ id: "no-options", name: "No entities available", disabled: true }])
    ],
    controlTypes: [
      { id: "none", name: "None" },
      ...(controlTypes.length > 0
        ? controlTypes.map(type => ({ id: type.controlTypeID, name: type.name }))
        : [{ id: "no-options", name: "No control types available", disabled: true }])
    ],
    risks: [
      { id: "none", name: "None" },
      ...(risks.length > 0
        ? risks.map(risk => ({ id: risk.riskID, name: risk.name }))
        : [{ id: "no-options", name: "No risks available", disabled: true }])
    ]
  }), [businessProcesses, organizationalProcesses, operations, applications, entities, controlTypes, risks]);

  // Event handlers with useCallback to prevent unnecessary re-renders
  const handleInputChange = useCallback((e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  }, []);

  const handleSelectChange = useCallback((name, value) => {
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  }, []);

  const handleGoBack = useCallback(() => {
    navigate("/admin/controls");
  }, [navigate]);

  const handleSubmit = useCallback(async (e) => {
    e.preventDefault();

    if (!formData.name) {
      toast.error(t('admin.controls.features.error.name_required', 'Name is required'));
      return;
    }

    setIsSubmitting(true);

    // Prepare control data for submission
    const controlData = {
      name: formData.name,
      code: formData.code,
      controlKey: formData.controlKey ? parseInt(formData.controlKey, 10) : null,
      controlExecutionMethod: formData.controlExecutionMethod || null,
      objective: formData.objective || null,
      executionProcedure: formData.executionProcedure || null,
      operationalCost: formData.operationalCost ? parseFloat(formData.operationalCost) : null,
      organizationalLevel: formData.organizationalLevel || null,
      sampleType: formData.sampleType || null,
      testingFrequency: formData.testingFrequency || null,
      testingMethod: formData.testingMethod || null,
      testingPopulationSize: formData.testingPopulationSize
        ? parseInt(formData.testingPopulationSize, 10)
        : null,
      testingProcedure: formData.testingProcedure || null,
      implementingActionPlan:
        formData.implementingActionPlan === "none" ? null : formData.implementingActionPlan || null,
      businessProcess: formData.businessProcessID === "none" ? null : formData.businessProcessID || null,
      organizationalProcess:
        formData.organizationalProcessID === "none" ? null : formData.organizationalProcessID || null,
      operation: formData.operationID === "none" ? null : formData.operationID,
      application: formData.applicationID === "none" ? null : formData.applicationID || null,
      entity: formData.entityID === "none" ? null : formData.entityID || null,
      controlType: formData.controlTypeID === "none" ? null : formData.controlTypeID || null,
      risk: formData.riskID === "none" ? null : formData.riskID || null,
      comment: formData.comment || null,
    };

    try {
      await dispatch(
        updateControl({
          id: control ? control.controlID : `CTL_${Date.now()}`,
          controlData,
        })
      ).unwrap();

      toast.success(control ? t('admin.controls.features.success.updated', 'Control updated successfully') : t('admin.controls.features.success.created', 'Control created successfully'));

      // Wait a moment before navigating to ensure the state is updated
      setTimeout(() => {
      if (control) {
          navigate(`/admin/controls/edit/${control.controlID}/overview`);
      } else {
          navigate("/admin/controls");
      }
      }, 200);
    } catch (error) {
      console.error(t('admin.controls.features.error.update', 'Error in control update:'), error);

      // Handle foreign key constraint errors
      if (error.message?.includes('foreign key constraint') ||
          error.message?.includes('does not exist')) {

        const errorMessage = error.response?.data?.message || error.message;
        const fieldMatch = errorMessage.match(/The ([a-zA-Z]+) with ID ([\w_]+) does not exist/i);

        if (fieldMatch && fieldMatch.length >= 3) {
          const fieldName = fieldMatch[1];
          const fieldValue = fieldMatch[2];

          toast.error(t('admin.controls.features.error.foreign_key', 'The {{fieldName}} with ID {{fieldValue}} does not exist in the database.', { fieldName, fieldValue }));

          // Special handling for operation field
          if (fieldName === 'operation') {
            setFormData(prev => ({ ...prev, operationID: 'none' }));

            // Try to resubmit with operation set to null
            try {
              await dispatch(
                updateControl({
                  id: control ? control.controlID : `CTL_${Date.now()}`,
                  controlData: { ...controlData, operation: null },
                })
              ).unwrap();

              toast.success(control ? t('admin.controls.features.success.updated', 'Control updated successfully') : t('admin.controls.features.success.created', 'Control created successfully'));

              setTimeout(() => {
              if (control) {
                  navigate(`/admin/controls/edit/${control.controlID}/overview`);
              } else {
                  navigate("/admin/controls");
              }
              }, 200);
            } catch (retryError) {
              console.error(t('admin.controls.features.error.retry', 'Error in retry:'), retryError);
              toast.error(t('admin.controls.features.error.retry_failed', 'Failed to save control even after retry'));
            }
          }
        } else {
          toast.error(t('admin.controls.features.error.foreign_key_constraint', 'Foreign key constraint error: One of the selected references does not exist.'));
        }
      } else {
        toast.error(error.message || t('admin.controls.features.error.save_failed', 'Failed to save control'));
      }
    } finally {
      setIsSubmitting(false);
    }
  }, [formData, control, navigate, dispatch]);

  // Show loading state
  if (isLoadingData) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <div className="flex flex-col items-center">
          <Loader2 className="h-12 w-12 animate-spin text-[#F62D51]" />
          <p className="mt-4 text-gray-600">Loading control data...</p>
        </div>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6 bg-white rounded-lg shadow-sm p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">{control ? t('admin.controls.features.title', 'Edit Control') : t('admin.controls.features.create_title', 'Create Control')}</h2>
        {control && (
          <div className="text-sm text-gray-500">
            {t('admin.controls.features.id', 'ID')}: {control.controlID}
          </div>
        )}
      </div>

      {/* Basic Information Section */}
      <div className="border rounded-lg shadow-sm">
        <div className="w-full flex items-center p-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-lg">
          <Database className="h-5 w-5 mr-2 text-blue-800" />
          <span className="text-lg font-medium text-blue-800">{t('admin.controls.features.sections.basic', 'Basic Information')}</span>
        </div>
        <div className="p-4">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <InputField
              label={t('admin.controls.features.form.name', 'Name')}
            name="name"
            value={formData.name}
            onChange={handleInputChange}
              disabled={false}
            />
            <InputField
              label={t('admin.controls.features.form.code', 'Code')}
            name="code"
            value={formData.code}
            onChange={handleInputChange}
              disabled={false}
            />
            <div className="mb-4">
              <label className="block text-gray-700 text-sm font-bold mb-2">
                {t('admin.controls.features.form.control_key', 'Control Key')}
              </label>
              <div className="flex items-center">
                <input
                  type="checkbox"
            id="controlKey"
                  checked={formData.controlKey === "1"}
                  onChange={(e) => {
                    handleSelectChange("controlKey", e.target.checked ? "1" : "0");
                  }}
                  className="h-4 w-4 border-gray-300 rounded text-blue-600 focus:ring-blue-500"
                />
                <label htmlFor="controlKey" className="ml-2 text-sm text-gray-600">
                  {formData.controlKey === "1" ? "Yes" : "No"}
                </label>
        </div>
            </div>
            <InputField
              label={t('admin.controls.features.form.control_execution_method', 'Control Execution Method')}
            name="controlExecutionMethod"
            value={formData.controlExecutionMethod}
            onChange={handleInputChange}
              disabled={false}
            />
            <InputField
              label={t('admin.controls.features.form.operational_cost', 'Operational Cost')}
            name="operationalCost"
            type="number"
            step="0.01"
            value={formData.operationalCost}
            onChange={handleInputChange}
              disabled={false}
            />
            <SelectField
              label={t('admin.controls.features.form.organizational_level', 'Organizational Level')}
              name="organizationalLevel"
            value={formData.organizationalLevel}
              onChange={handleSelectChange}
              options={organizationalLevelOptions}
              disabled={false}
            />
        </div>
        </div>
        </div>

      {/* Testing Details Section */}
      <div className="border rounded-lg shadow-sm">
        <div className="w-full flex items-center p-3 bg-gradient-to-r from-green-50 to-emerald-50 rounded-t-lg">
          <Gauge className="h-5 w-5 mr-2 text-green-800" />
          <span className="text-lg font-medium text-green-800">{t('admin.controls.features.sections.testing', 'Testing Details')}</span>
        </div>
        <div className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <SelectField
              label={t('admin.controls.features.form.sample_type', 'Sample Type')}
              name="sampleType"
              value={formData.sampleType}
              onChange={handleSelectChange}
              options={sampleTypeOptions}
              disabled={false}
            />
            <SelectField
              label={t('admin.controls.features.form.testing_frequency', 'Testing Frequency')}
              name="testingFrequency"
            value={formData.testingFrequency}
              onChange={handleSelectChange}
              options={testingFrequencyOptions}
              disabled={false}
            />
            <SelectField
              label={t('admin.controls.features.form.testing_method', 'Testing Method')}
              name="testingMethod"
            value={formData.testingMethod}
              onChange={handleSelectChange}
              options={testingMethodOptions}
              disabled={false}
            />
            <InputField
              label={t('admin.controls.features.form.testing_population_size', 'Testing Population Size')}
            name="testingPopulationSize"
            type="number"
            value={formData.testingPopulationSize}
              onChange={(e) => {
                const newValue = e.target.value === "" ? "" : parseInt(e.target.value, 10);
                handleInputChange({
                  target: {
                    name: "testingPopulationSize",
                    value: newValue
                  }
                });
              }}
              disabled={false}
              min="0"
              step="1"
            />
            <InputField
              label={t('admin.controls.features.form.testing_procedure', 'Testing Procedure')}
            name="testingProcedure"
            value={formData.testingProcedure}
            onChange={handleInputChange}
              disabled={false}
          />
        </div>
        </div>
      </div>

      {/* Reference Data Section */}
      <div className="border rounded-lg shadow-sm">
        <div className="w-full flex items-center p-3 bg-gradient-to-r from-amber-50 to-orange-50 rounded-t-lg">
          <Layers className="h-5 w-5 mr-2 text-amber-800" />
          <span className="text-lg font-medium text-amber-800">{t('admin.controls.features.sections.reference', 'Reference Data')}</span>
        </div>
        <div className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="space-y-2">
              <label className="block text-gray-700 text-sm font-bold mb-2 flex items-center">
                {t('admin.controls.overview.control_type', 'Control Type')}
              </label>
              <Combobox
                options={selectOptions.controlTypes.map(item => ({
                  value: item.id,
                  label: item.name,
                  key: `control-type-${item.id}`
                }))}
                value={formData.controlTypeID}
                onChange={(value) => handleSelectChange("controlTypeID", value)}
                placeholder={t('admin.controls.features.form.select_control_type', 'Select control type')}
                searchPlaceholder={t('admin.controls.features.form.search_control_types', 'Search control types...')}
                className="w-full"
              />
        </div>

        <div className="space-y-2">
              <label className="block text-gray-700 text-sm font-bold mb-2 flex items-center">
                {t('admin.controls.overview.business_process', 'Business Process')}
              </label>
              <Combobox
                options={selectOptions.businessProcesses.map(item => ({
                  value: item.id,
                  label: item.name,
                  key: `business-process-${item.id}`
                }))}
            value={formData.businessProcessID}
                onChange={(value) => handleSelectChange("businessProcessID", value)}
                placeholder={t('admin.controls.features.form.select_business_process', 'Select business process')}
                searchPlaceholder={t('admin.controls.features.form.search_business_processes', 'Search business processes...')}
                className="w-full"
              />
        </div>

        <div className="space-y-2">
              <label className="block text-gray-700 text-sm font-bold mb-2 flex items-center">
                {t('admin.controls.overview.organizational_process', 'Organizational Process')}
              </label>
              <Combobox
                options={selectOptions.organizationalProcesses.map(item => ({
                  value: item.id,
                  label: item.name,
                  key: `org-process-${item.id}`
                }))}
            value={formData.organizationalProcessID}
                onChange={(value) => handleSelectChange("organizationalProcessID", value)}
                placeholder={t('admin.controls.features.form.select_organizational_process', 'Select organizational process')}
                searchPlaceholder={t('admin.controls.features.form.search_organizational_processes', 'Search organizational processes...')}
                className="w-full"
              />
        </div>

        <div className="space-y-2">
              <label className="block text-gray-700 text-sm font-bold mb-2 flex items-center">
                {t('admin.controls.overview.operation', 'Operation')}
              </label>
              <Combobox
                options={selectOptions.operations.map(item => ({
                  value: item.id,
                  label: item.name,
                  key: `operation-${item.id}`
                }))}
                value={formData.operationID}
                onChange={(value) => handleSelectChange("operationID", value)}
                placeholder={t('admin.controls.features.form.select_operation', 'Select operation')}
                searchPlaceholder={t('admin.controls.features.form.search_operations', 'Search operations...')}
                className="w-full"
              />
        </div>

        <div className="space-y-2">
              <label className="block text-gray-700 text-sm font-bold mb-2 flex items-center">
                {t('admin.controls.overview.application', 'Application')}
              </label>
              <Combobox
                options={selectOptions.applications.map(item => ({
                  value: item.id,
                  label: item.name,
                  key: `application-${item.id}`
                }))}
            value={formData.applicationID}
                onChange={(value) => handleSelectChange("applicationID", value)}
                placeholder={t('admin.controls.features.form.select_application', 'Select application')}
                searchPlaceholder={t('admin.controls.features.form.search_applications', 'Search applications...')}
                className="w-full"
              />
        </div>

        <div className="space-y-2">
              <label className="block text-gray-700 text-sm font-bold mb-2 flex items-center">
                {t('admin.controls.overview.entity', 'Entity')}
              </label>
              <Combobox
                options={selectOptions.entities.map(item => ({
                  value: item.id,
                  label: item.name,
                  key: `entity-${item.id}`
                }))}
            value={formData.entityID}
                onChange={(value) => handleSelectChange("entityID", value)}
                placeholder={t('admin.controls.features.form.select_entity', 'Select entity')}
                searchPlaceholder={t('admin.controls.features.form.search_entities', 'Search entities...')}
                className="w-full"
              />
        </div>

        <div className="space-y-2">
              <label className="block text-gray-700 text-sm font-bold mb-2 flex items-center">
                {t('admin.controls.overview.associated_risk', 'Associated Risk')}
              </label>
              <Combobox
                options={selectOptions.risks.map(item => ({
                  value: item.id,
                  label: item.name,
                  key: `risk-${item.id}`
                }))}
                value={formData.riskID}
                onChange={(value) => handleSelectChange("riskID", value)}
                placeholder={t('admin.controls.features.form.select_risk', 'Select risk')}
                searchPlaceholder={t('admin.controls.features.form.search_risks', 'Search risks...')}
                className="w-full"
              />
        </div>
          </div>
        </div>
      </div>

      {/* Description Section */}
      <div className="border rounded-lg shadow-sm">
        <div className="w-full flex items-center p-3 bg-gradient-to-r from-purple-50 to-indigo-50 rounded-t-lg">
          <FileText className="h-5 w-5 mr-2 text-purple-800" />
          <span className="text-lg font-medium text-purple-800">{t('admin.controls.features.sections.descriptions', 'Descriptions')}</span>
        </div>
        <div className="p-4">
          <div className="grid grid-cols-1 gap-6">
        <div className="space-y-2">
              <Label htmlFor="objective" className="flex items-center gap-2">
                <FileText className="h-4 w-4 text-gray-500" />
                {t('admin.controls.features.form.objective', 'Objective')}
              </Label>
              <Textarea
                id="objective"
                name="objective"
                value={formData.objective || ""}
                onChange={handleInputChange}
                rows="4"
                disabled={false}
                className="resize-none w-full p-2 border rounded-md"
              />
        </div>
        <div className="space-y-2">
              <Label htmlFor="executionProcedure" className="flex items-center gap-2">
                <Activity className="h-4 w-4 text-gray-500" />
                {t('admin.controls.features.form.execution_procedure', 'Execution Procedure')}
              </Label>
              <Textarea
                id="executionProcedure"
                name="executionProcedure"
                value={formData.executionProcedure || ""}
                onChange={handleInputChange}
                rows="4"
                disabled={false}
                className="resize-none w-full p-2 border rounded-md"
              />
        </div>
        <div className="space-y-2">
              <Label htmlFor="comment" className="flex items-center gap-2">
                <FileText className="h-4 w-4 text-gray-500" />
                {t('admin.controls.features.form.comment', 'Comment')}
              </Label>
          <Textarea
            id="comment"
            name="comment"
                value={formData.comment || ""}
            onChange={handleInputChange}
                rows="4"
                disabled={false}
                className="resize-none w-full p-2 border rounded-md"
          />
            </div>
          </div>
        </div>
      </div>

      <div className="flex justify-end gap-4 mt-6">
        <Button type="button" variant="outline" onClick={handleGoBack} className="border-gray-300 hover:bg-gray-50">
          <X className="h-4 w-4 mr-2" />
          {t('common.buttons.cancel', 'Cancel')}
        </Button>
        <Button
          type="submit"
          className="bg-[#F62D51] hover:bg-red-700 text-white transition-colors"
          disabled={isSubmitting || isLoading}
        >
          {(isSubmitting || isLoading) ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {t('common.saving', 'Saving...')}
            </>
          ) : (
            <>
              <Save className="mr-2 h-4 w-4" />
          {control ? t('admin.controls.features.buttons.save', 'Save Changes') : t('admin.controls.features.buttons.create', 'Create Control')}
            </>
          )}
        </Button>
      </div>
    </form>
  );
}

export default React.memo(ControlFeatures);