import React, { useState, useContext } from 'react';

import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { Plus, FileText, Save, Edit, Trash2, Check, CircleAlert } from "lucide-react";
import { toast } from "sonner";
import { useMissionAuditContext } from '@/utils/context-helpers';
import { OutletContext } from '@/pages/audit-view/missions-audits/edit-missions-audits';

function RecommandationsTab(props) {
  const contextData = useMissionAuditContext();
  const missionAudit = props.missionAudit || contextData.missionAudit;
  const [recommendations, setRecommendations] = useState([]);
  
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingRecommendation, setEditingRecommendation] = useState(null);
  const [newRecommendation, setNewRecommendation] = useState({
    title: "",
    description: "",
    riskLevel: "Medium",
    priority: "Medium",
    responsibleDepartment: "",
    responsiblePerson: "",
    deadline: "",
    status: "Not Started",
    implementationNotes: "",
    isAccepted: false
  });

  // If no mission data is available yet
  if (!missionAudit || !missionAudit.id) {
    return (
      <div className="flex items-center justify-center h-48">
        <p className="text-gray-500">Chargement des recommandations...</p>
      </div>
    );
  }

  const handleSave = () => {
    toast.success("Recommandations sauvegardées avec succès");
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewRecommendation(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name, value) => {
    setNewRecommendation(prev => ({ ...prev, [name]: value }));
  };

  const handleCheckboxChange = (name, checked) => {
    setNewRecommendation(prev => ({ ...prev, [name]: checked }));
  };

  const handleAddRecommendation = () => {
    if (!newRecommendation.title || !newRecommendation.description) {
      toast.error("Veuillez remplir au moins le titre et la description");
      return;
    }

    if (editingRecommendation) {
      // Update existing recommendation
      setRecommendations(prev => prev.map(rec => 
        rec.id === editingRecommendation.id ? { ...newRecommendation, id: rec.id } : rec
      ));
      toast.success("Recommandation mise à jour avec succès");
    } else {
      // Add new recommendation
      setRecommendations(prev => [...prev, { ...newRecommendation, id: Date.now() }]);
      toast.success("Nouvelle recommandation ajoutée avec succès");
    }

    // Reset form and close dialog
    setNewRecommendation({
      title: "",
      description: "",
      riskLevel: "Medium",
      priority: "Medium",
      responsibleDepartment: "",
      responsiblePerson: "",
      deadline: "",
      status: "Not Started",
      implementationNotes: "",
      isAccepted: false
    });
    setEditingRecommendation(null);
    setIsDialogOpen(false);
  };

  const handleEditRecommendation = (recommendation) => {
    setEditingRecommendation(recommendation);
    setNewRecommendation(recommendation);
    setIsDialogOpen(true);
  };

  const handleDeleteRecommendation = (id) => {
    setRecommendations(prev => prev.filter(rec => rec.id !== id));
    toast.success("Recommandation supprimée avec succès");
  };

  const getLevelBadge = (level) => {
    switch (level) {
      case 'Low':
        return <Badge className="bg-green-100 text-green-800">Faible</Badge>;
      case 'Medium':
        return <Badge className="bg-yellow-100 text-yellow-800">Moyen</Badge>;
      case 'High':
        return <Badge className="bg-red-100 text-red-800">Élevé</Badge>;
      default:
        return <Badge variant="outline">-</Badge>;
    }
  };

  const getPriorityBadge = (priority) => {
    switch (priority) {
      case 'Low':
        return <Badge className="bg-blue-100 text-blue-800">Faible</Badge>;
      case 'Medium':
        return <Badge className="bg-yellow-100 text-yellow-800">Moyenne</Badge>;
      case 'High':
        return <Badge className="bg-red-100 text-red-800">Haute</Badge>;
      default:
        return <Badge variant="outline">-</Badge>;
    }
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case 'Completed':
        return <Badge className="bg-green-100 text-green-800">Terminé</Badge>;
      case 'In Progress':
        return <Badge className="bg-blue-100 text-blue-800">En cours</Badge>;
      case 'Planned':
        return <Badge className="bg-yellow-100 text-yellow-800">Planifié</Badge>;
      case 'Not Started':
        return <Badge className="bg-gray-100 text-gray-800">Non démarré</Badge>;
      default:
        return <Badge variant="outline">-</Badge>;
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return "-";
    return new Date(dateString).toLocaleDateString('fr-FR');
  };

  const getImplementationRate = () => {
    if (recommendations.length === 0) return 0;
    const completed = recommendations.filter(rec => rec.status === 'Completed').length;
    return Math.round((completed / recommendations.length) * 100);
  };

  const getAcceptanceRate = () => {
    if (recommendations.length === 0) return 0;
    const accepted = recommendations.filter(rec => rec.isAccepted).length;
    return Math.round((accepted / recommendations.length) * 100);
  };

  return (
    <div className="space-y-6 py-4">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-[#1A202C] flex items-center">
          <FileText className="h-6 w-6 mr-3 text-[#F62D51]" />
          Recommandations
        </h2>
        <div className="flex space-x-2">
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button className="bg-[#F62D51] hover:bg-[#F62D51]/90">
                <Plus className="h-4 w-4 mr-2" />
                Ajouter une Recommandation
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-xl">
              <DialogHeader>
                <DialogTitle>{editingRecommendation ? "Modifier la Recommandation" : "Ajouter une Nouvelle Recommandation"}</DialogTitle>
                <DialogDescription>
                  Complétez les informations ci-dessous pour {editingRecommendation ? "modifier" : "ajouter"} une recommandation.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Titre *</Label>
                  <Input 
                    id="title" 
                    name="title"
                    value={newRecommendation.title}
                    onChange={handleInputChange}
                    placeholder="Titre de la recommandation"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="description">Description *</Label>
                  <Textarea 
                    id="description" 
                    name="description"
                    value={newRecommendation.description}
                    onChange={handleInputChange}
                    placeholder="Description détaillée de la recommandation"
                    rows={3}
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="riskLevel">Niveau de Risque</Label>
                    <Select 
                      name="riskLevel"
                      value={newRecommendation.riskLevel} 
                      onValueChange={(value) => handleSelectChange("riskLevel", value)}
                    >
                      <SelectTrigger id="riskLevel">
                        <SelectValue placeholder="Niveau de risque" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Low">Faible</SelectItem>
                        <SelectItem value="Medium">Moyen</SelectItem>
                        <SelectItem value="High">Élevé</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="priority">Priorité</Label>
                    <Select 
                      name="priority"
                      value={newRecommendation.priority} 
                      onValueChange={(value) => handleSelectChange("priority", value)}
                    >
                      <SelectTrigger id="priority">
                        <SelectValue placeholder="Niveau de priorité" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Low">Faible</SelectItem>
                        <SelectItem value="Medium">Moyenne</SelectItem>
                        <SelectItem value="High">Haute</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="responsibleDepartment">Département Responsable</Label>
                    <Input 
                      id="responsibleDepartment" 
                      name="responsibleDepartment"
                      value={newRecommendation.responsibleDepartment}
                      onChange={handleInputChange}
                      placeholder="Ex: Finance, IT, RH"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="responsiblePerson">Personne Responsable</Label>
                    <Input 
                      id="responsiblePerson" 
                      name="responsiblePerson"
                      value={newRecommendation.responsiblePerson}
                      onChange={handleInputChange}
                      placeholder="Nom de la personne responsable"
                    />
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="deadline">Date Limite</Label>
                    <Input 
                      id="deadline" 
                      name="deadline"
                      type="date"
                      value={newRecommendation.deadline}
                      onChange={handleInputChange}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="status">Statut</Label>
                    <Select 
                      name="status"
                      value={newRecommendation.status} 
                      onValueChange={(value) => handleSelectChange("status", value)}
                    >
                      <SelectTrigger id="status">
                        <SelectValue placeholder="Statut de la recommandation" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Not Started">Non démarré</SelectItem>
                        <SelectItem value="Planned">Planifié</SelectItem>
                        <SelectItem value="In Progress">En cours</SelectItem>
                        <SelectItem value="Completed">Terminé</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="implementationNotes">Notes de Mise en Œuvre</Label>
                  <Textarea 
                    id="implementationNotes" 
                    name="implementationNotes"
                    value={newRecommendation.implementationNotes}
                    onChange={handleInputChange}
                    placeholder="Notes sur l'implémentation de cette recommandation"
                    rows={2}
                  />
                </div>
                
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="isAccepted" 
                    checked={newRecommendation.isAccepted}
                    onCheckedChange={(checked) => handleCheckboxChange("isAccepted", checked)}
                  />
                  <Label htmlFor="isAccepted">Recommandation acceptée par l'entité auditée</Label>
                </div>
              </div>
              
              <div className="flex justify-end gap-2 mt-4">
                <Button variant="outline" onClick={() => {
                  setIsDialogOpen(false);
                  setEditingRecommendation(null);
                  setNewRecommendation({
                    title: "",
                    description: "",
                    riskLevel: "Medium",
                    priority: "Medium",
                    responsibleDepartment: "",
                    responsiblePerson: "",
                    deadline: "",
                    status: "Not Started",
                    implementationNotes: "",
                    isAccepted: false
                  });
                }}>
                  Annuler
                </Button>
                <Button className="bg-[#F62D51] hover:bg-[#F62D51]/90" onClick={handleAddRecommendation}>
                  {editingRecommendation ? "Mettre à jour" : "Ajouter"}
                </Button>
              </div>
            </DialogContent>
          </Dialog>
          
          <Button onClick={handleSave} variant="outline" className="border-[#F62D51] text-[#F62D51]">
            <Save className="h-4 w-4 mr-2" />
            Sauvegarder
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg">Résumé des Recommandations</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
            <div className="bg-gray-50 p-3 rounded-md">
              <p className="text-sm text-gray-500">Total</p>
              <p className="text-2xl font-bold">{recommendations.length}</p>
            </div>
            <div className="bg-gray-50 p-3 rounded-md">
              <p className="text-sm text-gray-500">Priorité Haute</p>
              <p className="text-2xl font-bold text-red-600">
                {recommendations.filter(rec => rec.priority === 'High').length}
              </p>
            </div>
            <div className="bg-gray-50 p-3 rounded-md">
              <p className="text-sm text-gray-500">Taux d'Acceptation</p>
              <p className="text-2xl font-bold text-blue-600">
                {getAcceptanceRate()}%
              </p>
            </div>
            <div className="bg-gray-50 p-3 rounded-md">
              <p className="text-sm text-gray-500">Taux de Mise en Œuvre</p>
              <p className="text-2xl font-bold text-green-600">
                {getImplementationRate()}%
              </p>
            </div>
          </div>
          
          <div className="text-center py-8 border rounded-md">
            <CircleAlert className="h-12 w-12 mx-auto text-gray-300 mb-3" />
            <p className="text-gray-500">Aucune recommandation formulée pour cette mission d'audit.</p>
            <p className="text-sm text-gray-400">Cliquez sur "Ajouter une Recommandation" pour commencer.</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default RecommandationsTab;